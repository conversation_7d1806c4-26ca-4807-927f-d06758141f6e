package com.nsy.scm.constant.bd;

import com.nsy.api.scm.dto.domain.SelectModel;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum BdBrandCostFirstLevelCostEnum {

    MAIN_MARK(1, "主唛"),
    TAG(2, "吊牌"),
    PACKING_BAG(3, "包装袋");
    BdBrandCostFirstLevelCostEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    private final Integer value;
    private final String desc;

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static List<SelectModel> getSelectList() {
        return Arrays.stream(values()).map(v -> new SelectModel(v.getValue().toString(), v.getDesc())).collect(Collectors.toList());
    }

    public static String getDescByValue(Integer value) {
        for (BdBrandCostFirstLevelCostEnum c : BdBrandCostFirstLevelCostEnum.values()) {
            if (c.value.equals(value)) {
                return c.desc;
            }
        }
        return "";
    }
}
