package com.nsy.scm.enumstable.flow;

import com.google.common.collect.ImmutableList;
import com.nsy.api.scm.dto.domain.SelectModel;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 改包装方式任务状态枚举
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
public enum FlowTaskPurchaseProductPackageChangeStatusEnum {

    WAIT_SUBMIT(1, "待提交"),
    COMPLETED(100, "已完成"),
    CANCELED(0, "已取消"),
    UNKNOWN(-1, "未知状态");

    private final Integer code;
    private final String desc;

    FlowTaskPurchaseProductPackageChangeStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举
     */
    public static FlowTaskPurchaseProductPackageChangeStatusEnum getByCode(Integer code) {
        if (code == null) {
            return UNKNOWN;
        }
        return Arrays.stream(values())
                .filter(status -> Objects.equals(status.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }

    /**
     * 根据状态码获取描述
     *
     * @param code 状态码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        return getByCode(code).getDesc();
    }

    /**
     * 获取所有状态的下拉选项
     *
     * @return 下拉选项列表
     */
    public static List<SelectModel> getSelectList() {
        return Arrays.stream(values())
                .filter(status -> UNKNOWN != status)
                .map(status -> new SelectModel(status.getCode().toString(), status.getDesc()))
                .collect(Collectors.toList());
    }

    /**
     * 获取未完成状态列表
     *
     * @return 未完成状态码列表
     */
    public static List<Integer> getUnfinishedStatusList() {
        return ImmutableList.of(WAIT_SUBMIT.getCode());
    }

    /**
     * 获取已完成状态列表
     *
     * @return 已完成状态码列表
     */
    public static List<Integer> getFinishedStatusList() {
        return ImmutableList.of(COMPLETED.getCode(), CANCELED.getCode());
    }

    /**
     * 获取可取消状态列表
     *
     * @return 可取消状态码列表
     */
    public static List<Integer> getCancelableStatusList() {
        return ImmutableList.of(WAIT_SUBMIT.getCode());
    }

    /**
     * 判断是否可以取消
     *
     * @param statusCode 状态码
     * @return 是否可以取消
     */
    public static boolean isCancelable(Integer statusCode) {
        return CollectionUtils.isNotEmpty(getCancelableStatusList()) && getCancelableStatusList().contains(statusCode);
    }

    public static List<FlowTaskPurchaseProductPackageChangeStatusEnum> getStatusList() {
        return Arrays.asList(FlowTaskPurchaseProductPackageChangeStatusEnum.WAIT_SUBMIT, FlowTaskPurchaseProductPackageChangeStatusEnum.CANCELED, FlowTaskPurchaseProductPackageChangeStatusEnum.COMPLETED);
    }
}
