package com.nsy.scm.enumstable.price.rules;

/**
 * <AUTHOR>
 * @description 核价审核人配置类型
 * @since 2024/01/23
 */
public enum ConfirmPriceAuditTaskTypeEnum {

    CONFIRM_PRICE(1, "核价"),
    PURCHASE_PRICE(2, "采购价"),
    SALE_PRICE(3, "定价");

    private final Integer code;

    private final String desc;

    ConfirmPriceAuditTaskTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
