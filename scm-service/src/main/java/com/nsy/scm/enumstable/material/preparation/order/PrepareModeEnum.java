package com.nsy.scm.enumstable.material.preparation.order;

import com.google.common.collect.ImmutableList;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <h3>备料方式枚举类</h3>
 *
 * <AUTHOR>
 * @date 2023/11/24 17:48
 */
public enum PrepareModeEnum {

    ROLLING_PREPARE("滚动备料"),
    PERIOD_PREPARE("周期备料"),

    UNKNOWN("未知");

    private final String desc;

    public String getDesc() {
        return desc;
    }

    PrepareModeEnum(String desc) {
        this.desc = desc;
    }

    // 备料单导入单位校验
    public static final List<String> PREPARATION_IMPORT_VERIFICATION_LIST = ImmutableList.of(ROLLING_PREPARE.desc, PERIOD_PREPARE.desc);

    public static String getDescByName(String name) {
        return Arrays.stream(values())
                .filter(modeEnum -> Objects.equals(name, modeEnum.name()))
                .map(PrepareModeEnum::getDesc)
                .findFirst()
                .orElse(UNKNOWN.desc);
    }

    public static String getNameByDesc(String desc) {
        return Arrays.stream(values())
                .filter(modeEnum -> Objects.equals(desc, modeEnum.desc))
                .findFirst()
                .orElse(UNKNOWN)
                .name();
    }
}
