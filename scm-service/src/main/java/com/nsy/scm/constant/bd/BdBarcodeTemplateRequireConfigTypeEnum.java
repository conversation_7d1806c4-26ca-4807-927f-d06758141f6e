package com.nsy.scm.constant.bd;

import java.util.Arrays;
import java.util.Objects;

/**
 * 条码模版要求配置类型
 * <AUTHOR>
 * @since 2024-11-20
 */
public enum BdBarcodeTemplateRequireConfigTypeEnum {
    STORE(1, "店铺级"),
    PLATFORM(2, "平台级");

    private final int code;

    private final String desc;

    BdBarcodeTemplateRequireConfigTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BdBarcodeTemplateRequireConfigTypeEnum resolveByCode(Integer code) {
        return Arrays.stream(BdBarcodeTemplateRequireConfigTypeEnum.values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findAny().orElse(null);
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
