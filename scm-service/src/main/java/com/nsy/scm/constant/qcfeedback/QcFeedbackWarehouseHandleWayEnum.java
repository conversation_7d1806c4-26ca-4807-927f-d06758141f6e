package com.nsy.scm.constant.qcfeedback;

import com.nsy.api.scm.dto.domain.SelectModel;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum QcFeedbackWarehouseHandleWayEnum {
    RETURN_GOODS(0, "退货"),
    REPAIR_IN_WAREHOUSE(1, "在仓返修"),
    SCRAP(2, "报废"),
    OTHER(3, "其他");
    private final Integer code;

    private final String name;

    QcFeedbackWarehouseHandleWayEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(Integer code) {
        for (QcFeedbackWarehouseHandleWayEnum c : QcFeedbackWarehouseHandleWayEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.name;
            }
        }
        return "";
    }

    public static List<SelectModel> getSelectList() {
        return Arrays.stream(values()).map(v -> new SelectModel(v.getCode().toString(), v.getName())).collect(Collectors.toList());
    }

    public static List<Integer> getScrapAndRepairInWarehouse() {
        return Arrays.asList(REPAIR_IN_WAREHOUSE.getCode(), SCRAP.getCode());
    }

}
