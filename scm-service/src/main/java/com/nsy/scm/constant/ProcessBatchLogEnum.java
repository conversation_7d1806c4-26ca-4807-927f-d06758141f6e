package com.nsy.scm.constant;

import com.nsy.api.core.apicore.util.StringUtils;

/**
 * 波次单日志-用于展示进度等日志信息
 */
public enum ProcessBatchLogEnum {
    // ======= 波次日志开始（以最后一个sku的状态来记录值） =======
    SPACE_SHELVED("仓库上架", "仓库上架完成"),

    QC_COMPLETE("加工质检完成", "完成质检，修改加工结果数【%s】-->【%s】件，质检合格数【%s】，次品【%s】件"),

    PROCESS_COMPLETE("加工完成", "完成加工，预加工【%s】件，完成加工【%s】件"),

    PROCESSING("加工中", "定制加工中"),

    PROCESS_SORTED("加工分拣", "加工分拣完成，预分拣【%s】件，共分拣【%s】件，待加工【%s】件"),

    PROCESS_RECEIVE("加工收货", "加工收货完成，生成加工任务【%s】个任务"),

    PROCESS_PICKED("仓库拣货完成", "加工波次仓库拣货完成，预拣货【%s】件，共拣货【%s】件"),

    START_PICK("发起加工拣货", "发起仓库加工拣货出库"),

    CANCEL("取消加工波次单", "取消加工波次单"),

    CREATE("加工波次单生成", "加工波次单生成"),

    GENERATE_LACK_PROCESS_TASK("生成缺货加工任务", "生成缺货加工任务"),
    PROCESS_TASK_COMPLETE("加工任务完成", "加工任务完成"),
    PROCESS_TASK_QC_COMPLETE("加工质检完成", "加工质检完成"),
    PROCESS_TASK_QC_PRO_COMPLETE("质检确认完成", "质检确认完成");


    ProcessBatchLogEnum(String type, String content) {
        this.type = type;
        this.content = content;
    }

    // 记录类型
    private String type;
    // 中文描述
    private String content;

    public static ProcessBatchLogEnum getByType(String eventType) {
        if (!StringUtils.hasText(eventType)) {
            return null;
        }
        for (ProcessBatchLogEnum item : values()) {
            if (item.getType().equalsIgnoreCase(eventType)) {
                return item;
            }
        }
        return null;
    }

    public static ProcessBatchLogEnum getByName(String name) {
        if (!StringUtils.hasText(name)) {
            return null;
        }
        for (ProcessBatchLogEnum item : values()) {
            if (item.name().equalsIgnoreCase(name)) {
                return item;
            }
        }
        return null;
    }

    public String getType() {
        return type;
    }

    public String getContent() {
        return content;
    }


}
