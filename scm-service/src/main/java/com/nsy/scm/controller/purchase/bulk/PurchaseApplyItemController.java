package com.nsy.scm.controller.purchase.bulk;

import com.nsy.api.scm.dto.domain.purchase.bulk.apply.PurchaseApplyItemForPageDto;
import com.nsy.api.scm.dto.domain.purchase.bulk.apply.PurchaseApplyItemForProductDto;
import com.nsy.api.scm.dto.request.purchase.bulk.PurchaseApplyItemPageListRequest;
import com.nsy.api.scm.dto.request.purchase.bulk.apply.PurchaseApplyItemStoreSellerSkuRequest;
import com.nsy.api.scm.dto.request.purchase.bulk.apply.PurchaseApplySharingInTransitDoAllocateRequest;
import com.nsy.api.scm.dto.request.purchase.bulk.apply.UpdateSellerSkuRequest;
import com.nsy.api.scm.dto.response.common.PageResponse;
import com.nsy.api.scm.dto.response.purchase.bulk.PurchaseApplyItemSellerSkuResponse;
import com.nsy.api.scm.dto.response.purchase.bulk.PurchaseApplyItemSummaryInfoResponse;
import com.nsy.api.scm.dto.response.purchase.bulk.UpdateApplyItemBarcodeInfoResponse;
import com.nsy.api.scm.feign.PurchaseApplyItemFeignClient;
import com.nsy.scm.business.service.purchase.bulk.PurchaseApplyItemService;
import com.nsy.scm.repository.entity.purchase.bulk.PurchaseApplyItemEntity;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 申请单明细 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
@Api(tags = "申请单明细接口")
@RestController
public class PurchaseApplyItemController implements PurchaseApplyItemFeignClient {
    @Autowired
    private PurchaseApplyItemService purchaseApplyItemService;

    @Override
    public PageResponse<PurchaseApplyItemForPageDto> applyItemPageByList(PurchaseApplyItemPageListRequest request) {
        return purchaseApplyItemService.applyItemPageByList(request, false);
    }

    @Override
    public PurchaseApplyItemSummaryInfoResponse summary(PurchaseApplyItemPageListRequest request) {
        return purchaseApplyItemService.summaryApplyItem(request);
    }

    @Override
    public List<PurchaseApplyItemForProductDto> getCompanyFirstOrderItems(Integer productId) {
        return purchaseApplyItemService.getCompanyFirstOrderItems(productId);
    }

    @Override
    public List<PurchaseApplyItemSellerSkuResponse> getSellerSkuItems(List<Integer> purchaseApplyItemIds) {
        return purchaseApplyItemService.getSellerSkuItems(purchaseApplyItemIds);
    }

    @Override
    public UpdateApplyItemBarcodeInfoResponse updateSellerSkuChangeStore(PurchaseApplyItemStoreSellerSkuRequest request) {
        return purchaseApplyItemService.updateSellerSkuChangeStore(request);
    }

    @Override
    public void updateSellerSku(UpdateSellerSkuRequest request) {
        purchaseApplyItemService.updateSellerSku(request);
    }

    @Override
    public void allocateSharingInTransit(PurchaseApplySharingInTransitDoAllocateRequest request) {

    }

    @Override
    public Map<String, Integer> getSkcWaitOrderSumMap(List<String> skcList) {
        return purchaseApplyItemService.getSkcWaitOrderSumMap(skcList);
    }

    @Override
    public List<String> queryUnCompleteApplyBySkcList(List<String> skcList) {
        return purchaseApplyItemService.queryUnCompleteApplyBySkcList(skcList).stream().map(PurchaseApplyItemEntity::getSkc).distinct().collect(Collectors.toList());
    }
}
