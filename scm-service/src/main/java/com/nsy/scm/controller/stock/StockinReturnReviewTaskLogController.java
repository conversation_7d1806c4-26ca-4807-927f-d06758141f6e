package com.nsy.scm.controller.stock;

import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.scm.business.request.stock.StockinReturnReviewTaskLogPageRequest;
import com.nsy.scm.business.request.stock.StockinReturnReviewTaskLogResponse;
import com.nsy.scm.business.service.stock.impl.StockinReturnReviewTaskLogService;
import com.nsy.scm.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 退货审核任务日志表(StockinReturnReviewTaskLog)接口
 *
 * <AUTHOR>
 * @since 2025-03-10 10:16:49
 */
@Api(tags = "退货审核任务日志表(StockinReturnReviewTaskLog)相关接口")
@RestController
public class StockinReturnReviewTaskLogController extends BaseController {

    @Resource
    private StockinReturnReviewTaskLogService stockinReturnReviewTaskLogService;

    /**
     * 分页查询
     */
    @ApiOperation("首页page列表")
    @PostMapping("/stockin-return-review-task-log/page")
    public PageResponse<StockinReturnReviewTaskLogResponse> queryByPage(@RequestBody StockinReturnReviewTaskLogPageRequest pageRequest) {
        return this.stockinReturnReviewTaskLogService.queryByPage(pageRequest);
    }


}

