package com.nsy.scm.controller.replenishment;

import com.nsy.api.scm.dto.response.common.PageResponse;
import com.nsy.scm.business.request.replenishment.PurchaseReplenishmentOperationLogPageRequest;
import com.nsy.scm.business.response.replenishment.PurchaseReplenishmentOperationLogPageResponse;
import com.nsy.scm.business.service.replenishment.PurchaseReplenishmentOperationLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 采购补货操作日志Controller
 * <AUTHOR>
 * @since 1.0 2023-02-01
 */
@RestController
@Api(tags = "采购补货操作日志")
@RequestMapping("/purchase-replenishment-operation-log")
public class PurchaseReplenishmentOperationLogController {

    @Autowired
    private PurchaseReplenishmentOperationLogService purchaseReplenishmentOperationLogService;

    @ApiOperation(value = "补货店铺日志分页接口", notes = "补货店铺日志分页接口", produces = "application/json")
    @PostMapping("/store/page")
    public PageResponse<PurchaseReplenishmentOperationLogPageResponse> storePageQuery(@RequestBody PurchaseReplenishmentOperationLogPageRequest request) {
        request.setIsStore(1);
        return purchaseReplenishmentOperationLogService.pageQuery(request);
    }

    @ApiOperation(value = "补货部门日志分页接口", notes = "补货部门日志分页接口", produces = "application/json")
    @PostMapping("/dept/page")
    public PageResponse<PurchaseReplenishmentOperationLogPageResponse> deptPageQuery(@RequestBody PurchaseReplenishmentOperationLogPageRequest request) {
        request.setIsDept(1);
        return purchaseReplenishmentOperationLogService.pageQuery(request);
    }

}
