package com.nsy.scm.controller.report;

import com.nsy.scm.business.manage.bi.ReportMaterialSupplierColorCardProductBindMappingApiService;
import com.nsy.api.scm.dto.domain.bi.ReportMaterialSupplierColorCardProductBindMappingDto;
import com.nsy.api.scm.dto.request.bi.ReportMaterialSupplierColorCardProductBindMappingRequest;
import com.nsy.api.scm.dto.response.common.PageResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Api(tags = "面料供应商色卡绑定商品映射接口")
@RestController
public class ReportMaterialSupplierColorCardProductBindMappingController {

    @Autowired
    private ReportMaterialSupplierColorCardProductBindMappingApiService reportMaterialSupplierColorCardProductBindMappingApiService;


    @ApiOperation(value = "面料供应商色卡绑定商品映射接口统计列表", notes = "面料供应商色卡绑定商品映射接口统计列表", produces = "application/json")
    @PostMapping("/report-material-supplier-color-card-bind-mapping/page")
    public PageResponse<ReportMaterialSupplierColorCardProductBindMappingDto> page(@Valid @RequestBody ReportMaterialSupplierColorCardProductBindMappingRequest request) {
        return reportMaterialSupplierColorCardProductBindMappingApiService.page(request);
    }
}
