package com.nsy.scm.common.interceptor;

import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * supplierDto缓存数据拦截器
 * 当supplierEntity数据发生变化后，清空缓存数据
 *
 * <AUTHOR>
 * @since 2022-07-22 14:15
 */
@Aspect
@Component
public class SupplierDtoCacheInterceptor {

    private static final Logger LOGGER = LoggerFactory.getLogger(SupplierDtoCacheInterceptor.class);

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${lock.key.prefix}")
    private String lockKeyPrefix;

    @Pointcut("@annotation(com.nsy.scm.common.annotation.CleanSupplierCache)")
    public void cleanSupplierCache() {
    }

    @Around("cleanSupplierCache()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object o = joinPoint.proceed();
        try {
            String cacheKey = String.format("%s:%s*", lockKeyPrefix, "SupplierDtoList");
            Set<String> keys = redisTemplate.keys(cacheKey);
            if (CollectionUtils.isNotEmpty(keys)) {
                redisTemplate.unlink(keys);
            }
        } catch (Exception e) {
            LOGGER.error("删除supplierDto缓存失败，失败原因：{}", e.getMessage());
        }
        return o;
    }
}
