package com.nsy.scm.business.manage.businessbase;

import com.alibaba.excel.util.CollectionUtils;
import com.nsy.scm.business.manage.businessbase.request.AllProcessSaveRecordRequest;
import com.nsy.scm.business.manage.businessbase.request.AllProcessStatusChangeConsumerRecordDto;
import com.nsy.scm.business.manage.businessbase.request.WcRuTaskCompleteRequest;
import com.nsy.scm.business.manage.businessbase.request.WcRuTaskCreateRequest;
import com.nsy.scm.business.manage.businessbase.request.WcRuTaskUpdateRequest;
import com.nsy.scm.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.util.List;

@Service
public class BusinessBaseApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BusinessBaseApiService.class);
    @Inject
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.business-base}")
    private String businessBaseServiceUrl;

    public void createWcRuTask(WcRuTaskCreateRequest request) {
        LOGGER.info("createWcRuTask request: {}", JsonMapper.toJson(request));
        String uri = String.format("%s/wc-ru-task/sync", businessBaseServiceUrl);
        this.restTemplate.postForEntity(uri, request, String.class);
    }

    public void updateWcRuTask(WcRuTaskUpdateRequest request) {
        LOGGER.info("updateWcRuTask request: {}", JsonMapper.toJson(request));
        String uri = String.format("%s/wc-ru-task/update", businessBaseServiceUrl);
        this.restTemplate.postForEntity(uri, request, String.class);
    }

    public void completeWcRuTask(WcRuTaskCompleteRequest request) {
        LOGGER.info("completeWcRuTask request: {}", JsonMapper.toJson(request));
        String uri = String.format("%s/wc-ru-task/complete", businessBaseServiceUrl);
        this.restTemplate.postForEntity(uri, request, String.class);
    }


    public void saveProcessStatusChangeRecord(List<AllProcessStatusChangeConsumerRecordDto> recordDtoList) {
        if (CollectionUtils.isEmpty(recordDtoList)) {
            return;
        }
        AllProcessSaveRecordRequest request = new AllProcessSaveRecordRequest();
        request.setRecordDtoList(recordDtoList);
        try {
            String uri = String.format("%s/all-process-consumer-record/save", businessBaseServiceUrl);
            this.restTemplate.postForLocation(uri, request);
        } catch (Exception e) {
            LOGGER.error("saveProcessStatusChangeRecord error: {}", e.getMessage());
            LOGGER.error(e.getMessage(), e);
        }
    }
}
