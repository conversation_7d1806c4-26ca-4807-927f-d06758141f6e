package com.nsy.scm.business.manage.peer.product.response;

import java.util.List;
/**
 * <AUTHOR>
 * @date 2022-06-29
 */
public class BaseListResponse<T> {
    private long totalCount;
    private List<T> content;
    private long currentPage;
    private long totalPage;
    private int pageNum;
    private int pageSize;


    public static <T> BaseListResponse<T> of(Long totalCount) {
        BaseListResponse<T> pageResponse = new BaseListResponse<>();
        pageResponse.setTotalCount(totalCount);
        return pageResponse;
    }

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public List<T> getContent() {
        return content;
    }

    public void setContent(List<T> content) {
        this.content = content;
    }

    public long getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(long currentPage) {
        this.currentPage = currentPage;
    }

    public long getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(long totalPage) {
        this.totalPage = totalPage;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
