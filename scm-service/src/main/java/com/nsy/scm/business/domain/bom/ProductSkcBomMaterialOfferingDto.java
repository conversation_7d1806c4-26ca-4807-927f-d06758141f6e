package com.nsy.scm.business.domain.bom;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.scm.repository.entity.bom.ProductSkcBomMaterialOfferingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * skc最新报价明细表
 *
 * <AUTHOR>
 */
@ApiModel(value = "ProductSkcBomMaterialOfferingDto", description = "skc最新报价明细表")
public class ProductSkcBomMaterialOfferingDto {
    @ApiModelProperty(value = "主键", name = "productSkcBomMaterialOfferingId")
    private Integer productSkcBomMaterialOfferingId;

    @ApiModelProperty(value = "skc", name = "skc")
    private String skc;

    @ApiModelProperty(value = "面料类型：material面料；side_material辅料", name = "materialType")
    private String materialType;

    @ApiModelProperty(value = "面料类型中文：FABRIC面料；SIDE_FABRIC辅料；AUXILIARY_FABRIC辅面料", name = "materialTypeCh")
    private String materialTypeCh;

    @ApiModelProperty(value = "面料类型排序", name = "materialTypeOrder")
    private Integer materialTypeOrder;

    @ApiModelProperty(value = "面料价格展示字段", name = "materialColorCardPriceType")
    private String materialColorCardPriceType;

    @ApiModelProperty(value = "图片url", name = "imgUrl")
    private String imgUrl;

    @ApiModelProperty(value = "商品id", name = "productId")
    private Integer productId;

    @ApiModelProperty(value = "供应商面料id", name = "materialSupplierInfoId")
    private Integer materialSupplierInfoId;

    @ApiModelProperty(value = "供应商面料名称", name = "supplierMaterialName")
    private String supplierMaterialName;

    @ApiModelProperty(value = "供应商名称", name = "supplierName")
    private String supplierName;

    @ApiModelProperty(value = "供应商简称", name = "supplierShortName")
    private String supplierShortName;

    @ApiModelProperty(value = "花型颜色", name = "colorDesc")
    private String colorDesc;

    @ApiModelProperty(value = "面料商色号/花型号", name = "colorNumber")
    private String colorNumber;

    @ApiModelProperty(value = "花型风格", name = "floralStyleDesc")
    private String floralStyleDesc;

    @ApiModelProperty("测缩方式，字典对应的中文")
    private String measureShrinkModeDesc;
    @ApiModelProperty("横缩率（%）")
    private BigDecimal transverseShrinkRate;
    @ApiModelProperty("直缩率（%）")
    private BigDecimal directShrinkRate;

    @ApiModelProperty(value = "空差", name = "emptyDifferenceDesc")
    private BigDecimal emptyDifferenceDesc;

    @ApiModelProperty(value = "尺寸，单位 cm", name = "size")
    private BigDecimal size;

    @ApiModelProperty("标识，字典对应的中文")
    private String identificationDesc;

    @ApiModelProperty("标识")
    private String identification;

    @ApiModelProperty(value = "克重(克)", name = "grammage")
    private BigDecimal grammage;

    @ApiModelProperty(value = "净幅宽", name = "netBreadth")
    private BigDecimal netBreadth;

    @ApiModelProperty(value = "毛幅宽", name = "grossBreadth")
    private BigDecimal grossBreadth;

    @ApiModelProperty(value = "成分", name = "ingredientDesc")
    private String ingredientDesc;

    @ApiModelProperty(value = "价格", name = "price")
    private BigDecimal price;

    @ApiModelProperty(value = "公斤价", name = "unitPrice")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "价格单位", name = "priceUnitDesc")
    private String priceUnitDesc;

    @ApiModelProperty(value = "skc尺码段面料报价明细", name = "productSkcBomSizeRangeMaterialOfferingDtoList")
    private List<ProductSkcBomSizeRangeMaterialOfferingDto> productSkcBomSizeRangeMaterialOfferingDtoList;

    @ApiModelProperty(value = "面料使用部位", name = "usePartList")
    private List<String> usePartList;

    @ApiModelProperty(value = "排料图", name = "productSkcBomAttachDtos")
    private List<ProductSkcBomAttachDto> productSkcBomAttachDtos;

    @ApiModelProperty(value = "面料id", name = "materialId")
    private Integer materialId;
    @ApiModelProperty(value = "联系方式姓名", name = "contactsName")
    private String contactsName;
    @ApiModelProperty(value = "联系方式电话", name = "contactsPhone")
    private String contactsPhone;

    @ApiModelProperty(value = "色卡id", name = "colorCardId")
    private Integer colorCardId;

    @ApiModelProperty(value = "色卡编号", name = "colorCardNo")
    private String colorCardNo;

    @ApiModelProperty(value = "BOM主键id", name = "productBomId")
    private Integer productBomId;

    @ApiModelProperty(value = "面料供应商合作状态中文", name = "supplierStatusDesc")
    private String supplierStatusDesc;
    @ApiModelProperty(value = "面料状态中文", name = "materialStatusDesc")
    private String materialStatusDesc;
    @ApiModelProperty(value = "色卡状态中文", name = "colorCardStatusDesc")
    private String colorCardStatusDesc;

    @ApiModelProperty(value = "单价-实时的，之前的单价字段才是冗余的", name = "currentUnitPrice")
    private BigDecimal currentUnitPrice;
    @ApiModelProperty(value = "克重(克)-冗余的", name = "originalGrammage")
    private BigDecimal originalGrammage;
    @ApiModelProperty(value = "净幅宽-冗余的", name = "originalNetBreadth")
    private BigDecimal originalNetBreadth;
    @ApiModelProperty(value = "毛幅宽-冗余的", name = "originalGrossBreadth")
    private BigDecimal originalGrossBreadth;
    @ApiModelProperty(value = "横缩率（%）-冗余的", name = "originalTransverseShrinkRate")
    private BigDecimal originalTransverseShrinkRate;
    @ApiModelProperty(value = "直缩率（%）-冗余的", name = "originalDirectShrinkRate")
    private BigDecimal originalDirectShrinkRate;

    @ApiModelProperty(value = "风险（关键字）数据字典：scm_material_supplier_floral_risk", name = "riskDescList")
    private List<String> riskDescList;

    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    @ApiModelProperty(value = "是否集采", name = "isMaterialOrder")
    private Integer isMaterialOrder;
    @ApiModelProperty(value = "物料跟单员", name = "fabricMerchandiserName")
    private String fabricMerchandiserName;
    @ApiModelProperty(value = "计划到料日期", name = "estimateSubmitDate")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date estimateSubmitDate;

    public void hidePrice() {
        this.price = BigDecimal.ZERO;
        this.unitPrice = BigDecimal.ZERO;
        this.currentUnitPrice = BigDecimal.ZERO;
        this.priceUnitDesc = "";
        if (CollectionUtils.isNotEmpty(this.productSkcBomSizeRangeMaterialOfferingDtoList)) {
            this.productSkcBomSizeRangeMaterialOfferingDtoList.forEach(ProductSkcBomSizeRangeMaterialOfferingDto::hidePrice);
        }
    }

    public void handOtherFields(ProductSkcBomMaterialOfferingEntity entity) {
        this.originalGrammage = entity.getGrammage();
        this.originalNetBreadth = entity.getNetBreadth();
        this.originalGrossBreadth = entity.getGrossBreadth();
        this.originalTransverseShrinkRate = entity.getTransverseShrinkRate();
        this.originalDirectShrinkRate = entity.getDirectShrinkRate();
    }

    public Integer getProductSkcBomMaterialOfferingId() {
        return productSkcBomMaterialOfferingId;
    }

    public void setProductSkcBomMaterialOfferingId(Integer productSkcBomMaterialOfferingId) {
        this.productSkcBomMaterialOfferingId = productSkcBomMaterialOfferingId;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }

    public String getSupplierMaterialName() {
        return supplierMaterialName;
    }

    public void setSupplierMaterialName(String supplierMaterialName) {
        this.supplierMaterialName = supplierMaterialName;
    }

    public String getSupplierShortName() {
        return supplierShortName;
    }

    public void setSupplierShortName(String supplierShortName) {
        this.supplierShortName = supplierShortName;
    }

    public BigDecimal getGrammage() {
        return grammage;
    }

    public void setGrammage(BigDecimal grammage) {
        this.grammage = grammage;
    }

    public BigDecimal getNetBreadth() {
        return netBreadth;
    }

    public void setNetBreadth(BigDecimal netBreadth) {
        this.netBreadth = netBreadth;
    }

    public BigDecimal getGrossBreadth() {
        return grossBreadth;
    }

    public void setGrossBreadth(BigDecimal grossBreadth) {
        this.grossBreadth = grossBreadth;
    }

    public String getIngredientDesc() {
        return ingredientDesc;
    }

    public void setIngredientDesc(String ingredientDesc) {
        this.ingredientDesc = ingredientDesc;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getColorDesc() {
        return colorDesc;
    }

    public void setColorDesc(String colorDesc) {
        this.colorDesc = colorDesc;
    }

    public String getColorNumber() {
        return colorNumber;
    }

    public void setColorNumber(String colorNumber) {
        this.colorNumber = colorNumber;
    }

    public List<ProductSkcBomAttachDto> getProductSkcBomAttachDtos() {
        return productSkcBomAttachDtos;
    }

    public void setProductSkcBomAttachDtos(List<ProductSkcBomAttachDto> productSkcBomAttachDtos) {
        this.productSkcBomAttachDtos = productSkcBomAttachDtos;
    }

    public String getMaterialTypeCh() {
        return materialTypeCh;
    }

    public void setMaterialTypeCh(String materialTypeCh) {
        this.materialTypeCh = materialTypeCh;
    }

    public Integer getMaterialTypeOrder() {
        return materialTypeOrder;
    }

    public void setMaterialTypeOrder(Integer materialTypeOrder) {
        this.materialTypeOrder = materialTypeOrder;
    }

    public Integer getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Integer materialId) {
        this.materialId = materialId;
    }

    public String getContactsName() {
        return contactsName;
    }

    public void setContactsName(String contactsName) {
        this.contactsName = contactsName;
    }

    public String getContactsPhone() {
        return contactsPhone;
    }

    public void setContactsPhone(String contactsPhone) {
        this.contactsPhone = contactsPhone;
    }

    public String getMeasureShrinkModeDesc() {
        return measureShrinkModeDesc;
    }

    public void setMeasureShrinkModeDesc(String measureShrinkModeDesc) {
        this.measureShrinkModeDesc = measureShrinkModeDesc;
    }

    public BigDecimal getTransverseShrinkRate() {
        return transverseShrinkRate;
    }

    public void setTransverseShrinkRate(BigDecimal transverseShrinkRate) {
        this.transverseShrinkRate = transverseShrinkRate;
    }

    public BigDecimal getDirectShrinkRate() {
        return directShrinkRate;
    }

    public void setDirectShrinkRate(BigDecimal directShrinkRate) {
        this.directShrinkRate = directShrinkRate;
    }

    public BigDecimal getEmptyDifferenceDesc() {
        return emptyDifferenceDesc;
    }

    public void setEmptyDifferenceDesc(BigDecimal emptyDifferenceDesc) {
        this.emptyDifferenceDesc = emptyDifferenceDesc;
    }

    public Integer getMaterialSupplierInfoId() {
        return materialSupplierInfoId;
    }

    public void setMaterialSupplierInfoId(Integer materialSupplierInfoId) {
        this.materialSupplierInfoId = materialSupplierInfoId;
    }

    public Integer getColorCardId() {
        return colorCardId;
    }

    public void setColorCardId(Integer colorCardId) {
        this.colorCardId = colorCardId;
    }

    public String getColorCardNo() {
        return colorCardNo;
    }

    public void setColorCardNo(String colorCardNo) {
        this.colorCardNo = colorCardNo;
    }

    public Integer getProductBomId() {
        return productBomId;
    }

    public void setProductBomId(Integer productBomId) {
        this.productBomId = productBomId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public BigDecimal getSize() {
        return size;
    }

    public void setSize(BigDecimal size) {
        this.size = size;
    }

    public String getIdentificationDesc() {
        return identificationDesc;
    }

    public void setIdentificationDesc(String identificationDesc) {
        this.identificationDesc = identificationDesc;
    }

    public String getIdentification() {
        return identification;
    }

    public void setIdentification(String identification) {
        this.identification = identification;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getPriceUnitDesc() {
        return priceUnitDesc;
    }

    public void setPriceUnitDesc(String priceUnitDesc) {
        this.priceUnitDesc = priceUnitDesc;
    }

    public List<ProductSkcBomSizeRangeMaterialOfferingDto> getProductSkcBomSizeRangeMaterialOfferingDtoList() {
        return productSkcBomSizeRangeMaterialOfferingDtoList;
    }

    public void setProductSkcBomSizeRangeMaterialOfferingDtoList(List<ProductSkcBomSizeRangeMaterialOfferingDto> productSkcBomSizeRangeMaterialOfferingDtoList) {
        this.productSkcBomSizeRangeMaterialOfferingDtoList = productSkcBomSizeRangeMaterialOfferingDtoList;
    }

    public BigDecimal getCurrentUnitPrice() {
        return currentUnitPrice;
    }

    public void setCurrentUnitPrice(BigDecimal currentUnitPrice) {
        this.currentUnitPrice = currentUnitPrice;
    }

    public BigDecimal getOriginalGrammage() {
        return originalGrammage;
    }

    public void setOriginalGrammage(BigDecimal originalGrammage) {
        this.originalGrammage = originalGrammage;
    }

    public BigDecimal getOriginalNetBreadth() {
        return originalNetBreadth;
    }

    public void setOriginalNetBreadth(BigDecimal originalNetBreadth) {
        this.originalNetBreadth = originalNetBreadth;
    }

    public BigDecimal getOriginalGrossBreadth() {
        return originalGrossBreadth;
    }

    public void setOriginalGrossBreadth(BigDecimal originalGrossBreadth) {
        this.originalGrossBreadth = originalGrossBreadth;
    }

    public BigDecimal getOriginalTransverseShrinkRate() {
        return originalTransverseShrinkRate;
    }

    public void setOriginalTransverseShrinkRate(BigDecimal originalTransverseShrinkRate) {
        this.originalTransverseShrinkRate = originalTransverseShrinkRate;
    }

    public BigDecimal getOriginalDirectShrinkRate() {
        return originalDirectShrinkRate;
    }

    public void setOriginalDirectShrinkRate(BigDecimal originalDirectShrinkRate) {
        this.originalDirectShrinkRate = originalDirectShrinkRate;
    }

    public String getFloralStyleDesc() {
        return floralStyleDesc;
    }

    public void setFloralStyleDesc(String floralStyleDesc) {
        this.floralStyleDesc = floralStyleDesc;
    }

    public List<String> getUsePartList() {
        return usePartList;
    }

    public void setUsePartList(List<String> usePartList) {
        this.usePartList = usePartList;
    }

    public List<String> getRiskDescList() {
        return riskDescList;
    }

    public void setRiskDescList(List<String> riskDescList) {
        this.riskDescList = riskDescList;
    }

    public String getSupplierStatusDesc() {
        return supplierStatusDesc;
    }

    public void setSupplierStatusDesc(String supplierStatusDesc) {
        this.supplierStatusDesc = supplierStatusDesc;
    }

    public String getMaterialStatusDesc() {
        return materialStatusDesc;
    }

    public void setMaterialStatusDesc(String materialStatusDesc) {
        this.materialStatusDesc = materialStatusDesc;
    }

    public String getColorCardStatusDesc() {
        return colorCardStatusDesc;
    }

    public void setColorCardStatusDesc(String colorCardStatusDesc) {
        this.colorCardStatusDesc = colorCardStatusDesc;
    }

    public String getMaterialColorCardPriceType() {
        return materialColorCardPriceType;
    }

    public void setMaterialColorCardPriceType(String materialColorCardPriceType) {
        this.materialColorCardPriceType = materialColorCardPriceType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getIsMaterialOrder() {
        return isMaterialOrder;
    }

    public void setIsMaterialOrder(Integer isMaterialOrder) {
        this.isMaterialOrder = isMaterialOrder;
    }

    public String getFabricMerchandiserName() {
        return fabricMerchandiserName;
    }

    public void setFabricMerchandiserName(String fabricMerchandiserName) {
        this.fabricMerchandiserName = fabricMerchandiserName;
    }

    public Date getEstimateSubmitDate() {
        return estimateSubmitDate;
    }

    public void setEstimateSubmitDate(Date estimateSubmitDate) {
        this.estimateSubmitDate = estimateSubmitDate;
    }
}
