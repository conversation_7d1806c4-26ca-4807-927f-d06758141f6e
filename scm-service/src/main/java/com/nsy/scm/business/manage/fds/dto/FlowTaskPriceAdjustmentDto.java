package com.nsy.scm.business.manage.fds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 供应商调价主表
 *
 * @TableName flow_task_price_adjustment
 */
public class FlowTaskPriceAdjustmentDto {
    /**
     * 主键
     */
    private Integer flowTaskPriceAdjustmentId;

    /**
     * bom修改任务id
     */
    private Integer flowTaskEditBomId;

    /**
     * 驳回核价id
     */
    private Integer rejectEditBomId;

    /**
     * 公司id
     */
    private Integer companyId;

    /**
     * 供应链一级品类id
     */
    private Integer scmCategoryId;

    /**
     * 审核是否超期
     */
    private Integer isAuditOvertime;

    /**
     * 有过供应商确认 0-否 1-是
     */
    private Integer haveSupplierConfirm;

    /**
     * 商品系统的调价申请表id
     */
    private Integer productSupplierPurchaseApplyId;

    /**
     * 供应商id
     */
    private Integer supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商所在部门
     */
    private String supplierDepartment;

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * 商品编码
     */
    private String spu;

    private Integer specId;

    /**
     * 商品图片地址
     */
    private String productImageUrl;

    /**
     * 采购员id
     */
    private Integer buyerUserId;

    /**
     * 采购员名称
     */
    private String buyerUserName;

    /**
     * 采购员账号，流程引擎那边用的是账
     */
    private String buyerUserAccount;

    /**
     * 类型
     */
    private String type;

    /**
     * 新类型 1-报价 2-调价
     */
    private Integer newType;

    /**
     * 任务小单价类型 -1-未知 0-否 1-是
     */
    private Integer smallOrderPrice;

    /**
     * 原小单价类型 0-否 1-是
     */
    private Integer originalSmallOrderPrice;

    /**
     * 包含包装费
     */
    private Boolean includePackageFee;

    /**
     * 申请人id
     */
    private Integer applyUserId;

    /**
     * 申请人姓名
     */
    private String applyUserName;

    /**
     * 申请日期
     */
    private Date applyDate;

    /**
     * 申请原因
     */
    private String applyReason;

    /**
     * 申请备注
     */
    private String applyComment;

    /**
     * 提交审核时间
     */
    private Date submitAuditDate;

    /**
     * 申请状态：
     * PREPARE准备中,PENDING_CONFIRM待处理,SUPPLIER_CONFIRM供应商待确认,BARGAIN_SUBMIT议价中,PENDING_AUDIT待审核,TWICE_AUDIT二级审核,COMPLETE已完成,CANCEL已取消
     */
    private String applyStatus;

    /**
     * 申请状态备份
     */
    private String applyStatusBk;

    /**
     * 申请状态描述
     */
    private String applyStatusDesc;

    /**
     * 当前任务状态
     */
    private String taskName;

    /**
     * 完成时间
     */
    private Date endDate;
    /**
     * 申请处理结果：UNADJUSTED不调整，PRICE_ADJUSTMENT调价申请
     */
    private String applyResult;

    /**
     * 审核结果：AUDITED过审，REJECT驳回
     */
    private String auditResult;

    /**
     * 审核驳回原因
     */
    private String rejectReason;

    /**
     * 审核备注
     */
    private String auditComment;

    /**
     * 审核人id
     */
    private Integer auditUserId;

    /**
     * 审核人姓名
     */
    private String auditUserName;

    /**
     * 审核日期
     */
    private Date auditDate;

    /**
     * 二次审核备注
     */
    private String twiceAuditComment;

    /**
     * 二次审核人id
     */
    private Integer twiceAuditUserId;

    /**
     * 二次审核人姓名
     */
    private String twiceAuditUserName;

    /**
     * 二次审核时间
     */
    private Date twiceAuditDate;

    /**
     * 驳回核价原因
     */
    private String rejectPricingReason;

    /**
     * 驳回核价备注
     */
    private String rejectPricingComment;


    /**
     * 核价驳回备注
     */
    private String pricingRejectComment;

    /**
     * 地区
     */
    private String location;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
    private String updateBy;
    private String createBy;

    private Integer version;

    /**
     * 主键
     */
    public Integer getFlowTaskPriceAdjustmentId() {
        return flowTaskPriceAdjustmentId;
    }

    /**
     * 主键
     */
    public void setFlowTaskPriceAdjustmentId(Integer flowTaskPriceAdjustmentId) {
        this.flowTaskPriceAdjustmentId = flowTaskPriceAdjustmentId;
    }

    /**
     * bom修改任务id
     */
    public Integer getFlowTaskEditBomId() {
        return flowTaskEditBomId;
    }

    /**
     * bom修改任务id
     */
    public void setFlowTaskEditBomId(Integer flowTaskEditBomId) {
        this.flowTaskEditBomId = flowTaskEditBomId;
    }

    /**
     * 商品系统的调价申请表id
     */
    public Integer getProductSupplierPurchaseApplyId() {
        return productSupplierPurchaseApplyId;
    }

    /**
     * 商品系统的调价申请表id
     */
    public void setProductSupplierPurchaseApplyId(Integer productSupplierPurchaseApplyId) {
        this.productSupplierPurchaseApplyId = productSupplierPurchaseApplyId;
    }

    /**
     * 供应商id
     */
    public Integer getSupplierId() {
        return supplierId;
    }

    /**
     * 供应商id
     */
    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    /**
     * 供应商名称
     */
    public String getSupplierName() {
        return supplierName;
    }

    /**
     * 供应商名称
     */
    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    /**
     * 供应商所在部门
     */
    public String getSupplierDepartment() {
        return supplierDepartment;
    }

    /**
     * 供应商所在部门
     */
    public void setSupplierDepartment(String supplierDepartment) {
        this.supplierDepartment = supplierDepartment;
    }

    /**
     * 商品id
     */
    public Integer getProductId() {
        return productId;
    }

    /**
     * 商品id
     */
    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    /**
     * 商品编码
     */
    public String getSpu() {
        return spu;
    }

    /**
     * 商品编码
     */
    public void setSpu(String spu) {
        this.spu = spu;
    }

    /**
     * 商品图片地址
     */
    public String getProductImageUrl() {
        return productImageUrl;
    }

    /**
     * 商品图片地址
     */
    public void setProductImageUrl(String productImageUrl) {
        this.productImageUrl = productImageUrl;
    }

    /**
     * 采购员id
     */
    public Integer getBuyerUserId() {
        return buyerUserId;
    }

    /**
     * 采购员id
     */
    public void setBuyerUserId(Integer buyerUserId) {
        this.buyerUserId = buyerUserId;
    }

    /**
     * 采购员名称
     */
    public String getBuyerUserName() {
        return buyerUserName;
    }

    /**
     * 采购员名称
     */
    public void setBuyerUserName(String buyerUserName) {
        this.buyerUserName = buyerUserName;
    }

    /**
     * 申请人id
     */
    public Integer getApplyUserId() {
        return applyUserId;
    }

    /**
     * 申请人id
     */
    public void setApplyUserId(Integer applyUserId) {
        this.applyUserId = applyUserId;
    }

    /**
     * 申请人姓名
     */
    public String getApplyUserName() {
        return applyUserName;
    }

    /**
     * 申请人姓名
     */
    public void setApplyUserName(String applyUserName) {
        this.applyUserName = applyUserName;
    }

    /**
     * 申请日期
     */
    public Date getApplyDate() {
        return applyDate;
    }

    /**
     * 申请日期
     */
    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    /**
     * 申请原因
     */
    public String getApplyReason() {
        return applyReason;
    }

    /**
     * 申请原因
     */
    public void setApplyReason(String applyReason) {
        this.applyReason = applyReason;
    }

    /**
     * 申请备注
     */
    public String getApplyComment() {
        return applyComment;
    }

    /**
     * 申请备注
     */
    public void setApplyComment(String applyComment) {
        this.applyComment = applyComment;
    }

    /**
     * 申请状态：PENDING_CONFIRM待处理，PENDING_AUDIT待审核，COMPLETE已完成
     */
    public String getApplyStatus() {
        return applyStatus;
    }

    /**
     * 申请状态：PENDING_CONFIRM待处理，PENDING_AUDIT待审核，COMPLETE已完成
     */
    public void setApplyStatus(String applyStatus) {
        this.applyStatus = applyStatus;
    }

    /**
     * 申请处理结果：UNADJUSTED不调整，PRICE_ADJUSTMENT调价申请
     */
    public String getApplyResult() {
        return applyResult;
    }

    /**
     * 申请处理结果：UNADJUSTED不调整，PRICE_ADJUSTMENT调价申请
     */
    public void setApplyResult(String applyResult) {
        this.applyResult = applyResult;
    }

    /**
     * 审核结果：AUDITED过审，REJECT驳回
     */
    public String getAuditResult() {
        return auditResult;
    }

    /**
     * 审核结果：AUDITED过审，REJECT驳回
     */
    public void setAuditResult(String auditResult) {
        this.auditResult = auditResult;
    }

    /**
     * 审核备注
     */
    public String getAuditComment() {
        return auditComment;
    }

    /**
     * 审核备注
     */
    public void setAuditComment(String auditComment) {
        this.auditComment = auditComment;
    }

    /**
     * 审核人id
     */
    public Integer getAuditUserId() {
        return auditUserId;
    }

    /**
     * 审核人id
     */
    public void setAuditUserId(Integer auditUserId) {
        this.auditUserId = auditUserId;
    }

    /**
     * 审核人姓名
     */
    public String getAuditUserName() {
        return auditUserName;
    }

    /**
     * 审核人姓名
     */
    public void setAuditUserName(String auditUserName) {
        this.auditUserName = auditUserName;
    }

    /**
     * 审核日期
     */
    public Date getAuditDate() {
        return auditDate;
    }

    /**
     * 审核日期
     */
    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    /**
     * 地区
     */
    public String getLocation() {
        return location;
    }

    /**
     * 地区
     */
    public void setLocation(String location) {
        this.location = location;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBuyerUserAccount() {
        return buyerUserAccount;
    }

    public void setBuyerUserAccount(String buyerUserAccount) {
        this.buyerUserAccount = buyerUserAccount;
    }

    public Integer getNewType() {
        return newType;
    }

    public void setNewType(Integer newType) {
        this.newType = newType;
    }

    public String getApplyStatusBk() {
        return applyStatusBk;
    }

    public void setApplyStatusBk(String applyStatusBk) {
        this.applyStatusBk = applyStatusBk;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public String getTwiceAuditComment() {
        return twiceAuditComment;
    }

    public void setTwiceAuditComment(String twiceAuditComment) {
        this.twiceAuditComment = twiceAuditComment;
    }

    public Integer getTwiceAuditUserId() {
        return twiceAuditUserId;
    }

    public void setTwiceAuditUserId(Integer twiceAuditUserId) {
        this.twiceAuditUserId = twiceAuditUserId;
    }

    public String getTwiceAuditUserName() {
        return twiceAuditUserName;
    }

    public void setTwiceAuditUserName(String twiceAuditUserName) {
        this.twiceAuditUserName = twiceAuditUserName;
    }

    public Date getTwiceAuditDate() {
        return twiceAuditDate;
    }

    public void setTwiceAuditDate(Date twiceAuditDate) {
        this.twiceAuditDate = twiceAuditDate;
    }

    public Integer getSmallOrderPrice() {
        return smallOrderPrice;
    }

    public void setSmallOrderPrice(Integer smallOrderPrice) {
        this.smallOrderPrice = smallOrderPrice;
    }

    public Boolean getIncludePackageFee() {
        return includePackageFee;
    }

    public void setIncludePackageFee(Boolean includePackageFee) {
        this.includePackageFee = includePackageFee;
    }

    public Integer getRejectEditBomId() {
        return rejectEditBomId;
    }

    public void setRejectEditBomId(Integer rejectEditBomId) {
        this.rejectEditBomId = rejectEditBomId;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Integer getScmCategoryId() {
        return scmCategoryId;
    }

    public void setScmCategoryId(Integer scmCategoryId) {
        this.scmCategoryId = scmCategoryId;
    }

    public Integer getIsAuditOvertime() {
        return isAuditOvertime;
    }

    public void setIsAuditOvertime(Integer isAuditOvertime) {
        this.isAuditOvertime = isAuditOvertime;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public String getProductIdAndSpecId() {
        return String.format("%s_%s", productId, specId);
    }

    public String getRejectPricingReason() {
        return rejectPricingReason;
    }

    public void setRejectPricingReason(String rejectPricingReason) {
        this.rejectPricingReason = rejectPricingReason;
    }

    public String getRejectPricingComment() {
        return rejectPricingComment;
    }

    public void setRejectPricingComment(String rejectPricingComment) {
        this.rejectPricingComment = rejectPricingComment;
    }

    public Date getSubmitAuditDate() {
        return submitAuditDate;
    }

    public void setSubmitAuditDate(Date submitAuditDate) {
        this.submitAuditDate = submitAuditDate;
    }

    public String getPricingRejectComment() {
        return pricingRejectComment;
    }

    public void setPricingRejectComment(String pricingRejectComment) {
        this.pricingRejectComment = pricingRejectComment;
    }

    public Integer getOriginalSmallOrderPrice() {
        return originalSmallOrderPrice;
    }

    public void setOriginalSmallOrderPrice(Integer originalSmallOrderPrice) {
        this.originalSmallOrderPrice = originalSmallOrderPrice;
    }

    public Integer getHaveSupplierConfirm() {
        return haveSupplierConfirm;
    }

    public void setHaveSupplierConfirm(Integer haveSupplierConfirm) {
        this.haveSupplierConfirm = haveSupplierConfirm;
    }

    /**
     * 获取 @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" timezone = "GMT+8")    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
     */
    public Date getCreateDate() {
        return this.createDate;
    }

    /**
     * 设置 @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" timezone = "GMT+8")    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * 获取 @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" timezone = "GMT+8")    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
     */
    public Date getUpdateDate() {
        return this.updateDate;
    }

    /**
     * 设置 @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" timezone = "GMT+8")    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
     */
    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    /**
     * 获取
     */
    public String getUpdateBy() {
        return this.updateBy;
    }

    /**
     * 设置
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 获取
     */
    public String getCreateBy() {
        return this.createBy;
    }

    /**
     * 设置
     */
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 获取
     */
    public Integer getVersion() {
        return this.version;
    }

    /**
     * 设置
     */
    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getApplyStatusDesc() {
        return applyStatusDesc;
    }

    public void setApplyStatusDesc(String applyStatusDesc) {
        this.applyStatusDesc = applyStatusDesc;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
}
