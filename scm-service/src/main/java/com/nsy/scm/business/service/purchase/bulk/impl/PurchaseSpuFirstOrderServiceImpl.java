package com.nsy.scm.business.service.purchase.bulk.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.scm.dto.domain.purchase.PurchaseSpuFirstOrderDto;
import com.nsy.api.scm.dto.request.purchase.erp.order.dto.FirstOrderInfoBySkcDto;
import com.nsy.api.scm.dto.request.purchase.erp.order.request.FirstOrderInfoBySkcOrSpuRequest;
import com.nsy.scm.business.service.purchase.bulk.PurchaseOrderItemService;
import com.nsy.scm.business.service.purchase.bulk.PurchaseOrderService;
import com.nsy.scm.business.service.purchase.bulk.PurchaseSpuFirstOrderService;
import com.nsy.scm.common.annotation.IgnoreTenant;
import com.nsy.scm.constant.bulk.PurchaseOrderItemStatusEnum;
import com.nsy.scm.repository.entity.purchase.PurchaseSpuFirstOrderEntity;
import com.nsy.scm.repository.entity.purchase.bulk.PurchaseOrderEntity;
import com.nsy.scm.repository.entity.purchase.bulk.PurchaseOrderItemEntity;
import com.nsy.scm.repository.sql.mapper.PurchaseSpuFirstOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class PurchaseSpuFirstOrderServiceImpl extends ServiceImpl<PurchaseSpuFirstOrderMapper, PurchaseSpuFirstOrderEntity> implements PurchaseSpuFirstOrderService {
    @Autowired
    private PurchaseOrderItemService purchaseOrderItemService;
    @Autowired
    private PurchaseOrderService purchaseOrderService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSpuFirstOrder(String spu, Integer productId, Integer orderId, String orderNo, Date date, String location) {
        List<PurchaseSpuFirstOrderEntity> list = this.getBaseMapper().selectList(new LambdaQueryWrapper<PurchaseSpuFirstOrderEntity>().eq(PurchaseSpuFirstOrderEntity::getSpu, spu).eq(PurchaseSpuFirstOrderEntity::getLocation, location));
        PurchaseSpuFirstOrderEntity entity = list.stream().findFirst().orElse(new PurchaseSpuFirstOrderEntity());
        if (entity.getStatus().equals(0)) {
            entity.setSpu(spu);
            entity.setProductId(productId);
            entity.setOrderId(orderId);
            entity.setOrderNo(orderNo);
            entity.setOrderDate(date);
            entity.setLocation(location);
            entity.setStatus(1);
            saveOrUpdate(entity);
        }
    }

    @Override
    public boolean isSpuFirstOrder(String spu, String location) {
        List<PurchaseSpuFirstOrderEntity> list = this.getBaseMapper().selectList(new LambdaQueryWrapper<PurchaseSpuFirstOrderEntity>().eq(PurchaseSpuFirstOrderEntity::getSpu, spu).eq(PurchaseSpuFirstOrderEntity::getLocation, location));
        PurchaseSpuFirstOrderEntity entity = list.stream().findFirst().orElse(new PurchaseSpuFirstOrderEntity());
        return Objects.equals(0, entity.getStatus());
    }

    @Override
    public boolean isSpuFirstOrder(String spu, Integer orderId, String location) {
        List<PurchaseSpuFirstOrderEntity> list = this.getBaseMapper().selectList(new LambdaQueryWrapper<PurchaseSpuFirstOrderEntity>().eq(PurchaseSpuFirstOrderEntity::getSpu, spu).eq(PurchaseSpuFirstOrderEntity::getLocation, location));
        PurchaseSpuFirstOrderEntity entity = list.stream().findFirst().orElse(new PurchaseSpuFirstOrderEntity());
        return Objects.equals(0, entity.getStatus()) || entity.getOrderId().equals(orderId);
    }

    @Override
    @IgnoreTenant
    @Transactional(rollbackFor = Exception.class)
    public void reCalSpuFirstOrder(String spu, Integer productId, String location) {
        List<PurchaseSpuFirstOrderEntity> list = this.getBaseMapper().selectList(new LambdaQueryWrapper<PurchaseSpuFirstOrderEntity>().eq(PurchaseSpuFirstOrderEntity::getSpu, spu).eq(PurchaseSpuFirstOrderEntity::getLocation, location));
        PurchaseSpuFirstOrderEntity entity = list.stream().findFirst().orElse(new PurchaseSpuFirstOrderEntity());
        if (entity.getStatus().equals(1)) {
            //记录的采购单依然有spu存在，则不处理
            if (CollectionUtils.isEmpty(purchaseOrderItemService.list(new LambdaQueryWrapper<PurchaseOrderItemEntity>().eq(PurchaseOrderItemEntity::getOrderId, entity.getOrderId()).eq(PurchaseOrderItemEntity::getSpu, entity.getSpu()).ne(PurchaseOrderItemEntity::getStatus, PurchaseOrderItemStatusEnum.CANCELED.getValue())))) {
                reSetSpuFirstOrder(spu, productId, location, entity);
            }
        } else {
            reSetSpuFirstOrder(spu, productId, location, entity);
        }
    }

    @Override
    public Map<String, List<PurchaseSpuFirstOrderEntity>> findByProductIdAndStatusGroupBySpuAndLocation(Collection<Integer> productIds, Integer status) {
        List<PurchaseSpuFirstOrderEntity> list = list(new LambdaQueryWrapper<PurchaseSpuFirstOrderEntity>()
                .in(PurchaseSpuFirstOrderEntity::getProductId, productIds)
                .in(PurchaseSpuFirstOrderEntity::getStatus, status));
        return list.stream().collect(Collectors.groupingBy(s -> String.format("%s-%s", s.getProductId(), s.getLocation())));
    }

    private void reSetSpuFirstOrder(String spu, Integer productId, String location, PurchaseSpuFirstOrderEntity entity) {
        FirstOrderInfoBySkcOrSpuRequest request = new FirstOrderInfoBySkcOrSpuRequest();
        request.setSpuList(new LinkedList<>());
        request.getSpuList().add(spu);
        request.setLocation(location);
        FirstOrderInfoBySkcDto orderItem = purchaseOrderItemService.getFirstOrderBySkcOrSpu(request).stream().findFirst().orElse(null);
        entity.setSpu(spu);
        entity.setProductId(productId);
        entity.setLocation(location);
        entity.setStatus(0);
        if (Objects.nonNull(orderItem)) {
            entity.setOrderId(orderItem.getFirstOrderId());
            PurchaseOrderEntity order = purchaseOrderService.getById(entity.getOrderId());
            entity.setOrderDate(order.getCreateDate());
            entity.setStatus(1);
            entity.setOrderNo(order.getOrderNo());
        }
        if (entity.getOrderId() != null && entity.getOrderId() > 0) {
            saveOrUpdate(entity);
        }
    }

    @Override
    public List<PurchaseSpuFirstOrderDto> querySpuFirstOrders(List<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Lists.newArrayList();
        }
        List<PurchaseSpuFirstOrderDto> purchaseSpuFirstOrderDtos = this.baseMapper.querySpuFirstOrders(productIds);
        if (CollectionUtils.isEmpty(purchaseSpuFirstOrderDtos)) {
            return productIds.stream().map(id -> {
                PurchaseSpuFirstOrderDto purchaseSpuFirstOrderDto = new PurchaseSpuFirstOrderDto();
                purchaseSpuFirstOrderDto.setProductId(id);
                return purchaseSpuFirstOrderDto;
            }).collect(Collectors.toList());
        }
        return purchaseSpuFirstOrderDtos;
    }
}
