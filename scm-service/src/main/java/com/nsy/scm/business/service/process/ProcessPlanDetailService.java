package com.nsy.scm.business.service.process;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.scm.constant.DictionaryNameEnum;
import com.nsy.scm.constant.ProcessImageSourceTypeEnum;
import com.nsy.scm.constant.ProcessImageTypeEnum;
import com.nsy.scm.constant.ProcessPlanLogEnum;
import com.nsy.scm.business.domain.process.PlanStatusLog;
import com.nsy.scm.business.domain.process.ProcessPlanDTO;
import com.nsy.scm.business.domain.process.ProcessPlanLogDto;
import com.nsy.scm.business.domain.process.ProcessPlanSkuInfo;
import com.nsy.scm.repository.entity.ProcessPlanEntity;
import com.nsy.scm.repository.entity.ProcessPlanItemEntity;
import com.nsy.scm.repository.entity.ProcessPlanLogEntity;
import com.nsy.scm.business.request.process.ProcessPlanDetailRequest;
import com.nsy.scm.business.response.process.ProcessPlanCountResponse;
import com.nsy.scm.business.response.process.ProcessPlanDetailResponse;
import com.nsy.scm.repository.cache.DictionaryCacheService;
import com.nsy.api.scm.dto.domain.dictionary.DictionaryItem;
import com.nsy.api.scm.dto.response.common.PageResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * HXD
 * 2021/9/26
 **/
@Service
public class ProcessPlanDetailService {
    @Autowired
    private ProcessPlanService processPlanService;
    @Autowired
    private ProcessPlanItemService itemService;
    @Autowired
    private DictionaryCacheService dictionaryCacheService;
    @Autowired
    private ProcessPlanLogService processPlanLogService;
    @Autowired
    private ProcessImageService imageService;

    public ProcessPlanDetailResponse infoDetail(Integer planId) {
        ProcessPlanEntity entity = processPlanService.getById(planId);
        if (entity == null) {
            throw new BusinessServiceException(planId + "找不到对应的计划单");
        }
        ProcessPlanDTO dto = new ProcessPlanDTO();
        BeanUtils.copyProperties(entity, dto);
        Map<String, DictionaryItem> allDictionaryItemMap = dictionaryCacheService.getAllDictionaryItemMap();
        dto.setCustomTechnologyLabel(dto.getCustomTechnology());
        dto.setCustomTypeLabel(DictionaryNameEnum.getDictionaryItemMapValue(allDictionaryItemMap, DictionaryNameEnum.SCM_PROCESS_CUSTOM_TYPE.getParentKey(), dto.getCustomType()));
        dto.setProcessTypeLabel(DictionaryNameEnum.getDictionaryItemMapValue(allDictionaryItemMap, DictionaryNameEnum.SCM_PROCESS_TYPE.getParentKey(), dto.getProcessType()));
        dto.setStatusLabel(DictionaryNameEnum.getDictionaryItemMapValue(allDictionaryItemMap, DictionaryNameEnum.SCM_PROCESS_PLAN_STATUS.getParentKey(), dto.getStatus()));
        dto.setBusinessTypeLabel(dto.getBusinessType());
        ProcessPlanDetailResponse response = new ProcessPlanDetailResponse();
        response.setProcessPlanDTO(dto);
        List<PlanStatusLog> planStatusLogs = processPlanLogService.getBaseMapper().showStatusLog(planId);
        List<PlanStatusLog> collect = planStatusLogs.stream().filter(item -> StringUtils.hasText(item.getStatus()))
                .peek(item -> {
                    item.setStatus(DictionaryNameEnum.getDictionaryItemMapValue(allDictionaryItemMap, DictionaryNameEnum.SCM_PROCESS_PLAN_STATUS.getParentKey(), dto.getStatus()));
                    if (Objects.equals("准备中", item.getStatus())) {
                        item.setStatus("已生成");
                    }
                    if (Objects.equals("待加工", item.getStatus())) {
                        item.setStatus("已拣货完成");
                    }
                })
                .collect(Collectors.toList());
        response.setPlanStatusLogList(collect);
        return response;
    }

    public PageResponse<ProcessPlanSkuInfo> skuInfoList(ProcessPlanDetailRequest request) {
        LambdaQueryWrapper<ProcessPlanItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProcessPlanItemEntity::getPlanId, request.getPlanId());
        if (StringUtils.hasText(request.getSku())) {
            wrapper.and(wp -> wp.eq(ProcessPlanItemEntity::getBaseSku, request.getSku())
                    .or().eq(ProcessPlanItemEntity::getCustomSku, request.getSku()));
        }
        IPage<ProcessPlanItemEntity> iPage = itemService.page(new Page<>(request.getPageIndex(), request.getPageSize()), wrapper.orderByAsc(ProcessPlanItemEntity::getPlanItemId));
        Map<String, DictionaryItem> allDictionaryItemMap = dictionaryCacheService.getAllDictionaryItemMap();
        List<ProcessPlanSkuInfo> list = iPage.getRecords().stream().map(entity -> {
            ProcessPlanSkuInfo info = new ProcessPlanSkuInfo();
            BeanUtils.copyProperties(entity, info);
            info.setCustomTechnologyLabel(info.getCustomTechnology());
            info.setCustomTypeLabel(DictionaryNameEnum.getDictionaryItemMapValue(allDictionaryItemMap, DictionaryNameEnum.SCM_PROCESS_CUSTOM_TYPE.getParentKey(), info.getCustomType()));
            info.setStatusLabel(DictionaryNameEnum.getDictionaryItemMapValue(allDictionaryItemMap, DictionaryNameEnum.SCM_PROCESS_TASK_STATUS.getParentKey(), info.getStatus()));
            info.setBaseImageUrlList(imageService.getBySourceId(ProcessImageTypeEnum.BASE.name(), ProcessImageSourceTypeEnum.PLAN_ITEM.name(), entity.getPlanItemId()));
            info.setElementImageUrlList(imageService.getBySourceId(ProcessImageTypeEnum.ELEMENT.name(), ProcessImageSourceTypeEnum.PLAN_ITEM.name(), entity.getPlanItemId()));
            info.setCustomImageUrlList(imageService.getBySourceId(ProcessImageTypeEnum.CUSTOM.name(), ProcessImageSourceTypeEnum.PLAN_ITEM.name(), entity.getPlanItemId()));
            return info;
        }).collect(Collectors.toList());
        return PageResponse.of(list, iPage.getTotal());
    }

    public ProcessPlanCountResponse skuInfoCount(ProcessPlanDetailRequest request) {
        return itemService.skuInfoCount(request);
    }

    public PageResponse<ProcessPlanLogDto> logList(ProcessPlanDetailRequest request) {
        LambdaQueryWrapper<ProcessPlanLogEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProcessPlanLogEntity::getProcessPlanId, request.getPlanId());
        ProcessPlanLogEnum planLogEnum = ProcessPlanLogEnum.getByType(request.getEventType());
        if (planLogEnum != null) {
            request.setEventType(planLogEnum.name());
        }
        wrapper.eq(StringUtils.hasText(request.getEventType()), ProcessPlanLogEntity::getEventType, request.getEventType());
        wrapper.eq(StringUtils.hasText(request.getCreateBy()), ProcessPlanLogEntity::getOperateName, request.getCreateBy());
        wrapper.like(StringUtils.hasText(request.getContent()), ProcessPlanLogEntity::getContent, "%" + request.getContent() + "%");
        IPage<ProcessPlanLogEntity> iPage = processPlanLogService.page(new Page<>(request.getPageIndex(), request.getPageSize()), wrapper);
        List<ProcessPlanLogDto> collect = iPage.getRecords().stream().map(item -> {
            ProcessPlanLogDto dto = new ProcessPlanLogDto();
            BeanUtils.copyProperties(item, dto);
            ProcessPlanLogEnum log = ProcessPlanLogEnum.getByName(item.getEventType());
            if (log == null) {
                dto.setEventTypeLabel(dto.getEventType());
            } else {
                dto.setEventTypeLabel(log.getType());
            }
            return dto;
        }).collect(Collectors.toList());
        return PageResponse.of(collect, iPage.getTotal());
    }
}
