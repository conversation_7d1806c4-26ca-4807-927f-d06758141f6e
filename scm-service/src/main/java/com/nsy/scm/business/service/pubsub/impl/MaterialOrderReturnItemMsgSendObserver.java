package com.nsy.scm.business.service.pubsub.impl;

import com.google.common.collect.Sets;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.scm.dto.constant.AttachmentTypeEnum;
import com.nsy.api.scm.dto.domain.supplier.SupplierAddressDto;
import com.nsy.scm.business.domain.common.MarkdownMsgTemplate;
import com.nsy.scm.business.domain.pubsub.MaterialOrderReturnItemReturnParam;
import com.nsy.scm.business.manage.peer.user.UserApiService;
import com.nsy.scm.business.manage.peer.user.dto.BdDictionaryItemDto;
import com.nsy.scm.business.service.common.MessageService;
import com.nsy.scm.business.service.material.MaterialPurchaseOrderItemBulkOrderDetailService;
import com.nsy.scm.business.service.material.MaterialSupplierAttachmentService;
import com.nsy.scm.business.service.material.MaterialSupplierColorCardService;
import com.nsy.scm.business.service.material.MaterialSupplierInfoService;
import com.nsy.scm.business.service.pubsub.Observer;
import com.nsy.scm.business.service.pubsub.SimpleSubject;
import com.nsy.scm.business.service.supplier.SupplierAddressService;
import com.nsy.scm.business.service.supplier.impl.SupplierInfoService;
import com.nsy.scm.constant.DictionaryNameEnum;
import com.nsy.scm.enumstable.material.MaterialTypeEnum;
import com.nsy.scm.constant.StringConstant;
import com.nsy.scm.repository.dao.MaterialDao;
import com.nsy.scm.repository.dao.MaterialReceiveInfoDao;
import com.nsy.scm.repository.entity.MaterialEntity;
import com.nsy.scm.repository.entity.MaterialSupplierInfoEntity;
import com.nsy.scm.repository.entity.SupplierEntity;
import com.nsy.scm.repository.entity.material.MaterialPurchaseOrderEntity;
import com.nsy.scm.repository.entity.material.MaterialPurchaseOrderItemBulkOrderDetailEntity;
import com.nsy.scm.repository.entity.material.MaterialPurchaseOrderItemEntity;
import com.nsy.scm.repository.entity.material.MaterialPurchaseOrderReturnItemEntity;
import com.nsy.scm.repository.entity.material.MaterialReceiveInfoEntity;
import com.nsy.scm.repository.entity.material.MaterialSupplierAttachmentEntity;
import com.nsy.scm.repository.entity.material.MaterialSupplierColorCardEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 集采单接单，发送消息
 *
 * <AUTHOR>
 */
@Component
public class MaterialOrderReturnItemMsgSendObserver implements Observer<MaterialOrderReturnItemReturnParam> {

    private static final Logger LOGGER = LoggerFactory.getLogger(MaterialOrderReturnItemMsgSendObserver.class);
    @Autowired
    private MessageService messageService;
    @Autowired
    private MaterialSupplierAttachmentService materialSupplierAttachmentService;
    @Autowired
    private MaterialSupplierColorCardService materialColorCardService;
    @Autowired
    private SupplierAddressService supplierAddressService;
    @Autowired
    private SupplierInfoService supplierInfoService;
    @Autowired
    private MaterialSupplierInfoService materialSupplierInfoService;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    private MaterialDao materialDao;
    @Autowired
    private MaterialReceiveInfoDao materialReceiveInfoDao;
    @Autowired
    private MaterialPurchaseOrderItemBulkOrderDetailService materialPurchaseOrderItemBulkOrderDetailService;

    @Override
    public void register() {
        SimpleSubject.MaterialOrderReturnItemReturnSubject.getInstance().registerObserver(this);
    }

    @Override
    public void fire(MaterialOrderReturnItemReturnParam param) {
        List<MaterialPurchaseOrderReturnItemEntity> orderReturnItemList = param.getOrderReturnItemList();
        Collection<MaterialPurchaseOrderItemEntity> orderItemList = param.getOrderItemList();
        List<MaterialPurchaseOrderEntity> orderList = param.getOrderList();
        // 根据领料单主表ID分组
        Map<Integer, MaterialPurchaseOrderItemEntity> orderItemMap = orderItemList.stream().collect(Collectors.toMap(MaterialPurchaseOrderItemEntity::getPurchaseOrderItemId, Function.identity(), (v1, v2) -> v2));
        // List转map
        Map<Integer, MaterialPurchaseOrderEntity> orderMap = orderList.stream().collect(Collectors.toMap(MaterialPurchaseOrderEntity::getPurchaseOrderId, Function.identity(), (v1, v2) -> v2));
        // 收货信息
        Map<Integer, MaterialReceiveInfoEntity> materialReceiveInfoMap = materialReceiveInfoDao.queryByPurchaseOrderItemIds(orderItemList.stream().map(MaterialPurchaseOrderItemEntity::getPurchaseOrderItemId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(MaterialReceiveInfoEntity::getPurchaseOrderItemId, Function.identity(), (v1, v2) -> v2));
        // 根据领料单ID分组
        orderReturnItemList.forEach(orderReturnItem -> {
            MaterialPurchaseOrderItemEntity materialPurchaseOrderItemEntity = orderItemMap.get(orderReturnItem.getMaterialOrderItemId());
            MaterialPurchaseOrderEntity materialPurchaseOrderEntity = orderMap.get(materialPurchaseOrderItemEntity.getPurchaseOrderId());
            MaterialReceiveInfoEntity receiveInfo = materialReceiveInfoMap.get(materialPurchaseOrderItemEntity.getPurchaseOrderItemId());
            // 发消息
            this.sendMsg(buildMarkdownMsgTemplate(materialPurchaseOrderEntity, materialPurchaseOrderItemEntity, receiveInfo, orderReturnItem, param), "面料领料单退货", getMsgSendUserIds(materialPurchaseOrderEntity));
        });
    }

    private MarkdownMsgTemplate buildMarkdownMsgTemplate(MaterialPurchaseOrderEntity orderEntity,
                                                         MaterialPurchaseOrderItemEntity orderItemEntity,
                                                         MaterialReceiveInfoEntity receiveInfo,
                                                         MaterialPurchaseOrderReturnItemEntity materialPurchaseOrderReturnItem,
                                                         MaterialOrderReturnItemReturnParam param) {
        // 面料参考图片
        String materialReferenceImgUrl = materialSupplierAttachmentService.getEntitiesByMaterialSupplierInfoId(orderItemEntity.getSupplierInfoId(), AttachmentTypeEnum.FABRIC_SUPPLIER_REFERENCE_IMG)
                .stream().map(MaterialSupplierAttachmentEntity::getAttachmentUrl).filter(StringUtils::isNotBlank).findFirst().orElse(StringUtils.EMPTY);
        // 花型色卡图
        String colorCardImgUrl = Optional.ofNullable(materialColorCardService.getById(orderItemEntity.getColorCardId()))
                .map(MaterialSupplierColorCardEntity::getImgUrl).orElse(StringUtils.EMPTY);
        // 面料供应商信息
        MaterialSupplierInfoEntity supplierInfoEntity = Optional.ofNullable(materialSupplierInfoService.getById(orderItemEntity.getSupplierInfoId())).orElse(new MaterialSupplierInfoEntity());
        MaterialEntity material = Optional.ofNullable(materialDao.getById(supplierInfoEntity.getMaterialId())).orElse(new MaterialEntity());

        Integer supplierId = supplierInfoEntity.getSupplierId();
        SupplierEntity supplierEntity = Optional.ofNullable(supplierInfoService.getById(supplierId)).orElse(new SupplierEntity());
        SupplierAddressDto supplierAddressDto = Optional.ofNullable(supplierAddressService.getSupplierAddressDtoMap(Collections.singletonList(supplierId)).get(supplierId)).orElse(new SupplierAddressDto());
        // 颜色字典值
        Map<String, BdDictionaryItemDto> bdDictionaryItemDtoMap = userApiService.getBdDictionaryItemMapByDictionaryName(DictionaryNameEnum.COLOR.getParentKey());
        // 大货采购单
        Map<Integer, MaterialPurchaseOrderItemBulkOrderDetailEntity> bulkOrderMap = materialPurchaseOrderItemBulkOrderDetailService.queryByMaterialOrderId(orderItemEntity.getPurchaseOrderId())
                .stream().filter(s -> StringUtils.isNotEmpty(s.getBulkOrderNo()))
                .collect(Collectors.toMap(MaterialPurchaseOrderItemBulkOrderDetailEntity::getMaterialPurchaseOrderId, Function.identity(), (k1, k2) -> k1));

        return MarkdownMsgTemplate.create("面料领料单退货：【" + orderItemEntity.getSupplierMaterialName() + "】")
                .addImg(materialReferenceImgUrl)
                .addImg(colorCardImgUrl)
                .appendBold("大货采购单号：").appendNormal(Optional.ofNullable(bulkOrderMap.get(orderEntity.getPurchaseOrderId())).map(MaterialPurchaseOrderItemBulkOrderDetailEntity::getBulkOrderNo).orElse(StringConstant.EMPTY_STRING)).newLine()
                .appendBold("集采领料单号：").appendNormal(orderEntity.getPurchaseOrderNo()).newLine()
                .appendBold("面料供应商：").appendNormal(supplierEntity.getSupplierName()).newLine()
                .appendBold("面料供应商联系方式：").appendNormal(supplierAddressDto.getContactsName()).appendNormal("/").appendNormal(supplierAddressDto.getContactsPhone()).newLine()
                .appendBold("预计发货日期：").appendNormal(getEstimateDeliverDateStr(receiveInfo)).newLine()
                .appendBold("预计到货日期：").appendNormal(getEstimateSubmitDateStr(receiveInfo)).newLine()
                .appendBold("下单人：").appendNormal(orderEntity.getBuyerUserName()).newLine()
                .appendBold("面料花型/供应商色号：").appendNormal(orderItemEntity.getColorNumber()).newLine()
                .appendBold("颜色描述：").appendNormal(Optional.ofNullable(bdDictionaryItemDtoMap.get(DictionaryNameEnum.getDictionaryItemMapKey(DictionaryNameEnum.COLOR.getParentKey(), orderItemEntity.getColorDictionaryItemValue())))
                        .map(bdDictionaryItemDto -> String.format("%s#%s", bdDictionaryItemDto.getAttribute(), bdDictionaryItemDto.getLabel())).orElse(StringUtils.EMPTY)).newLine()
                .appendBold("花型编号：").appendNormal(getColorCardNo(orderItemEntity, material)).newLine()
                .appendBold("领料花型下单量：").appendNormal(String.valueOf(orderItemEntity.getPurchaseQuantity())).appendNormal(orderItemEntity.getPurchaseQuantityUnit()).newLine()
                .appendBold("成衣工厂需求量：").newLine()
                .appendBold(orderEntity.getSycSupplierCode()).appendBold(":")
                .appendNormal(String.valueOf(orderItemEntity.getDemand())).appendNormal(orderItemEntity.getDemandUnit()).newLine()
                .appendBold("退货数量：").appendNormal(String.valueOf(materialPurchaseOrderReturnItem.getReturnQty())).appendNormal(orderItemEntity.getPurchaseQuantityUnit()).newLine()
                .appendBold("退货原因：").appendNormal(param.getReturnReason()).newLine()
                .addImg(param.getFirstReturnAttachImgUrl()).newLine();
    }

    private String getColorCardNo(MaterialPurchaseOrderItemEntity orderItemEntity, MaterialEntity material) {
        return MaterialTypeEnum.ACCESSORIES.name().equals(material.getMaterialCategory()) ? "" : orderItemEntity.getColorCardNo();
    }

    private String getEstimateSubmitDateStr(MaterialReceiveInfoEntity receiveInfoEntity) {
        return Optional.ofNullable(receiveInfoEntity.getEstimateSubmitDate())
                .map(DateUtils::format).orElse(null);
    }

    private String getEstimateDeliverDateStr(MaterialReceiveInfoEntity receiveInfoEntity) {
        return Optional.ofNullable(receiveInfoEntity.getEstimateDeliverDate())
                .map(DateUtils::format).orElse(null);
    }

    private Set<Integer> getMsgSendUserIds(MaterialPurchaseOrderEntity order) {
        Integer createUserId = order.getBuyerUserId();
        Integer purchaserEmpId = Optional.ofNullable(supplierInfoService.getById(order.getSycSupplierId()))
                .map(SupplierEntity::getContactPurchaserEmpId).orElse(0);
        return Sets.newHashSet(createUserId, purchaserEmpId);
    }

    private void sendMsg(MarkdownMsgTemplate msgTemplate, String title, Set<Integer> receiverIds) {
        if (CollectionUtils.isEmpty(receiverIds)) {
            LOGGER.info("sendMsg ignore,empty receiverIds");
            return;
        }
        messageService.sendMarkdownDingTalkRobotMessageByUserId(receiverIds, title, msgTemplate.generateContext());
    }
}
