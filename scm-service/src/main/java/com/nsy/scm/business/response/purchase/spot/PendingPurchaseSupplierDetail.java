package com.nsy.scm.business.response.purchase.spot;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@ApiModel(value = "PendingPurchaseSupplierDetail", description = "待平台下单详情")
public class PendingPurchaseSupplierDetail {


    /**
     * 采买来源
     */
    @ApiModelProperty("采买来源")
    private String planTypeDesc;

    /**
     * 仓库名称
     */
    @ApiModelProperty("仓库名称")
    private String spaceName;

    /**
     * 待下单总数
     */
    @ApiModelProperty("待下单总数")
    private Integer planPurchaseQty;

    /**
     * 款式总数
     */
    @ApiModelProperty("款式总数")
    private Long purchasedSpuQty;

    /**
     * SKU总数
     */
    @ApiModelProperty("SKU总数")
    private Long purchasedSkuQty;

    /**
     * 供应商Id
     */
    @ApiModelProperty("供应商Id")
    private Integer supplierId;

    /**
     * 供应商登录账号Id
     */
    @ApiModelProperty("供应商登录账号Id")
    private String supplierLoginId;

    /**
     * 供应商Id
     */
    @ApiModelProperty("供应商名称")
    private String supplierName;

    /**
     * 采购员员工名称
     */
    @ApiModelProperty("采购员员工名称")
    private String purchaserName;

    @ApiModelProperty("供应商备注")
    private String supplierRemark;


    @ApiModelProperty("列表数据")
    private List<PendingPurchasePlan> pendingPurchasePlans;

    public static PendingPurchaseSupplierDetail empty() {
        PendingPurchaseSupplierDetail detail = new PendingPurchaseSupplierDetail();
        detail.setPendingPurchasePlans(Collections.emptyList());
        return detail;
    }


    public String getSupplierLoginId() {
        return supplierLoginId;
    }

    public void setSupplierLoginId(String supplierLoginId) {
        this.supplierLoginId = supplierLoginId;
    }

    public String getSupplierRemark() {
        return supplierRemark;
    }

    public void setSupplierRemark(String supplierRemark) {
        this.supplierRemark = supplierRemark;
    }

    public List<PendingPurchasePlan> getPendingPurchasePlans() {
        return pendingPurchasePlans;
    }

    public void setPendingPurchasePlans(List<PendingPurchasePlan> pendingPurchasePlans) {
        this.pendingPurchasePlans = pendingPurchasePlans;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getPlanTypeDesc() {
        return planTypeDesc;
    }

    public void setPlanTypeDesc(String planTypeDesc) {
        this.planTypeDesc = planTypeDesc;
    }

    public Integer getPlanPurchaseQty() {
        return planPurchaseQty;
    }

    public void setPlanPurchaseQty(Integer planPurchaseQty) {
        this.planPurchaseQty = planPurchaseQty;
    }

    public Long getPurchasedSpuQty() {
        return purchasedSpuQty;
    }

    public void setPurchasedSpuQty(Long purchasedSpuQty) {
        this.purchasedSpuQty = purchasedSpuQty;
    }

    public Long getPurchasedSkuQty() {
        return purchasedSkuQty;
    }

    public void setPurchasedSkuQty(Long purchasedSkuQty) {
        this.purchasedSkuQty = purchasedSkuQty;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getPurchaserName() {
        return purchaserName;
    }

    public void setPurchaserName(String purchaserName) {
        this.purchaserName = purchaserName;
    }


    public static class PendingPurchasePlan {
        /**
         * 主键id
         */
        @ApiModelProperty("计划单主键ID")
        private Integer planId;

        /**
         * SKU可采买状态
         */
        @ApiModelProperty("SKU可采买状态")
        private String status;

        /**
         * 商品Id
         */
        @ApiModelProperty("商品Id")
        private Integer productId;

        /**
         * 规格Id
         */
        @ApiModelProperty("规格Id")
        private Integer specId;

        /**
         * skc
         */
        @ApiModelProperty("skc")
        private String skc;

        /**
         * sku
         */
        @ApiModelProperty("sku")
        private String sku;

        /**
         * skuSize
         */
        @ApiModelProperty("skuSize")
        private String skuSize;

        /**
         * spu
         */
        @ApiModelProperty("spu")
        private String spu;

        /**
         * 图片链接
         */
        @ApiModelProperty("图片链接")
        private String imgUrl;

        /**
         * 计划采购数
         */
        @ApiModelProperty("计划采购数")
        private Integer planQty;

        /**
         * 采购数
         */
        @ApiModelProperty("采购数")
        private Integer purchaseQty;

        /**
         * 预估单价
         */
        @ApiModelProperty("预估单价")
        private BigDecimal estimatePrice;

        /**
         * 系统工厂当前价
         */
        @ApiModelProperty("系统工厂当前价")
        private BigDecimal systemSupplierPurchasePrice;


        /**
         * 采购价
         */
        @ApiModelProperty("采购价")
        private BigDecimal purchasePrice;

        /**
         * 预计到货日期，默认按当天日期+7天
         */
        @ApiModelProperty("预计到货日期")
        private Date estimateArrivalDate;

        /**
         * 商品标签
         */
        @ApiModelProperty("商品标签")
        private List<String> productLabels;

        /**
         * 现货链接
         */
        @ApiModelProperty("现货链接")
        private String spotUrl;

        /**
         * 平台商品款号
         */
        @ApiModelProperty("平台商品款号")
        private String crossSku;

        /**
         * 未付款数量
         */
        @ApiModelProperty("未付款数量")
        private Integer unpaidQty;

        @ApiModelProperty("下单备注（买手备注）")
        private String orderRemark;

        public BigDecimal getSystemSupplierPurchasePrice() {
            return systemSupplierPurchasePrice;
        }

        public void setSystemSupplierPurchasePrice(BigDecimal systemSupplierPurchasePrice) {
            this.systemSupplierPurchasePrice = systemSupplierPurchasePrice;
        }

        public Integer getUnpaidQty() {
            return unpaidQty;
        }

        public void setUnpaidQty(Integer unpaidQty) {
            this.unpaidQty = unpaidQty;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public List<String> getProductLabels() {
            return productLabels;
        }

        public void setProductLabels(List<String> productLabels) {
            this.productLabels = productLabels;
        }

        public String getSpotUrl() {
            return spotUrl;
        }

        public void setSpotUrl(String spotUrl) {
            this.spotUrl = spotUrl;
        }

        public String getCrossSku() {
            return crossSku;
        }

        public void setCrossSku(String crossSku) {
            this.crossSku = crossSku;
        }

        public Integer getPlanId() {
            return planId;
        }

        public void setPlanId(Integer planId) {
            this.planId = planId;
        }

        public Integer getProductId() {
            return productId;
        }

        public void setProductId(Integer productId) {
            this.productId = productId;
        }

        public Integer getSpecId() {
            return specId;
        }

        public void setSpecId(Integer specId) {
            this.specId = specId;
        }

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public String getImgUrl() {
            return imgUrl;
        }

        public void setImgUrl(String imgUrl) {
            this.imgUrl = imgUrl;
        }

        public Integer getPlanQty() {
            return planQty;
        }

        public void setPlanQty(Integer planQty) {
            this.planQty = planQty;
        }

        public Integer getPurchaseQty() {
            return purchaseQty;
        }

        public void setPurchaseQty(Integer purchaseQty) {
            this.purchaseQty = purchaseQty;
        }

        public BigDecimal getEstimatePrice() {
            return estimatePrice;
        }

        public void setEstimatePrice(BigDecimal estimatePrice) {
            this.estimatePrice = estimatePrice;
        }

        public BigDecimal getPurchasePrice() {
            return purchasePrice;
        }

        public void setPurchasePrice(BigDecimal purchasePrice) {
            this.purchasePrice = purchasePrice;
        }

        public Date getEstimateArrivalDate() {
            return estimateArrivalDate;
        }

        public void setEstimateArrivalDate(Date estimateArrivalDate) {
            this.estimateArrivalDate = estimateArrivalDate;
        }

        public String getSpu() {
            return spu;
        }

        public void setSpu(String spu) {
            this.spu = spu;
        }

        public void setOrderRemark(String orderRemark) {
            this.orderRemark = orderRemark;
        }

        public String getOrderRemark() {
            return orderRemark;
        }

        public String getSkc() {
            return skc;
        }

        public void setSkc(String skc) {
            this.skc = skc;
        }

        public String getSkuSize() {
            return skuSize;
        }

        public void setSkuSize(String skuSize) {
            this.skuSize = skuSize;
        }
    }
}
