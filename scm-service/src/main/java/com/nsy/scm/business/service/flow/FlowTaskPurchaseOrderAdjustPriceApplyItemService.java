package com.nsy.scm.business.service.flow;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.scm.dto.domain.supplier.ExistsProcessingPurchaseOrderAdjustPriceTaskByOrderNoSkcDto;
import com.nsy.api.scm.dto.request.supplier.ExistsProcessingPurchaseOrderAdjustPriceTaskRequest;
import com.nsy.scm.business.domain.flow.PurchaseOrderAdjustPriceApplyDownloadDto;
import com.nsy.scm.business.request.flow.ChangePurchaseOrderAdjustPriceStatusRequest;
import com.nsy.scm.business.request.flow.PurchaseOrderAdjustPriceItemPageRequest;
import com.nsy.scm.business.request.flow.PurchaseOrderAdjustPricePageRequest;
import com.nsy.scm.business.request.flow.RecommendChangeTaskAssigneeRequest;
import com.nsy.scm.business.response.flow.PurchaseOrderAdjustPriceItemPageResponse;
import com.nsy.scm.constant.flow.PurchaseOrderAdjustPriceOperateTypeEnum;
import com.nsy.scm.repository.entity.flow.FlowTaskPurchaseOrderAdjustPriceApplyEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-07-12 11:16
 */
public interface FlowTaskPurchaseOrderAdjustPriceApplyItemService {

    PurchaseOrderAdjustPriceItemPageResponse detail(PurchaseOrderAdjustPriceItemPageRequest request);

    /**
     * 任务转派
     * @param request 请求体
     */
    void changeTaskAssignee(RecommendChangeTaskAssigneeRequest request);

    /**
     * 按实时数据保存明细
     */
    void saveApplyItem(FlowTaskPurchaseOrderAdjustPriceApplyEntity applyEntity, PurchaseOrderAdjustPriceOperateTypeEnum operateType);

    List<ExistsProcessingPurchaseOrderAdjustPriceTaskByOrderNoSkcDto> existsProcessingTaskByOrderNoSkc(ExistsProcessingPurchaseOrderAdjustPriceTaskRequest request);

    void updateDetailsByRequest(ChangePurchaseOrderAdjustPriceStatusRequest request);

    IPage<PurchaseOrderAdjustPriceApplyDownloadDto> queryDetailDownloadData(PurchaseOrderAdjustPricePageRequest pageRequest);
}
