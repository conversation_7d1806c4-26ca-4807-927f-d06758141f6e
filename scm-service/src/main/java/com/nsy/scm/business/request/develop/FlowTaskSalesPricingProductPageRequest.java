package com.nsy.scm.business.request.develop;

import com.nsy.api.scm.dto.request.common.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @ClassName FlowTaskSalesPricingProductPageRequest
 * @create_time 2024/1/28 13:47
 * @描述 基础商品列表
 */
@ApiModel(value = "FlowTaskSalesPricingProductPageRequest", description = "基础商品列表请求体")
public class FlowTaskSalesPricingProductPageRequest extends PageRequest {

    @ApiModelProperty(value = "款式", name = "spu")
    private String spu;

    @ApiModelProperty(value = "所属公司编码", name = "companyCode")
    private String companyCode;
    @ApiModelProperty(value = "是否被删除", name = "isDeleted")
    private Integer isDeleted;

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}
