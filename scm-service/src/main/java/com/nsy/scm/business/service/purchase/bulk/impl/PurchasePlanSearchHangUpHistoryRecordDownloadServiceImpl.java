package com.nsy.scm.business.service.purchase.bulk.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.scm.dto.constant.BigDecimalUtils;
import com.nsy.api.scm.dto.constant.PurchasePlanHangUpRecordConstant;
import com.nsy.api.scm.dto.domain.purchase.bulk.PurchasePlanForHangUpDto;
import com.nsy.api.scm.dto.domain.purchase.bulk.PurchasePlanHangUpHistoryRecordDto;
import com.nsy.api.scm.dto.domain.purchase.bulk.PurchasePlanHangUpPageFilterDto;
import com.nsy.api.scm.dto.request.purchase.bulk.plan.PurchasePlanHangUpPageListRequest;
import com.nsy.api.scm.dto.response.common.PageResponse;
import com.nsy.scm.business.domain.supplier.PurchasePlanHangUpHistoryRecordExport;
import com.nsy.scm.business.manage.peer.product.ProductApiService;
import com.nsy.scm.business.manage.peer.product.request.GetSkcDevelopRequest;
import com.nsy.scm.business.manage.peer.product.response.GetSkcDevelopResponse;
import com.nsy.scm.business.manage.peer.product.response.SkcDevelopDto;
import com.nsy.scm.business.manage.peer.user.UserApiService;
import com.nsy.scm.business.manage.peer.user.dto.SysDepartmentDto;
import com.nsy.scm.business.service.download.IDownloadService;
import com.nsy.scm.business.service.download.base.DownloadRequest;
import com.nsy.scm.business.service.download.base.DownloadResponse;
import com.nsy.scm.business.service.download.base.QuartzDownloadQueueTypeEnum;
import com.nsy.scm.business.service.product.ProductSpecService;
import com.nsy.scm.business.service.purchase.bulk.PurchaseApplyItemService;
import com.nsy.scm.business.service.purchase.bulk.PurchasePlanHangUpRecordService;
import com.nsy.scm.constant.NumberConstant;
import com.nsy.scm.constant.StringConstant;
import com.nsy.scm.constant.bulk.PurchaseOrderTypeEnum;
import com.nsy.scm.repository.cache.SupplierCacheService;
import com.nsy.scm.repository.entity.product.ProductSpecEntity;
import com.nsy.scm.repository.entity.purchase.bulk.PurchaseApplyItemEntity;
import com.nsy.scm.repository.entity.purchase.bulk.PurchasePlanEntity;
import com.nsy.scm.repository.entity.purchase.bulk.PurchasePlanHangUpRecordEntity;
import com.nsy.scm.repository.sql.mapper.purchase.bulk.PurchasePlanMapper;
import com.nsy.scm.utils.JsonMapper;
import com.nsy.scm.utils.SupplierUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 采购计划单挂起历史数据导出
 *
 * <AUTHOR>
 */
@Service
public class PurchasePlanSearchHangUpHistoryRecordDownloadServiceImpl implements IDownloadService {

    @Autowired
    private PurchasePlanMapper purchasePlanMapper;
    @Autowired
    private SupplierCacheService supplierCacheService;
    @Autowired
    private PurchaseApplyItemService purchaseApplyItemService;
    @Autowired
    private PurchasePlanHangUpRecordService purchasePlanHangUpRecordService;
    @Autowired
    private ProductApiService productApiService;
    @Autowired
    private ProductSpecService productSpecService;
    @Autowired
    private UserApiService userApiService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.SCM_EXCEPTION_PLAN_ORDER_HANGUP_HISTORY_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        PurchasePlanHangUpPageListRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), PurchasePlanHangUpPageListRequest.class);
        downloadRequest.setPageIndex(request.getPageIndex());
        downloadRequest.setPageSize(request.getPageSize());
        downloadRequest.setCount(Objects.equals(request.getPageIndex(), 1));
        downloadRequest.buildSpuAutoMatchList(downloadRequest.getSpu());
        downloadRequest.buildSkcAutoMatchList(downloadRequest.getSkc());
        downloadRequest.buildSkuAutoMatchList(downloadRequest.getSku());
        PageResponse<PurchasePlanHangUpHistoryRecordDto> pageResponse = pageListByHangUpForHistory(downloadRequest);
        // 构建数据
        List<PurchasePlanHangUpHistoryRecordExport> resultList = Lists.newArrayListWithExpectedSize(request.getPageSize());
        pageResponse.getContent().forEach(content -> content.getItemList().forEach(item -> {
            PurchasePlanHangUpHistoryRecordExport export = new PurchasePlanHangUpHistoryRecordExport();
            BeanUtils.copyProperties(item, export);
            BeanUtils.copyProperties(content, export);
            export.setDisposeTime(BigDecimalUtils.minutesToHours(export.getDisposeTime(), 1, RoundingMode.HALF_UP));
            export.setIsHangUpDesc(content.getIsHangUp() == 1 ? "是" : "否");
            if (!CollectionUtils.isEmpty(item.getApplyExpectDeliveryDateList())) {
                export.setApplyExpectDeliveryDate(StringUtils.join(item.getApplyExpectDeliveryDateList(), ";"));
            }
            resultList.add(export);
        }));
        response.setTotalCount(pageResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(resultList));
        return response;
    }


    /**
     * 取至：PurchasePlanSearchServiceImpl.pageListByHangUp()
     *
     * @param request
     * @return
     */
    private PageResponse<PurchasePlanHangUpHistoryRecordDto> pageListByHangUpForHistory(PurchasePlanHangUpPageListRequest request) {
        // 缓存，减少重复查询
        Map<String, List<PurchasePlanEntity>> cachePurchasePlanMap = Maps.newHashMap();
        Map<String, List<PurchaseApplyItemEntity>> cachePurchaseApplyItemMap = Maps.newHashMap();
        List<PurchasePlanHangUpPageFilterDto> purchasePlanHangUpPageFilterDtos = purchasePlanMapper.pageListByHangUpForHistoryFilter(request);
        IPage<PurchasePlanHangUpHistoryRecordDto> iPage = purchasePlanMapper.pageListByHangUpForHistory(new Page<>(request.getPageIndex(), request.getPageSize(), request.isCount()), request, purchasePlanHangUpPageFilterDtos);
        PageResponse<PurchasePlanHangUpHistoryRecordDto> pageResponse = new PageResponse<>();
        pageResponse.setTotalCount(iPage.getTotal());
        Map<String, PurchasePlanHangUpRecordEntity> hangUpRecordSuspendsionEntityMap = purchasePlanHangUpRecordService.getCancelHangUpAfterLatestHangUpBySkuAndSupplierIdMap(iPage.getRecords().stream().map(PurchasePlanHangUpHistoryRecordDto::getPlanSku).distinct().collect(Collectors.toList()), null);
        List<PurchasePlanHangUpHistoryRecordDto> list = iPage.getRecords();
        List<String> batchNumberList = list.stream().filter(l -> StringUtils.isNotBlank(l.getBatchNumber())).map(PurchasePlanHangUpHistoryRecordDto::getBatchNumber).distinct().collect(Collectors.toList());
        List<PurchasePlanHangUpRecordEntity> purchasePlanHangUpRecordEntities = purchasePlanHangUpRecordService.getByBatchNumber(batchNumberList);
        GetSkcDevelopResponse skcDevelopResponse = getSkcDevelop(list);
        Set<Integer> dutyDepartmentIds = list.stream().map(PurchasePlanHangUpHistoryRecordDto::getDutyDepartmentId).collect(Collectors.toSet());
        Map<Integer, SysDepartmentDto> firstLevelDepartmentMap = userApiService.getFirstLevelDepartmentByDepartmentId(dutyDepartmentIds);

        pageResponse.setContent(list.stream().map(entity -> {
            PurchasePlanHangUpHistoryRecordDto dto = new PurchasePlanHangUpHistoryRecordDto();
            buildRecordDto(dto, entity, paramsBuilder()
                    .cachePurchasePlanMap(cachePurchasePlanMap)
                    .cachePurchaseApplyItemMap(cachePurchaseApplyItemMap)
                    .hangUpRecordSuspendsionEntityMap(hangUpRecordSuspendsionEntityMap)
                    .purchasePlanHangUpRecordEntities(purchasePlanHangUpRecordEntities)
                    .firstLevelDepartmentMap(firstLevelDepartmentMap));
            SkcDevelopDto skcDevelopDto = skcDevelopResponse.getBySkc(dto.getSkc());
            dto.setDevelopBy(skcDevelopDto.getDevelopBy());
            dto.setDevelopName(skcDevelopDto.getDevelopName());
            return dto;
        }).collect(Collectors.toList()));
        return pageResponse;
    }

    private GetSkcDevelopResponse getSkcDevelop(List<PurchasePlanHangUpHistoryRecordDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new GetSkcDevelopResponse();
        }
        List<String> skcList = list.stream().filter(s -> Strings.isNotBlank(s.getSkc())).map(PurchasePlanHangUpHistoryRecordDto::getSkc).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skcList)) {
            return new GetSkcDevelopResponse();
        }
        GetSkcDevelopRequest request = new GetSkcDevelopRequest();
        request.setSkcList(skcList);
        return productApiService.getSkcDevelop(request);
    }

    private void buildRecordDto(PurchasePlanHangUpHistoryRecordDto dto, PurchasePlanHangUpHistoryRecordDto entity, BuildRecordDtoParams params) {
        BeanUtilsEx.copyProperties(entity, dto);
        dto.setSupplierId(entity.getSupplierId());
        dto.setSupplierName(SupplierUtils.convertSupplierName(supplierCacheService.getSupplierById(entity.getSupplierId())));

        List<PurchasePlanEntity> planList = params.cachePurchasePlanMap.get(dto.getIdStr());
        if (CollectionUtils.isEmpty(planList)) {
            planList = purchasePlanMapper.selectBatchIds(Arrays.stream(dto.getIdStr().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
            params.cachePurchasePlanMap.put(dto.getIdStr(), planList);
        }
        List<PurchaseApplyItemEntity> applyItemList = params.cachePurchaseApplyItemMap.get(dto.getIdStr());
        if (CollectionUtils.isEmpty(applyItemList)) {
            applyItemList = purchaseApplyItemService.listByIds(planList.stream().map(PurchasePlanEntity::getApplyItemId).collect(Collectors.toList()));
            params.cachePurchaseApplyItemMap.put(dto.getIdStr(), applyItemList);
        }
        Map<Date, List<PurchaseApplyItemEntity>> expectedDateMap = applyItemList.stream().filter(t -> Objects.nonNull(t.getExpectedArrivalDate())).collect(Collectors.groupingBy(PurchaseApplyItemEntity::getExpectedArrivalDate));
        dto.setItemList(new ArrayList<>());
        planList.stream().collect(Collectors.groupingBy(plan -> String.format("%s_%s_%s", plan.getSku(), plan.getPlanType(), plan.getSpaceId()))).forEach((k, v) -> {
            PurchasePlanForHangUpDto planDto = new PurchasePlanForHangUpDto();
            PurchasePlanEntity firstPlan = v.get(0);
            planDto.setSku(firstPlan.getSku());
            planDto.setPlanTypeStr(PurchaseOrderTypeEnum.resolveByIntValue(firstPlan.getPlanType()).getDesc());
            planDto.setSpaceName(firstPlan.getSpaceName());
            planDto.setPlanQty(v.stream().mapToInt(PurchasePlanEntity::getPlanQty).sum());
            planDto.setApplyBusinessType(v.stream().map(PurchasePlanEntity::getApplyBusinessType).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.joining(StringConstant.COMMA)));
            //todo 不做删除 旧数据没批次号
            planDto.setLatestCreateDate(Optional.ofNullable(params.hangUpRecordSuspendsionEntityMap.get(String.format(PurchasePlanHangUpRecordConstant.SKU_SUPPLIER_ID_KEY, planDto.getSku(), dto.getSupplierId()))).map(PurchasePlanHangUpRecordEntity::getCreateDate).orElse(null));
            planDto.setCancelHangUpDate(entity.getIsHangUp() == 1 ? "" : DateUtil.format(entity.getCreateDate(), "yyyy-MM-dd HH:mm:ss"));
            planDto.setFirstDepartmentName(Optional.ofNullable(params.firstLevelDepartmentMap.get(entity.getDutyDepartmentId())).map(SysDepartmentDto::getDepartmentName).orElse(StringConstant.BLANK));
            Optional<PurchasePlanHangUpRecordEntity> minEntity = params.purchasePlanHangUpRecordEntities.stream().filter(p -> Objects.equals(p.getBatchNumber(), entity.getBatchNumber()) && Objects.equals(p.getIsHangUp(), NumberConstant.ONE))
                    .min(Comparator.comparingInt(PurchasePlanHangUpRecordEntity::getPlanHangUpRecordId));
            if (minEntity.isPresent()) {
                planDto.setLatestCreateDate(minEntity.get().getCreateDate());
            }
            Optional<PurchasePlanHangUpRecordEntity> maxEntity = params.purchasePlanHangUpRecordEntities.stream().filter(p -> Objects.equals(p.getBatchNumber(), entity.getBatchNumber()) && Objects.equals(p.getIsHangUp(), NumberConstant.ZERO))
                    .max(Comparator.comparingInt(PurchasePlanHangUpRecordEntity::getPlanHangUpRecordId));
            if (maxEntity.isPresent()) {
                planDto.setCancelHangUpDate(DateUtil.format(maxEntity.get().getCreateDate(), "yyyy-MM-dd HH:mm:ss"));
            }
            expectedDateMap.keySet().stream().sorted().forEach(d -> {
                List<Integer> applyItemIds = expectedDateMap.get(d).stream().map(PurchaseApplyItemEntity::getApplyItemId).collect(Collectors.toList());
                int count = v.stream().filter(plan -> applyItemIds.contains(plan.getApplyItemId())).mapToInt(PurchasePlanEntity::getPlanQty).sum();
                if (count > 0) {
                    planDto.addApplyExpectDeliveryDate(String.format("%s(%s)", DateUtil.format(d, "yyyy-MM-dd"), count).trim());
                }
            });
            dto.getItemList().add(planDto);
        });
        List<ProductSpecEntity> productSpecList = productSpecService.findBySpecIdIn(planList.stream().map(PurchasePlanEntity::getSpecId).distinct().collect(Collectors.toList()));
        ProductSpecEntity specEntity = productSpecList.stream().filter(spec -> StringUtils.isNotEmpty(spec.getImageUrl())).findFirst().orElse(null);
        if (specEntity != null) {
            dto.setImageUrl(specEntity.getImageUrl());
        }
    }

    private BuildRecordDtoParams paramsBuilder() {
        return new BuildRecordDtoParams();
    }

    private static class BuildRecordDtoParams {
        private Map<String, List<PurchasePlanEntity>> cachePurchasePlanMap;
        private Map<String, List<PurchaseApplyItemEntity>> cachePurchaseApplyItemMap;
        private Map<String, PurchasePlanHangUpRecordEntity> hangUpRecordSuspendsionEntityMap;
        private List<PurchasePlanHangUpRecordEntity> purchasePlanHangUpRecordEntities;
        private Map<Integer, SysDepartmentDto> firstLevelDepartmentMap;

        public BuildRecordDtoParams cachePurchasePlanMap(Map<String, List<PurchasePlanEntity>> val) {
            cachePurchasePlanMap = val;
            return this;
        }

        public BuildRecordDtoParams cachePurchaseApplyItemMap(Map<String, List<PurchaseApplyItemEntity>> val) {
            cachePurchaseApplyItemMap = val;
            return this;
        }

        public BuildRecordDtoParams hangUpRecordSuspendsionEntityMap(Map<String, PurchasePlanHangUpRecordEntity> val) {
            hangUpRecordSuspendsionEntityMap = val;
            return this;
        }

        public BuildRecordDtoParams purchasePlanHangUpRecordEntities(List<PurchasePlanHangUpRecordEntity> val) {
            purchasePlanHangUpRecordEntities = val;
            return this;
        }

        public BuildRecordDtoParams firstLevelDepartmentMap(Map<Integer, SysDepartmentDto> val) {
            firstLevelDepartmentMap = val;
            return this;
        }
    }
}
