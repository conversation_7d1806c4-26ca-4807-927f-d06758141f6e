package com.nsy.scm.business.service.stock;

import com.nsy.scm.business.manage.peer.omspublish.response.ErpWebsiteInfo;
import com.nsy.scm.business.manage.peer.thirdparty.ThirdPartyApiService;
import com.nsy.scm.business.manage.peer.thirdparty.domain.temu.TemuResponse;
import com.nsy.scm.business.manage.peer.thirdparty.domain.temu.TemuSubOrderForSupplierResponse;
import com.nsy.scm.business.manage.peer.thirdparty.domain.temu.TemuSubOrderResponse;
import com.nsy.scm.business.manage.peer.thirdparty.request.temu.GetGoodsSalesRequest;
import com.nsy.scm.business.manage.peer.thirdparty.request.temu.GetPurchaseorderRequest;
import com.nsy.scm.business.manage.peer.thirdparty.request.temu.PurchaseorderRequest;
import com.nsy.scm.business.manage.peer.thirdparty.request.temu.WebsiteConfig;
import com.nsy.scm.business.manage.peer.thirdparty.request.temu.WebsiteSynchronizeTemuDataRequest;
import org.springframework.stereotype.Service;

import javax.inject.Inject;


@Service
public class TemuApiService {
    @Inject
    private ThirdPartyApiService thirdPartyApiService;

    public TemuSubOrderResponse getGetGoodsSalesReq(ErpWebsiteInfo erpWebsiteInfo, GetGoodsSalesRequest getGoodsSalesReq) {
        WebsiteConfig websiteConfig = new WebsiteConfig();
        websiteConfig.setWebsiteKey(erpWebsiteInfo.getWebsiteKey());
        websiteConfig.setAppSecret(erpWebsiteInfo.getAppSecret());
        websiteConfig.setWebsiteToken(erpWebsiteInfo.getWebsiteToken());
        websiteConfig.setWebsiteUrl(erpWebsiteInfo.getWebsiteUrl());
        WebsiteSynchronizeTemuDataRequest<GetGoodsSalesRequest> request = new WebsiteSynchronizeTemuDataRequest<>();
        request.setWebsiteConfig(websiteConfig);
        request.setData(getGoodsSalesReq);
        return thirdPartyApiService.getGetGoodsSalesReq(request);
    }


    public TemuResponse apply(ErpWebsiteInfo erpWebsiteInfo, PurchaseorderRequest purchaseorderRequest) {
        WebsiteConfig websiteConfig = new WebsiteConfig();
        websiteConfig.setWebsiteKey(erpWebsiteInfo.getWebsiteKey());
        websiteConfig.setAppSecret(erpWebsiteInfo.getAppSecret());
        websiteConfig.setWebsiteToken(erpWebsiteInfo.getWebsiteToken());
        websiteConfig.setWebsiteUrl(erpWebsiteInfo.getWebsiteUrl());
        WebsiteSynchronizeTemuDataRequest<PurchaseorderRequest> request = new WebsiteSynchronizeTemuDataRequest<>();
        request.setWebsiteConfig(websiteConfig);
        request.setData(purchaseorderRequest);
        return thirdPartyApiService.temuApply(request);
    }


    public TemuSubOrderForSupplierResponse getTemuSubOrderForSupplierResponse(ErpWebsiteInfo erpWebsiteInfo, GetPurchaseorderRequest getPurchaseorderRequest) {
        WebsiteConfig websiteConfig = new WebsiteConfig();
        websiteConfig.setWebsiteKey(erpWebsiteInfo.getWebsiteKey());
        websiteConfig.setAppSecret(erpWebsiteInfo.getAppSecret());
        websiteConfig.setWebsiteToken(erpWebsiteInfo.getWebsiteToken());
        websiteConfig.setWebsiteUrl(erpWebsiteInfo.getWebsiteUrl());
        WebsiteSynchronizeTemuDataRequest<GetPurchaseorderRequest> request = new WebsiteSynchronizeTemuDataRequest<>();
        request.setWebsiteConfig(websiteConfig);
        request.setData(getPurchaseorderRequest);
        return thirdPartyApiService.getTemuSubOrderForSupplierResponse(request);
    }
}
