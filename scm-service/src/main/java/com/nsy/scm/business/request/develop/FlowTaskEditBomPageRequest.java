package com.nsy.scm.business.request.develop;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.scm.dto.constant.ProductCodePrefixEnum;
import com.nsy.api.scm.dto.request.common.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@ApiModel(value = "FlowTaskEditBomPageRequest", description = "bom修改任务列表")
public class FlowTaskEditBomPageRequest extends PageRequest {

    @ApiModelProperty(value = "商品编码", name = "spu")
    private String spu;

    private List<String> spuAutoMatchList;

    @ApiModelProperty(value = "类别id", name = "scmCategoryIds")
    private List<Integer> scmCategoryIds;

    @ApiModelProperty(value = "打版供应商id", name = "productSupplierId")
    private Integer productSupplierId;

    @ApiModelProperty(value = "采购员id", name = "buyerUserId")
    private Integer buyerUserId;

    @ApiModelProperty(value = "样版跟单员id", name = "modelMerchandiserEmpId")
    private Integer modelMerchandiserEmpId;

    @ApiModelProperty(value = "WAIT_SUBMIT--待提交，WAIT_PICK_AUDIT--审核待领取,WAIT_AUDIT--待审核，COMPLETE--完成，CANCEL--取消", name = "status")
    private List<String> statusList;

    @ApiModelProperty(value = "类型：改版,加色、正常修改、加色后修改", name = "type")
    private String type;

    @ApiModelProperty(value = "类型列表", name = "typeList")
    private List<String> typeList;

    @ApiModelProperty(value = "部门id", name = "departmentId")
    private Integer departmentId;

    private String requirementType;

    @ApiModelProperty(value = "公司code", name = "companyCode")
    private String companyCode;

    @ApiModelProperty(value = "任务生成开始时间", name = "createDateStart")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDateStart;

    @ApiModelProperty(value = "任务生成结束时间", name = "createDateEnd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDateEnd;

    @ApiModelProperty(value = "任务结束时间开始时间", name = "endDateStart")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDateStart;

    @ApiModelProperty(value = "任务结束时间结束时间", name = "endDateEnd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDateEnd;

    @ApiModelProperty(value = "完成：COMPLETE 取消：CANCEL", name = "status")
    private String status;

    @ApiModelProperty(value = "核价人", name = "submitUserIds")
    private List<String> submitUserIds;

    @ApiModelProperty(value = "审核人id列表", name = "auditUserIds")
    private List<String> auditUserIds;

    @ApiModelProperty(value = "当前处理人id列表", name = "handleUserId")
    private List<String> handleUserIds;

    @ApiModelProperty(value = "发起人", name = "offeringUserIds")
    private List<String> offeringUserIds;

    @ApiModelProperty(value = "核价开始时间", name = "createDateStart")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitDateStart;

    @ApiModelProperty(value = "核价结束时间", name = "createDateEnd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitDateEnd;

    @ApiModelProperty(value = "来源类型，1 采购价申请，2 推荐面料，3 加色任务， 4 报核价任务", name = "sourceType")
    private Integer sourceType;

    @ApiModelProperty(value = "是否工厂驳回，1是，0否", name = "isSupplierReject")
    private Integer isSupplierReject;

    @ApiModelProperty(value = "商品标签id列表", name = "productLabelIdList")
    private List<Integer> productLabelIdList;

    private List<Integer> ids;

    private Integer hangUp;

    private String developDepartment;

    private Integer isDokotoo;

    @ApiModelProperty(value = "核价模式编码 1：毛衣模式，0：常规模式", name = "corePriceModeCode")
    private Integer corePriceModeCode;

    @ApiModelProperty(value = "尺码模式 0-单尺码模式 1-多尺码模式", name = "sizeRangeMode")
    private Integer sizeRangeMode;

    private Integer filterUserTask;
    @ApiModelProperty(value = "核价原因", name = "editBomReasonList")
    private List<String> editBomReasonList;

    @ApiModelProperty(value = "系列Id集合", name = "bdSeriesIds")
    private List<Integer> bdSeriesIds;

    @ApiModelProperty(value = "商品风格ID集合", name = "productStyleIds")
    private List<Integer> productStyleIds;

    @ApiModelProperty(value = "是否自动完成 1：是，0：否", name = "autoCompleteTask")
    private Integer autoCompleteTask;

    public String getSpu() {
        this.spuAutoMatchList = Optional.ofNullable(ProductCodePrefixEnum.buildLikeProductCodeList(spu)).orElse(Collections.emptyList());
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public List<String> getSpuAutoMatchList() {
        return spuAutoMatchList;
    }

    public void setSpuAutoMatchList(List<String> spuAutoMatchList) {
        this.spuAutoMatchList = spuAutoMatchList;
    }

    public Integer getProductSupplierId() {
        return productSupplierId;
    }

    public void setProductSupplierId(Integer productSupplierId) {
        this.productSupplierId = productSupplierId;
    }

    public Integer getBuyerUserId() {
        return buyerUserId;
    }

    public void setBuyerUserId(Integer buyerUserId) {
        this.buyerUserId = buyerUserId;
    }

    public Integer getModelMerchandiserEmpId() {
        return modelMerchandiserEmpId;
    }

    public void setModelMerchandiserEmpId(Integer modelMerchandiserEmpId) {
        this.modelMerchandiserEmpId = modelMerchandiserEmpId;
    }


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<Integer> getScmCategoryIds() {
        return scmCategoryIds;
    }

    public void setScmCategoryIds(List<Integer> scmCategoryIds) {
        this.scmCategoryIds = scmCategoryIds;
    }

    public List<String> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<String> statusList) {
        this.statusList = statusList;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public void setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public void setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
    }

    public Date getEndDateStart() {
        return endDateStart;
    }

    public void setEndDateStart(Date endDateStart) {
        this.endDateStart = endDateStart;
    }

    public Date getEndDateEnd() {
        return endDateEnd;
    }

    public void setEndDateEnd(Date endDateEnd) {
        this.endDateEnd = endDateEnd;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<String> getSubmitUserIds() {
        return submitUserIds;
    }

    public void setSubmitUserIds(List<String> submitUserIds) {
        this.submitUserIds = submitUserIds;
    }

    public List<String> getOfferingUserIds() {
        return offeringUserIds;
    }

    public void setOfferingUserIds(List<String> offeringUserIds) {
        this.offeringUserIds = offeringUserIds;
    }

    public String getRequirementType() {
        return requirementType;
    }

    public void setRequirementType(String requirementType) {
        this.requirementType = requirementType;
    }

    public Date getSubmitDateStart() {
        return submitDateStart;
    }

    public void setSubmitDateStart(Date submitDateStart) {
        this.submitDateStart = submitDateStart;
    }

    public Date getSubmitDateEnd() {
        return submitDateEnd;
    }

    public void setSubmitDateEnd(Date submitDateEnd) {
        this.submitDateEnd = submitDateEnd;
    }

    public Integer getHangUp() {
        return hangUp;
    }

    public void setHangUp(Integer hangUp) {
        this.hangUp = hangUp;
    }

    public String getDevelopDepartment() {
        return developDepartment;
    }

    public void setDevelopDepartment(String developDepartment) {
        this.developDepartment = developDepartment;
    }

    public Integer getIsDokotoo() {
        return isDokotoo;
    }

    public void setIsDokotoo(Integer isDokotoo) {
        this.isDokotoo = isDokotoo;
    }

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public Integer getCorePriceModeCode() {
        return corePriceModeCode;
    }

    public void setCorePriceModeCode(Integer corePriceModeCode) {
        this.corePriceModeCode = corePriceModeCode;
    }

    public Integer getSizeRangeMode() {
        return sizeRangeMode;
    }

    public void setSizeRangeMode(Integer sizeRangeMode) {
        this.sizeRangeMode = sizeRangeMode;
    }

    public Integer getFilterUserTask() {
        return filterUserTask;
    }

    public void setFilterUserTask(Integer filterUserTask) {
        this.filterUserTask = filterUserTask;
    }

    public List<String> getAuditUserIds() {
        return auditUserIds;
    }

    public void setAuditUserIds(List<String> auditUserIds) {
        this.auditUserIds = auditUserIds;
    }

    public List<String> getHandleUserIds() {
        return handleUserIds;
    }

    public void setHandleUserIds(List<String> handleUserIds) {
        this.handleUserIds = handleUserIds;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public Integer getIsSupplierReject() {
        return isSupplierReject;
    }

    public void setIsSupplierReject(Integer isSupplierReject) {
        this.isSupplierReject = isSupplierReject;
    }

    public List<String> getTypeList() {
        return typeList;
    }

    public void setTypeList(List<String> typeList) {
        this.typeList = typeList;
    }

    public List<Integer> getProductLabelIdList() {
        return productLabelIdList;
    }

    public void setProductLabelIdList(List<Integer> productLabelIdList) {
        this.productLabelIdList = productLabelIdList;
    }

    public List<String> getEditBomReasonList() {
        return editBomReasonList;
    }

    public void setEditBomReasonList(List<String> editBomReasonList) {
        this.editBomReasonList = editBomReasonList;
    }

    public List<Integer> getBdSeriesIds() {
        return bdSeriesIds;
    }

    public void setBdSeriesIds(List<Integer> bdSeriesIds) {
        this.bdSeriesIds = bdSeriesIds;
    }

    public List<Integer> getProductStyleIds() {
        return productStyleIds;
    }

    public void setProductStyleIds(List<Integer> productStyleIds) {
        this.productStyleIds = productStyleIds;
    }

    public Integer getAutoCompleteTask() {
        return autoCompleteTask;
    }

    public void setAutoCompleteTask(Integer autoCompleteTask) {
        this.autoCompleteTask = autoCompleteTask;
    }
}
