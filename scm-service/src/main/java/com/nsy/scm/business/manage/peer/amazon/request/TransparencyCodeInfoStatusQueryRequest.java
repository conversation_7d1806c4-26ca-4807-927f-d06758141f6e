package com.nsy.scm.business.manage.peer.amazon.request;

import com.nsy.scm.constant.purchase.fifo.TransparencyCodeInfoBusinessTypeEnum;
import com.nsy.scm.constant.purchase.fifo.TransparencyCodeStatusEnum;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/26 11:31
 */
public class TransparencyCodeInfoStatusQueryRequest extends TransparencyCodeInfoOccupyRequest {

    private List<Integer> statusList;

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public static TransparencyCodeInfoStatusQueryRequest buildDefaultRequest(String businessNo, String sellerSku, Integer storeId) {
        TransparencyCodeInfoStatusQueryRequest request = new TransparencyCodeInfoStatusQueryRequest();
        request.setBusinessNo(businessNo);
        request.setSellerSku(sellerSku);
        request.setStoreId(storeId);

        request.setBusinessType(TransparencyCodeInfoBusinessTypeEnum.PURHCASE_ORDER.name());
        request.setStatusList(Arrays.asList(TransparencyCodeStatusEnum.EFFECT.getCode(), TransparencyCodeStatusEnum.OCCUPIED.getCode()));
        return request;
    }
}
