package com.nsy.scm.business.service.supplier.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.beust.jcommander.Strings;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.scm.dto.domain.supplier.SupplierTaxLogPageDto;
import com.nsy.api.scm.dto.enumstable.material.supplier.SupplierTaxLogOperateTypeEnum;
import com.nsy.api.scm.dto.request.supplier.SupplierTaxLogPageRequest;
import com.nsy.api.scm.dto.response.common.PageResponse;
import com.nsy.scm.business.service.supplier.SupplierTaxLogService;
import com.nsy.scm.repository.dao.supplier.SupplierTaxLogDao;
import com.nsy.scm.repository.entity.supplier.SupplierTaxLogEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商税务信息 - 操作日志  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Service
public class SupplierTaxLogServiceImpl implements SupplierTaxLogService {
    @Autowired
    private SupplierTaxLogDao supplierTaxLogDao;
    @Autowired
    LoginInfoService loginInfoService;

    @Override
    public PageResponse<SupplierTaxLogPageDto> page(SupplierTaxLogPageRequest request) {
        PageResponse<SupplierTaxLogPageDto> pageResponse = new PageResponse<>();
        if (request.getTaxId() == null) {
            pageResponse.setContent(ListUtil.empty());
            pageResponse.setTotalCount(0);
            return pageResponse;
        }
        LambdaQueryWrapper<SupplierTaxLogEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierTaxLogEntity::getTaxId, request.getTaxId());
        queryWrapper.orderByDesc(SupplierTaxLogEntity::getLogId);
        Page<SupplierTaxLogEntity> iPage = supplierTaxLogDao.page(new Page<>(request.getPageIndex(), request.getPageSize()), queryWrapper);
        // 转换dto
        pageResponse.setTotalCount(iPage.getTotal());
        pageResponse.setContent(iPage.getRecords().stream().map(entity -> {
            SupplierTaxLogPageDto dto = new SupplierTaxLogPageDto();
            BeanUtilsEx.copyProperties(entity, dto);
            SupplierTaxLogOperateTypeEnum supplierTaxLogOperateTypeEnum = SupplierTaxLogOperateTypeEnum.getByValue(dto.getOperateType());
            if (supplierTaxLogOperateTypeEnum != null) {
                dto.setOperateTypeName(supplierTaxLogOperateTypeEnum.getName());
            }
            return dto;
        }).collect(Collectors.toList()));
        return pageResponse;
    }

    @Override
    public void saveLog(Integer taxId, SupplierTaxLogOperateTypeEnum logOptType, String content) {
        SupplierTaxLogEntity saveEntity = new SupplierTaxLogEntity();
        saveEntity.setTaxId(taxId);
        saveEntity.setOperateType(logOptType.getName());
        saveEntity.setContent(content);
        saveEntity.setOperatorEmpCode(loginInfoService.getUserCode());
        saveEntity.setOperatorEmpName(loginInfoService.getName());
        saveEntity.setCreateBy(loginInfoService.getName());
        saveEntity.setUpdateBy(loginInfoService.getName());
        supplierTaxLogDao.save(saveEntity);
    }


    @Override
    public void saveLog(List<String> logList, Integer taxId) {
        if (logList.isEmpty()) {
            return;
        }
        // 日志每5个分一组
        List<List<String>> partitionList = Lists.partition(logList, 5);
        List<SupplierTaxLogEntity> saveList = partitionList.stream().map(s -> {
            SupplierTaxLogEntity saveEntity = new SupplierTaxLogEntity();
            saveEntity.setTaxId(taxId);
            saveEntity.setOperateType(SupplierTaxLogOperateTypeEnum.SUPPLEMENTED_TAX_INFO.getName());
            saveEntity.setContent(Strings.join(",", s));
            saveEntity.setIp(loginInfoService.getIpAddress());
            saveEntity.setOperatorEmpCode(loginInfoService.getUserCode());
            saveEntity.setOperatorEmpName(loginInfoService.getName());
            return saveEntity;
        }).collect(Collectors.toList());
        supplierTaxLogDao.saveBatch(saveList, 100);
    }

    @Override
    public void saveLog(List<String> logList, Integer taxId, String operateType) {
        List<SupplierTaxLogEntity> saveList = logList.stream().map(l -> {
            SupplierTaxLogEntity saveEntity = new SupplierTaxLogEntity();
            saveEntity.setTaxId(taxId);
            saveEntity.setOperateType(operateType);
            saveEntity.setContent(l);
            saveEntity.setIp(loginInfoService.getIpAddress());
            saveEntity.setOperatorEmpCode(loginInfoService.getUserCode());
            saveEntity.setOperatorEmpName(loginInfoService.getName());
            return saveEntity;
        }).collect(Collectors.toList());
        supplierTaxLogDao.saveBatch(saveList, 100);
    }
}
