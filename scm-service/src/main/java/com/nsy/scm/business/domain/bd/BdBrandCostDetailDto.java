package com.nsy.scm.business.domain.bd;

import java.math.BigDecimal;

/**
 * 品牌费用细项详情
 * <AUTHOR>
 * @create 2024-03-18 11:22
 */
public class BdBrandCostDetailDto {

    /**
     * 品牌id
     */
    private Integer brandId;
    /**
     * 是否品牌级别：0-否；1-是
     */
    private Integer type;
    /**
     * 品牌级别费用
     */
    private BigDecimal cost;
    /**
     * 一级费用项目：1-主唛；2-吊牌；3-包装袋
     */
    private Integer firstLevelCost;
    /**
     * 二级费用id
     */
    private Integer secondLevelCostId;
    /**
     * 材料费用
     */
    private BigDecimal materialsCost;
    /**
     * 人工费用
     */
    private BigDecimal laborCost;

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    public Integer getFirstLevelCost() {
        return firstLevelCost;
    }

    public void setFirstLevelCost(Integer firstLevelCost) {
        this.firstLevelCost = firstLevelCost;
    }

    public Integer getSecondLevelCostId() {
        return secondLevelCostId;
    }

    public void setSecondLevelCostId(Integer secondLevelCostId) {
        this.secondLevelCostId = secondLevelCostId;
    }

    public BigDecimal getMaterialsCost() {
        return materialsCost;
    }

    public void setMaterialsCost(BigDecimal materialsCost) {
        this.materialsCost = materialsCost;
    }

    public BigDecimal getLaborCost() {
        return laborCost;
    }

    public void setLaborCost(BigDecimal laborCost) {
        this.laborCost = laborCost;
    }
}
