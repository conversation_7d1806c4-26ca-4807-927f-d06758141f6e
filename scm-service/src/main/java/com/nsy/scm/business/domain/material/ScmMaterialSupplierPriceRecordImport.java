package com.nsy.scm.business.domain.material;

import com.alibaba.excel.annotation.ExcelProperty;
import com.nsy.api.scm.dto.domain.supplier.SupplierDto;
import com.nsy.scm.repository.entity.MaterialEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * scm物料列表面料价格导入
 *
 * <AUTHOR>
 * @date 2023/10/05 09:47
 */
public class ScmMaterialSupplierPriceRecordImport {

    @ExcelProperty("供应商面料id")
    @NotBlank(message = "供应商面料id不能为空")
    private Integer supplierInfoId;

    @ExcelProperty("面料统称")
    private String materialName;

    @ExcelProperty("供应商面料名称")
    private String supplierMaterialName;

    @ExcelProperty("面料供应商简称")
    private String supplierShortName;

    @ExcelProperty("当前面料足米价")
    @NotNull(message = "当前面料足米价不能为空")
    private BigDecimal currentWholePrice;

    @ExcelProperty("当前面料公斤价")
    private BigDecimal currentUnitPrice;

    @ExcelProperty("其他说明")
    private String otherExplain;

    @ExcelProperty("是否同步色卡价格")
    private String isSyncColorCardPrice;

    /**
     * 旧足米价
     */
    private BigDecimal oldWholePrice;

    /**
     * 旧公斤价
     */
    private BigDecimal oldUnitPrice;

    /**
     * 价格是否变更
     */
    private boolean priceChanged;

    /**
     * 是否需要审核
     */
    private boolean neededReview;

    /**
     * 面料商ID
     */
    private Integer supplierId;
    /**
     * 面料商信息
     */
    private SupplierDto supplierDto;

    /**
     * 物料类型
     */
    private String materialCategory;

    /**
     * 公司物料，物料大分类
     */
    private String fabricMaterialType;

    /**
     * 公司物料信息
     */
    private MaterialEntity material;

    /**
     * 空差
     */
    private BigDecimal emptyDifferenceDesc;

    /**
     * 错误信息
     */
    private String errorMsg;

    public Integer getSupplierInfoId() {
        return supplierInfoId;
    }

    public void setSupplierInfoId(Integer supplierInfoId) {
        this.supplierInfoId = supplierInfoId;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getSupplierMaterialName() {
        return supplierMaterialName;
    }

    public void setSupplierMaterialName(String supplierMaterialName) {
        this.supplierMaterialName = supplierMaterialName;
    }

    public String getSupplierShortName() {
        return supplierShortName;
    }

    public void setSupplierShortName(String supplierShortName) {
        this.supplierShortName = supplierShortName;
    }

    public BigDecimal getCurrentWholePrice() {
        return currentWholePrice;
    }

    public void setCurrentWholePrice(BigDecimal currentWholePrice) {
        this.currentWholePrice = currentWholePrice;
    }

    public BigDecimal getCurrentUnitPrice() {
        return currentUnitPrice;
    }

    public void setCurrentUnitPrice(BigDecimal currentUnitPrice) {
        this.currentUnitPrice = currentUnitPrice;
    }

    public String getOtherExplain() {
        return otherExplain;
    }

    public void setOtherExplain(String otherExplain) {
        this.otherExplain = otherExplain;
    }

    public BigDecimal getOldWholePrice() {
        return oldWholePrice;
    }

    public void setOldWholePrice(BigDecimal oldWholePrice) {
        this.oldWholePrice = oldWholePrice;
    }

    public BigDecimal getOldUnitPrice() {
        return oldUnitPrice;
    }

    public void setOldUnitPrice(BigDecimal oldUnitPrice) {
        this.oldUnitPrice = oldUnitPrice;
    }

    public boolean isPriceChanged() {
        return priceChanged;
    }

    public void setPriceChanged(boolean priceChanged) {
        this.priceChanged = priceChanged;
    }

    public boolean isNeededReview() {
        return neededReview;
    }

    public void setNeededReview(boolean neededReview) {
        this.neededReview = neededReview;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public SupplierDto getSupplierDto() {
        return supplierDto;
    }

    public void setSupplierDto(SupplierDto supplierDto) {
        this.supplierDto = supplierDto;
    }

    public String getMaterialCategory() {
        return materialCategory;
    }

    public void setMaterialCategory(String materialCategory) {
        this.materialCategory = materialCategory;
    }

    public String getFabricMaterialType() {
        return fabricMaterialType;
    }

    public void setFabricMaterialType(String fabricMaterialType) {
        this.fabricMaterialType = fabricMaterialType;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getIsSyncColorCardPrice() {
        return isSyncColorCardPrice;
    }

    public void setIsSyncColorCardPrice(String isSyncColorCardPrice) {
        this.isSyncColorCardPrice = isSyncColorCardPrice;
    }

    public MaterialEntity getMaterial() {
        return material;
    }

    public void setMaterial(MaterialEntity material) {
        this.material = material;
    }

    public BigDecimal getEmptyDifferenceDesc() {
        return emptyDifferenceDesc;
    }

    public void setEmptyDifferenceDesc(BigDecimal emptyDifferenceDesc) {
        this.emptyDifferenceDesc = emptyDifferenceDesc;
    }
}
