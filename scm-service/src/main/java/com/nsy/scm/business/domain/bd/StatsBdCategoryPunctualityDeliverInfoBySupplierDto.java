package com.nsy.scm.business.domain.bd;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 根据供应商 统计品类准交信息
 * <AUTHOR>
 */
public class StatsBdCategoryPunctualityDeliverInfoBySupplierDto {

    @ApiModelProperty(value = "供应商id", name = "supplierId")
    private Integer supplierId;

    @ApiModelProperty(value = "供应链分类id", name = "scmCategoryId")
    private Integer scmCategoryId;

    @ApiModelProperty(value = "月份", name = "productionMonth")
    private String productionMonth;

    @ApiModelProperty(value = "平均周期", name = "averagePeriod")
    private BigDecimal averagePeriod;

    @ApiModelProperty(value = "平均准交率", name = "averagePunctualityDeliverRate")
    private BigDecimal averagePunctualityDeliverRate;

    public BigDecimal getAveragePeriod() {
        return averagePeriod;
    }

    public void setAveragePeriod(BigDecimal averagePeriod) {
        this.averagePeriod = averagePeriod;
    }

    public BigDecimal getAveragePunctualityDeliverRate() {
        return averagePunctualityDeliverRate;
    }

    public void setAveragePunctualityDeliverRate(BigDecimal averagePunctualityDeliverRate) {
        this.averagePunctualityDeliverRate = averagePunctualityDeliverRate;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getScmCategoryId() {
        return scmCategoryId;
    }

    public void setScmCategoryId(Integer scmCategoryId) {
        this.scmCategoryId = scmCategoryId;
    }

    public String getProductionMonth() {
        return productionMonth;
    }

    public void setProductionMonth(String productionMonth) {
        this.productionMonth = productionMonth;
    }
}
