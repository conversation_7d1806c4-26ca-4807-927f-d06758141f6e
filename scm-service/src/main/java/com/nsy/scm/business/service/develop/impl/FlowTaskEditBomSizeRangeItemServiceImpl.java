package com.nsy.scm.business.service.develop.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.scm.business.service.develop.FlowTaskEditBomSizeRangeItemService;
import com.nsy.scm.repository.dao.develop.FlowTaskEditBomSizeRangeItemDao;
import com.nsy.scm.repository.entity.bom.ProductBomSizeRangeItemEntity;
import com.nsy.scm.repository.entity.develop.FlowTaskEditBomSizeRangeEntity;
import com.nsy.scm.repository.entity.develop.FlowTaskEditBomSizeRangeItemEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 针对表【核价任务尺码段明细表】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Service
public class FlowTaskEditBomSizeRangeItemServiceImpl implements FlowTaskEditBomSizeRangeItemService {

    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private FlowTaskEditBomSizeRangeItemDao flowTaskEditBomSizeRangeItemDao;

    @Override
    public List<FlowTaskEditBomSizeRangeItemEntity> findBySizeRangeIdList(List<Integer> sizeRangeIdList) {
        if (CollectionUtils.isEmpty(sizeRangeIdList)) {
            return Collections.emptyList();
        }
        return flowTaskEditBomSizeRangeItemDao.list(Wrappers.<FlowTaskEditBomSizeRangeItemEntity>lambdaQuery().in(FlowTaskEditBomSizeRangeItemEntity::getSizeRangeId, sizeRangeIdList));
    }

    @Override
    public List<FlowTaskEditBomSizeRangeItemEntity> findByTaskId(Integer taskId, String taskType) {
        return flowTaskEditBomSizeRangeItemDao.list(Wrappers.<FlowTaskEditBomSizeRangeItemEntity>lambdaQuery().eq(FlowTaskEditBomSizeRangeItemEntity::getTaskId, taskId).eq(FlowTaskEditBomSizeRangeItemEntity::getTaskType, taskType));
    }

    @Override
    public void deleteByIds(List<Integer> sizeRangeItemIds) {
        if (CollectionUtils.isEmpty(sizeRangeItemIds)) {
            return;
        }
        flowTaskEditBomSizeRangeItemDao.removeByIds(sizeRangeItemIds);
    }

    @Override
    public void buildFlowTaskEditBomSizeRangeItemFromBom(FlowTaskEditBomSizeRangeEntity editBomSizeRange, List<ProductBomSizeRangeItemEntity> productBomSizeRangeItemEntities) {
        if (CollectionUtils.isEmpty(productBomSizeRangeItemEntities)) {
            return;
        }
        List<FlowTaskEditBomSizeRangeItemEntity> editBomSizeRangeItemList = productBomSizeRangeItemEntities.stream().collect(Collectors.toMap(ProductBomSizeRangeItemEntity::getSize, Function.identity(), (k1, k2) -> k1)).values().stream().map(item -> {
            FlowTaskEditBomSizeRangeItemEntity entity = new FlowTaskEditBomSizeRangeItemEntity();
            entity.setTaskId(editBomSizeRange.getTaskId());
            entity.setTaskType(editBomSizeRange.getTaskType());
            entity.setProductId(editBomSizeRange.getProductId());
            entity.setSizeRangeId(editBomSizeRange.getSizeRangeId());
            entity.setSize(item.getSize());
            entity.setRemark(item.getRemark());
            entity.setCreateBy(loginInfoService.getName());
            entity.setUpdateBy(loginInfoService.getName());
            return entity;
        }).collect(Collectors.toList());
        flowTaskEditBomSizeRangeItemDao.saveOrUpdateBatch(editBomSizeRangeItemList);
    }

    @Override
    public void saveOrUpdateEntityList(List<FlowTaskEditBomSizeRangeItemEntity> sizeRangeItemList) {
        if (CollectionUtils.isEmpty(sizeRangeItemList)) {
            return;
        }
        flowTaskEditBomSizeRangeItemDao.saveOrUpdateBatch(sizeRangeItemList);
    }
}
