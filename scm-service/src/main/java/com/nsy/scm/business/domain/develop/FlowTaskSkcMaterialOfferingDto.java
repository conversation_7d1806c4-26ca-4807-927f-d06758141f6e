package com.nsy.scm.business.domain.develop;

import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.scm.dto.constant.BigDecimalUtils;
import com.nsy.scm.business.domain.develop.editbom.FlowTaskSkcMaterialUsePartDto;
import com.nsy.scm.business.domain.develop.editbom.FlowTaskSkcSizeRangeMaterialOfferingDto;
import com.nsy.scm.constant.NumberConstant;
import com.nsy.scm.constant.StringConstant;
import com.nsy.scm.enumstable.price.editbom.FlowTaskEditBomStatusEnum;
import com.nsy.scm.enumstable.product.bom.MaterialTypeEnum;
import com.nsy.scm.repository.entity.bom.ProductSkcBomMaterialOfferingEntity;
import com.nsy.scm.repository.entity.develop.FlowTaskSkcMaterialOfferingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 任务skc面料报价明细Dto
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@ApiModel(value = "FlowTaskSkcMaterialOfferingDto", description = "任务skc面料报价明细Dto")
public class FlowTaskSkcMaterialOfferingDto {

    @ApiModelProperty(value = "主键", name = "flowTaskSkcMaterialOfferingId")
    private Integer flowTaskSkcMaterialOfferingId;

    @ApiModelProperty(value = "skc", name = "skc")
    private String skc;

    @ApiModelProperty(value = "面料类型：FABRIC面料；SIDE_FABRIC辅料；AUXILIARY_FABRIC辅面料", name = "materialType")
    private String materialType;

    @ApiModelProperty(value = "面料类型中文：FABRIC面料；SIDE_FABRIC辅料；AUXILIARY_FABRIC辅面料", name = "materialTypeCh")
    private String materialTypeCh;

    @ApiModelProperty(value = "面料类型排序", name = "materialTypeOrder")
    private Integer materialTypeOrder;

    @ApiModelProperty(value = "面料价格展示字段", name = "materialColorCardPriceType")
    private String materialColorCardPriceType;

    @ApiModelProperty(value = "供应商面料id", name = "materialSupplierInfoId")
    private Integer materialSupplierInfoId;

    @ApiModelProperty(value = "面料id", name = "materialId")
    private Integer materialId;

    @ApiModelProperty(value = "色卡id", name = "colorCardId")
    private Integer colorCardId;

    @ApiModelProperty(value = "图片url", name = "imgUrl")
    private String imgUrl;

    @ApiModelProperty(value = "供应商面料名称", name = "supplierMaterialName")
    private String supplierMaterialName;

    @ApiModelProperty(value = "供应商名称", name = "supplierName")
    private String supplierName;

    @ApiModelProperty(value = "供应商简称", name = "supplierShortName")
    private String supplierShortName;

    @ApiModelProperty(value = "联系方式姓名", name = "contactsName")
    private String contactsName;

    @ApiModelProperty(value = "联系方式电话", name = "contactsPhone")
    private String contactsPhone;

    @ApiModelProperty("面料商标识，字典对应的中文")
    private String supplierIdentificationDesc;

    @ApiModelProperty(value = "花型颜色", name = "colorDesc")
    private String colorDesc;

    @ApiModelProperty(value = "面料商色号/花型号", name = "colorNumber")
    private String colorNumber;

    @ApiModelProperty("测缩方式，字典对应的中文")
    private String measureShrinkModeDesc;

    @ApiModelProperty("横缩率（%）")
    private BigDecimal transverseShrinkRate;
    @ApiModelProperty("直缩率（%）")
    private BigDecimal directShrinkRate;
    @ApiModelProperty(value = "克重(克)", name = "grammage")
    private BigDecimal grammage;
    @ApiModelProperty(value = "净幅宽", name = "netBreadth")
    private BigDecimal netBreadth;
    @ApiModelProperty(value = "毛幅宽", name = "grossBreadth")
    private BigDecimal grossBreadth;

    @ApiModelProperty(value = "成分", name = "ingredientDesc")
    private String ingredientDesc;

    @ApiModelProperty(value = "原单价-不参与计算的", name = "originalUnitPrice")
    private BigDecimal originalUnitPrice;

    @ApiModelProperty(value = "单价", name = "unitPrice")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "公斤价", name = "kilogramPrice")
    private BigDecimal kilogramPrice;

    @ApiModelProperty(value = "价格单位", name = "priceUnitDesc")
    private String priceUnitDesc;

    @ApiModelProperty(value = "标记60天内是否变更价格", name = "hasChangeRecord")
    private Boolean has60DaysChangeRecord;

    @ApiModelProperty(value = "skc尺码段面料报价明细", name = "flowTaskSkcSizeRangeMaterialOfferingDtoList")
    private List<FlowTaskSkcSizeRangeMaterialOfferingDto> flowTaskSkcSizeRangeMaterialOfferingDtoList;

    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    @ApiModelProperty(value = "面料使用部位", name = "usePartList")
    private List<String> usePartList;

    @ApiModelProperty(value = "面料使用部位", name = "usePartDtos")
    private List<FlowTaskSkcMaterialUsePartDto> usePartDtos;

    @ApiModelProperty(value = "报价排料图", name = "flowTaskSkcAttachDtos")
    private List<FlowTaskSkcAttachDto> flowTaskSkcAttachDtos;

    @ApiModelProperty(value = "核价排料图", name = "flowTaskSkcConfirmAttachDtos")
    private List<FlowTaskSkcAttachDto> flowTaskSkcConfirmAttachDtos;

    @ApiModelProperty(value = "报价备注", name = "offeringRemark")
    private String offeringRemark;

    @ApiModelProperty(value = "异议备注", name = "rejectRemark")
    private String rejectRemark;

    @ApiModelProperty(value = "核价备注", name = "checkRemark")
    private String checkRemark;

    @ApiModelProperty(value = "是否工厂端新增", name = "isFactory")
    private Integer isFactory;

    @ApiModelProperty(value = "供应商面料-辅料-尺寸", name = "size")
    private BigDecimal size;

    @ApiModelProperty("色卡标识，字典对应的中文")
    private String identificationDesc;

    @ApiModelProperty("色卡标识")
    private String identification;

    @ApiModelProperty("供应商自备量")
    private Integer supplierReserve;

    @ApiModelProperty("公司储备量")
    private Integer reserveForNsy;

    @ApiModelProperty(value = "面料供应商合作状态中文", name = "supplierStatusDesc")
    private String supplierStatusDesc;
    @ApiModelProperty(value = "面料状态中文", name = "materialStatusDesc")
    private String materialStatusDesc;
    @ApiModelProperty(value = "色卡状态中文", name = "colorCardStatusDesc")
    private String colorCardStatusDesc;

    @ApiModelProperty(value = "单价-实时的，之前的单价字段才是冗余的", name = "currentUnitPrice")
    private BigDecimal currentUnitPrice;
    @ApiModelProperty(value = "克重(克)-冗余的", name = "originalGrammage")
    private BigDecimal originalGrammage;
    @ApiModelProperty(value = "净幅宽-冗余的", name = "originalNetBreadth")
    private BigDecimal originalNetBreadth;
    @ApiModelProperty(value = "毛幅宽-冗余的", name = "originalGrossBreadth")
    private BigDecimal originalGrossBreadth;
    @ApiModelProperty(value = "横缩率（%）-冗余的", name = "originalTransverseShrinkRate")
    private BigDecimal originalTransverseShrinkRate;
    @ApiModelProperty(value = "直缩率（%）-冗余的", name = "originalDirectShrinkRate")
    private BigDecimal originalDirectShrinkRate;

    @ApiModelProperty(value = "是否bom存在的数据", name = "bomData")
    private boolean bomData;

    @ApiModelProperty(value = "风险（关键字）数据字典：scm_material_supplier_floral_risk", name = "riskDescList")
    private List<String> riskDescList;

    @ApiModelProperty("面料检测状态")
    private String materialDetectionStatus;
    @ApiModelProperty("数据来源")
    private String dataSource;

    public void hidePrice() {
        this.originalUnitPrice = BigDecimal.ZERO;
        this.unitPrice = BigDecimal.ZERO;
        this.currentUnitPrice = BigDecimal.ZERO;
        this.kilogramPrice = BigDecimal.ZERO;
        this.priceUnitDesc = "";
        if (CollectionUtils.isNotEmpty(this.flowTaskSkcSizeRangeMaterialOfferingDtoList)) {
            this.flowTaskSkcSizeRangeMaterialOfferingDtoList.forEach(FlowTaskSkcSizeRangeMaterialOfferingDto::hidePrice);
        }

    }

    public void handOtherFields(FlowTaskSkcMaterialOfferingEntity entity) {
        this.netBreadth = entity.getNetBreadth();
        this.grossBreadth = entity.getGrossBreadth();
        this.transverseShrinkRate = entity.getTransverseShrinkRate();
        this.directShrinkRate = entity.getDirectShrinkRate();
    }

    public void handOtherFieldsByBom(ProductSkcBomMaterialOfferingEntity entity) {
        this.originalUnitPrice = entity.getPrice();
        this.originalGrammage = entity.getGrammage();
        this.originalNetBreadth = entity.getNetBreadth();
        this.originalGrossBreadth = entity.getGrossBreadth();
        this.originalTransverseShrinkRate = entity.getTransverseShrinkRate();
        this.originalDirectShrinkRate = entity.getDirectShrinkRate();
        this.bomData = false;
        if (Objects.nonNull(entity.getProductSkcBomMaterialOfferingId())) {
            this.bomData = true;
        }
    }

    public String getUniqueKey() {
        return String.format("%s-%s-%s-%s", skc, materialType, materialSupplierInfoId, colorCardId);
    }

    public String getUniqueKeyNotColorCardId() {
        return String.format("%s-%s-%s", skc, materialType, materialSupplierInfoId);
    }

    public String getUniqueKeyNotPriceByRequest() {
        return String.format("%s-%s-%s-%s-%s-%s", skc, materialType, materialSupplierInfoId,
                BigDecimalUtils.null2Zero(netBreadth).stripTrailingZeros(), BigDecimalUtils.null2Zero(transverseShrinkRate).stripTrailingZeros(), BigDecimalUtils.null2Zero(directShrinkRate).stripTrailingZeros());
    }

    public String getUniqueKeyNotPriceByResponse() {
        return String.format("%s-%s-%s-%s-%s-%s", skc, materialType, materialSupplierInfoId,
                BigDecimalUtils.null2Zero(originalNetBreadth).stripTrailingZeros(), BigDecimalUtils.null2Zero(originalTransverseShrinkRate).stripTrailingZeros(), BigDecimalUtils.null2Zero(originalDirectShrinkRate).stripTrailingZeros());
    }

    public void copySkcDataFormOther(FlowTaskSkcMaterialOfferingDto copySkcMaterialOfferingDto) {
        this.setUsePartDtos(copySkcMaterialOfferingDto.getUsePartDtos());
        this.getFlowTaskSkcSizeRangeMaterialOfferingDtoList().forEach(flowTaskSkcSizeRangeMaterialOfferingDto -> {
            FlowTaskSkcSizeRangeMaterialOfferingDto copySkcSizeRangeMaterialOfferingDto = copySkcMaterialOfferingDto.getFlowTaskSkcSizeRangeMaterialOfferingDtoList()
                    .stream().filter(copyDto -> copyDto.getSizeRangeId().equals(flowTaskSkcSizeRangeMaterialOfferingDto.getSizeRangeId())).findFirst().orElse(new FlowTaskSkcSizeRangeMaterialOfferingDto());
            flowTaskSkcSizeRangeMaterialOfferingDto.setConfirmAmount(copySkcSizeRangeMaterialOfferingDto.getConfirmAmount());
            flowTaskSkcSizeRangeMaterialOfferingDto.setConfirmLoss(copySkcSizeRangeMaterialOfferingDto.getConfirmLoss());
        });
        this.setRemark(copySkcMaterialOfferingDto.getRemark());
        List<FlowTaskSkcAttachDto> attachDtos = Optional.ofNullable(copySkcMaterialOfferingDto.getFlowTaskSkcConfirmAttachDtos()).orElse(Collections.emptyList()).stream().map(dto -> {
            FlowTaskSkcAttachDto attachDto = new FlowTaskSkcAttachDto();
            BeanUtilsEx.copyProperties(dto, attachDto, "flowTaskSkcAttachId");
            attachDto.setFlowTaskSkcMaterialOfferingId(this.getFlowTaskSkcMaterialOfferingId());
            attachDto.setFlowTaskSkcSecondaryDesignId(NumberConstant.ZERO);
            attachDto.setFlowTaskSkcLoomId(NumberConstant.ZERO);
            attachDto.setSkc(this.getSkc());
            return attachDto;
        }).collect(Collectors.toList());
        this.setFlowTaskSkcConfirmAttachDtos(attachDtos);

    }

    public BigDecimal getKilogramPrice() {
        return kilogramPrice;
    }

    public void setKilogramPrice(BigDecimal kilogramPrice) {
        this.kilogramPrice = kilogramPrice;
    }

    public String getOfferingRemark() {
        return offeringRemark;
    }

    public void setOfferingRemark(String offeringRemark) {
        this.offeringRemark = offeringRemark;
    }

    public String getRejectRemark() {
        return rejectRemark;
    }

    public void setRejectRemark(String rejectRemark) {
        this.rejectRemark = rejectRemark;
    }

    public String getCheckRemark() {
        return checkRemark;
    }

    public void setCheckRemark(String checkRemark) {
        this.checkRemark = checkRemark;
    }

    public Integer getFlowTaskSkcMaterialOfferingId() {
        return flowTaskSkcMaterialOfferingId;
    }

    public void setFlowTaskSkcMaterialOfferingId(Integer flowTaskSkcMaterialOfferingId) {
        this.flowTaskSkcMaterialOfferingId = flowTaskSkcMaterialOfferingId;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }

    public String getMaterialTypeCh() {
        return materialTypeCh;
    }

    public void setMaterialTypeCh(String materialTypeCh) {
        this.materialTypeCh = materialTypeCh;
    }

    public Integer getMaterialTypeOrder() {
        return materialTypeOrder;
    }

    public void setMaterialTypeOrder(Integer materialTypeOrder) {
        this.materialTypeOrder = materialTypeOrder;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getSupplierMaterialName() {
        return supplierMaterialName;
    }

    public void setSupplierMaterialName(String supplierMaterialName) {
        this.supplierMaterialName = supplierMaterialName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierShortName() {
        return supplierShortName;
    }

    public void setSupplierShortName(String supplierShortName) {
        this.supplierShortName = supplierShortName;
    }

    public String getColorDesc() {
        return colorDesc;
    }

    public void setColorDesc(String colorDesc) {
        this.colorDesc = colorDesc;
    }

    public String getColorNumber() {
        return colorNumber;
    }

    public void setColorNumber(String colorNumber) {
        this.colorNumber = colorNumber;
    }

    public BigDecimal getGrammage() {
        return grammage;
    }

    public void setGrammage(BigDecimal grammage) {
        this.grammage = grammage;
    }

    public BigDecimal getNetBreadth() {
        return netBreadth;
    }

    public void setNetBreadth(BigDecimal netBreadth) {
        this.netBreadth = netBreadth;
    }

    public BigDecimal getGrossBreadth() {
        return grossBreadth;
    }

    public void setGrossBreadth(BigDecimal grossBreadth) {
        this.grossBreadth = grossBreadth;
    }

    public String getIngredientDesc() {
        return ingredientDesc;
    }

    public void setIngredientDesc(String ingredientDesc) {
        this.ingredientDesc = ingredientDesc;
    }

    public BigDecimal getOriginalUnitPrice() {
        return originalUnitPrice;
    }

    public void setOriginalUnitPrice(BigDecimal originalUnitPrice) {
        this.originalUnitPrice = originalUnitPrice;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public List<FlowTaskSkcSizeRangeMaterialOfferingDto> getFlowTaskSkcSizeRangeMaterialOfferingDtoList() {
        return flowTaskSkcSizeRangeMaterialOfferingDtoList;
    }

    public void setFlowTaskSkcSizeRangeMaterialOfferingDtoList(List<FlowTaskSkcSizeRangeMaterialOfferingDto> flowTaskSkcSizeRangeMaterialOfferingDtoList) {
        this.flowTaskSkcSizeRangeMaterialOfferingDtoList = flowTaskSkcSizeRangeMaterialOfferingDtoList;
    }

    public void calculateActualConfirmMaterialCost() {
        getFlowTaskSkcSizeRangeMaterialOfferingDtoList().forEach(FlowTaskSkcSizeRangeMaterialOfferingDto::calculateActualConfirmMaterialCost);
    }
    public boolean checkSubmitConfirmMaterialCost() {
        return getFlowTaskSkcSizeRangeMaterialOfferingDtoList().stream().anyMatch(FlowTaskSkcSizeRangeMaterialOfferingDto::checkSubmitConfirmMaterialCost);
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<FlowTaskSkcAttachDto> getFlowTaskSkcAttachDtos() {
        return flowTaskSkcAttachDtos;
    }

    public void setFlowTaskSkcAttachDtos(List<FlowTaskSkcAttachDto> flowTaskSkcAttachDtos) {
        this.flowTaskSkcAttachDtos = flowTaskSkcAttachDtos;
    }

    public List<FlowTaskSkcAttachDto> getFlowTaskSkcConfirmAttachDtos() {
        return flowTaskSkcConfirmAttachDtos;
    }

    public void setFlowTaskSkcConfirmAttachDtos(List<FlowTaskSkcAttachDto> flowTaskSkcConfirmAttachDtos) {
        this.flowTaskSkcConfirmAttachDtos = flowTaskSkcConfirmAttachDtos;
    }

    public Integer getMaterialSupplierInfoId() {
        return materialSupplierInfoId;
    }

    public void setMaterialSupplierInfoId(Integer materialSupplierInfoId) {
        this.materialSupplierInfoId = materialSupplierInfoId;
    }

    public Integer getColorCardId() {
        return colorCardId;
    }

    public void setColorCardId(Integer colorCardId) {
        this.colorCardId = colorCardId;
    }


    public String getContactsName() {
        return contactsName;
    }

    public void setContactsName(String contactsName) {
        this.contactsName = contactsName;
    }

    public String getContactsPhone() {
        return contactsPhone;
    }

    public void setContactsPhone(String contactsPhone) {
        this.contactsPhone = contactsPhone;
    }

    public String getSupplierIdentificationDesc() {
        return supplierIdentificationDesc;
    }

    public void setSupplierIdentificationDesc(String supplierIdentificationDesc) {
        this.supplierIdentificationDesc = supplierIdentificationDesc;
    }

    public Integer getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Integer materialId) {
        this.materialId = materialId;
    }

    public Boolean getHas60DaysChangeRecord() {
        return has60DaysChangeRecord;
    }

    public void setHas60DaysChangeRecord(Boolean has60DaysChangeRecord) {
        this.has60DaysChangeRecord = has60DaysChangeRecord;
    }

    public String getMeasureShrinkModeDesc() {
        return measureShrinkModeDesc;
    }

    public void setMeasureShrinkModeDesc(String measureShrinkModeDesc) {
        this.measureShrinkModeDesc = measureShrinkModeDesc;
    }

    public BigDecimal getTransverseShrinkRate() {
        return transverseShrinkRate;
    }

    public void setTransverseShrinkRate(BigDecimal transverseShrinkRate) {
        this.transverseShrinkRate = transverseShrinkRate;
    }

    public BigDecimal getDirectShrinkRate() {
        return directShrinkRate;
    }

    public void setDirectShrinkRate(BigDecimal directShrinkRate) {
        this.directShrinkRate = directShrinkRate;
    }

    public Integer getIsFactory() {
        return isFactory;
    }

    public void setIsFactory(Integer isFactory) {
        this.isFactory = isFactory;
    }


    public BigDecimal getSize() {
        return size;
    }

    public void setSize(BigDecimal size) {
        this.size = size;
    }

    public String getIdentificationDesc() {
        return identificationDesc;
    }

    public void setIdentificationDesc(String identificationDesc) {
        this.identificationDesc = identificationDesc;
    }

    public String getIdentification() {
        return identification;
    }

    public void setIdentification(String identification) {
        this.identification = identification;
    }

    public Integer getSupplierReserve() {
        return supplierReserve;
    }

    public void setSupplierReserve(Integer supplierReserve) {
        this.supplierReserve = supplierReserve;
    }

    public Integer getReserveForNsy() {
        return reserveForNsy;
    }

    public void setReserveForNsy(Integer reserveForNsy) {
        this.reserveForNsy = reserveForNsy;
    }

    public String getPriceUnitDesc() {
        return priceUnitDesc;
    }

    public void setPriceUnitDesc(String priceUnitDesc) {
        this.priceUnitDesc = priceUnitDesc;
    }

    public String getSupplierStatusDesc() {
        return supplierStatusDesc;
    }

    public void setSupplierStatusDesc(String supplierStatusDesc) {
        this.supplierStatusDesc = supplierStatusDesc;
    }

    public String getMaterialStatusDesc() {
        return materialStatusDesc;
    }

    public void setMaterialStatusDesc(String materialStatusDesc) {
        this.materialStatusDesc = materialStatusDesc;
    }

    public String getColorCardStatusDesc() {
        return colorCardStatusDesc;
    }

    public void setColorCardStatusDesc(String colorCardStatusDesc) {
        this.colorCardStatusDesc = colorCardStatusDesc;
    }

    public BigDecimal getCurrentUnitPrice() {
        return currentUnitPrice;
    }

    public void setCurrentUnitPrice(BigDecimal currentUnitPrice) {
        this.currentUnitPrice = currentUnitPrice;
    }

    public BigDecimal getOriginalGrammage() {
        return originalGrammage;
    }

    public void setOriginalGrammage(BigDecimal originalGrammage) {
        this.originalGrammage = originalGrammage;
    }

    public BigDecimal getOriginalNetBreadth() {
        return originalNetBreadth;
    }

    public void setOriginalNetBreadth(BigDecimal originalNetBreadth) {
        this.originalNetBreadth = originalNetBreadth;
    }

    public BigDecimal getOriginalGrossBreadth() {
        return originalGrossBreadth;
    }

    public void setOriginalGrossBreadth(BigDecimal originalGrossBreadth) {
        this.originalGrossBreadth = originalGrossBreadth;
    }

    public BigDecimal getOriginalTransverseShrinkRate() {
        return originalTransverseShrinkRate;
    }

    public void setOriginalTransverseShrinkRate(BigDecimal originalTransverseShrinkRate) {
        this.originalTransverseShrinkRate = originalTransverseShrinkRate;
    }

    public BigDecimal getOriginalDirectShrinkRate() {
        return originalDirectShrinkRate;
    }

    public void setOriginalDirectShrinkRate(BigDecimal originalDirectShrinkRate) {
        this.originalDirectShrinkRate = originalDirectShrinkRate;
    }

    public boolean isBomData() {
        return bomData;
    }

    public void setBomData(boolean bomData) {
        this.bomData = bomData;
    }

    public List<String> getRiskDescList() {
        return riskDescList;
    }

    public void setRiskDescList(List<String> riskDescList) {
        this.riskDescList = riskDescList;
    }

    public void buildInfo(FlowTaskSkcMaterialOfferingEntity f, Map<Integer, Boolean> priceChangeRecordMap, Map<Integer, String> infringementLabelMap, FlowTaskEditBomDto flowTaskEditBomDto) {
        this.setMaterialTypeCh(MaterialTypeEnum.getDescByValue(f.getMaterialType()));
        this.setMaterialTypeOrder(MaterialTypeEnum.getOrderByDesc(this.getMaterialTypeCh()));
        this.setHas60DaysChangeRecord(priceChangeRecordMap.getOrDefault(f.getMaterialSupplierInfoId(), Boolean.FALSE));
        if (FlowTaskEditBomStatusEnum.BOM_EXCLUDE_STATUS_LIST.contains(flowTaskEditBomDto.getStatus())) {
            this.handOtherFields(f);
        }
        this.setRiskDescList(Collections.singletonList(infringementLabelMap.getOrDefault(f.getColorCardId(), StringConstant.EMPTY_STRING)));
    }

    public String getMaterialDetectionStatus() {
        return materialDetectionStatus;
    }

    public void setMaterialDetectionStatus(String materialDetectionStatus) {
        this.materialDetectionStatus = materialDetectionStatus;
    }

    public List<String> getUsePartList() {
        return usePartList;
    }

    public void setUsePartList(List<String> usePartList) {
        this.usePartList = usePartList;
    }

    public List<FlowTaskSkcMaterialUsePartDto> getUsePartDtos() {
        return usePartDtos;
    }

    public void setUsePartDtos(List<FlowTaskSkcMaterialUsePartDto> usePartDtos) {
        this.usePartDtos = usePartDtos;
    }

    public String getMaterialColorCardPriceType() {
        return materialColorCardPriceType;
    }

    public void setMaterialColorCardPriceType(String materialColorCardPriceType) {
        this.materialColorCardPriceType = materialColorCardPriceType;
    }
}
