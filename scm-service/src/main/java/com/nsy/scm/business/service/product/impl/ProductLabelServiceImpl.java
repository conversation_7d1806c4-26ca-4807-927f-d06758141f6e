package com.nsy.scm.business.service.product.impl;

import com.nsy.api.scm.dto.domain.product.ProductLabelRelationIdDto;
import com.nsy.scm.business.service.product.ProductLabelService;
import com.nsy.scm.constant.product.ProductLabelConstant;
import com.nsy.scm.repository.dao.product.ProductLabelDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 商品标签 服务类
 * <AUTHOR>
 * @create 2023-11-21 15:49
 */
@Service
public class ProductLabelServiceImpl implements ProductLabelService {

    @Autowired
    private ProductLabelDao productLabelDao;

    @Override
    public List<ProductLabelRelationIdDto> getRelationIdByProductIds(List<Integer> productIds) {
        return productLabelDao.getRelationIdByProductIds(productIds);
    }

    @Override
    public Map<Integer, Set<String>> getLabelMapByProductIds(List<Integer> productIds) {
        return productLabelDao.getLabelMapByProductIds(productIds);
    }

    @Override
    public Map<Integer, Set<String>> getLabelMapByProductIds(List<Integer> productIds, List<String> labelList) {
        return productLabelDao.getLabelMapByProductIds(productIds, labelList);
    }

    @Override
    public boolean includeAutonomousDesignProductLabel(Integer productId) {
        List<String> labelList = Arrays.asList(ProductLabelConstant.LABEL_PRODUCT_AUTONOMOUS_DESIGN, ProductLabelConstant.LABEL_PRODUCT_SAMPLE_TEST);
        Set<String> res = getLabelMapByProductIds(Collections.singletonList(productId), labelList).getOrDefault(productId, Collections.emptySet());
        return CollectionUtils.isNotEmpty(res);
    }
}
