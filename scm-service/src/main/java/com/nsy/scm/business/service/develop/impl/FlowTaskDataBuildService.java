package com.nsy.scm.business.service.develop.impl;

import com.nsy.api.scm.dto.domain.dictionary.DictionaryItem;
import com.nsy.api.scm.dto.domain.supplier.SupplierDto;
import com.nsy.scm.business.domain.develop.FlowTaskSkcMaterialOfferingDto;
import com.nsy.scm.business.service.develop.FlowTaskSkcMaterialOfferingService;
import com.nsy.scm.business.service.supplier.SupplierAddressService;
import com.nsy.scm.constant.DictionaryNameEnum;
import com.nsy.scm.constant.TrueOrFalseConstant;
import com.nsy.scm.enumstable.product.bom.MaterialTypeEnum;
import com.nsy.scm.enumstable.supplier.FabricSupplierCooperateStatusEnum;
import com.nsy.scm.repository.cache.SupplierCacheService;
import com.nsy.scm.repository.entity.BdIngredientEntity;
import com.nsy.scm.repository.entity.MaterialSupplierInfoEntity;
import com.nsy.scm.repository.entity.SupplierAddressEntity;
import com.nsy.scm.repository.entity.material.MaterialSupplierColorCardEntity;
import com.nsy.scm.repository.entity.material.MaterialSupplierColorCardStockEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022-11-17
 */
@Service
public class FlowTaskDataBuildService {
    @Inject
    @Qualifier("supplierCacheServiceImpl")
    private SupplierCacheService supplierCacheService;
    @Inject
    private SupplierAddressService supplierAddressService;
    @Autowired
    private FlowTaskSkcMaterialOfferingService flowTaskSkcMaterialOfferingService;

    void injectSupplierAndColorInfoField(FlowTaskSkcMaterialOfferingDto dto,
                                         Map<String, DictionaryItem> dictionaryItemMap,
                                         Map<Integer, MaterialSupplierInfoEntity> materialSupplierInfoDtoMap,
                                         Map<Integer, BdIngredientEntity> ingredientIdEntityMap,
                                         Map<Integer, MaterialSupplierColorCardEntity> materialSupplierColorCardEntityMap,
                                         Map<Integer, MaterialSupplierColorCardStockEntity> materialSupplierColorCardStockEntityMap) {
        MaterialSupplierInfoEntity materialSupplierInfoEntity = materialSupplierInfoDtoMap.getOrDefault(dto.getMaterialSupplierInfoId(), new MaterialSupplierInfoEntity());
        dto.setMaterialId(materialSupplierInfoEntity.getMaterialId());
        dto.setSupplierMaterialName(materialSupplierInfoEntity.getSupplierMaterialName());
        dto.setGrammage(materialSupplierInfoEntity.getGrammage());
        dto.setGrossBreadth(materialSupplierInfoEntity.getGrossBreadth());
        dto.setNetBreadth(materialSupplierInfoEntity.getNetBreadth());
        dto.setSize(materialSupplierInfoEntity.getSize());
        dto.setCurrentUnitPrice(flowTaskSkcMaterialOfferingService.getUnitPriceForTemp(materialSupplierColorCardEntityMap, materialSupplierInfoDtoMap, dto));
        dto.setMaterialStatusDesc(TrueOrFalseConstant.DELETED.equals(materialSupplierInfoEntity.getIsDelete()) ? "删除" : TrueOrFalseConstant.DISABLE.equals(materialSupplierInfoEntity.getEnableStatus()) ? "停用" : "");
        SupplierDto supplier = supplierCacheService.getDtoByIdIgnoreTenant(materialSupplierInfoEntity.getSupplierId());
        dto.setSupplierName(supplier.getSupplierName());
        dto.setSupplierShortName(supplier.getSupplierShortName());
        dto.setSupplierStatusDesc(FabricSupplierCooperateStatusEnum.getDescByName(supplier.getCooperateStatus()));
        dto.setSupplierIdentificationDesc(DictionaryNameEnum.getDictionaryItemMapValue(dictionaryItemMap, DictionaryNameEnum.SCM_SUPPLIER_IDENTIFICATION.getParentKey(), supplier.getIdentification()));
        SupplierAddressEntity supplierAddressEntity = supplierAddressService.selectOfficeAddressBySupplierId(supplier.getSupplierId());
        if (supplierAddressEntity != null) {
            dto.setContactsName(supplierAddressEntity.getContactsName());
            dto.setContactsPhone(supplierAddressEntity.getContactsPhone());
        }
        //成分
        if (!Objects.isNull(materialSupplierInfoEntity.getIngredientId())) {
            BdIngredientEntity bdIngredientEntity = ingredientIdEntityMap.get(materialSupplierInfoEntity.getIngredientId());
            if (Objects.nonNull(bdIngredientEntity)) {
                dto.setIngredientDesc(bdIngredientEntity.getText());
            }
        }
        //花型
        MaterialSupplierColorCardEntity colorCardEntity = materialSupplierColorCardEntityMap.getOrDefault(dto.getColorCardId(), new MaterialSupplierColorCardEntity());
        Optional.ofNullable(dictionaryItemMap.get(DictionaryNameEnum.getDictionaryItemMapKey(DictionaryNameEnum.COLOR.getParentKey(), colorCardEntity.getColorDictionaryItemValue())))
            .ifPresent(bdDictionaryItemDto -> dto.setColorDesc(String.format("%s#%s", bdDictionaryItemDto.getAttribute(), bdDictionaryItemDto.getLabel())));
        dto.setColorNumber(colorCardEntity.getColorNumber());
        dto.setImgUrl(colorCardEntity.getImgUrl());
        dto.setIdentificationDesc(DictionaryNameEnum.getDictionaryItemMapValue(dictionaryItemMap, DictionaryNameEnum.SCM_FABRIC_COLOR_CARD_IDENTIFICATION.getParentKey(), colorCardEntity.getIdentification()));
        dto.setIdentification(colorCardEntity.getIdentification());
        MaterialSupplierColorCardStockEntity materialSupplierColorCardStockEntity = materialSupplierColorCardStockEntityMap.getOrDefault(dto.getColorCardId(), new MaterialSupplierColorCardStockEntity());
        dto.setReserveForNsy(materialSupplierColorCardStockEntity.getReserveForNsy());
        dto.setSupplierReserve(materialSupplierColorCardStockEntity.getSupplierReserve());
        dto.setKilogramPrice(colorCardEntity.getUnitPrice());
        dto.setColorCardStatusDesc(TrueOrFalseConstant.DELETED.equals(colorCardEntity.getIsDelete()) ? "删除" : TrueOrFalseConstant.DISABLE_STATUS.equals(colorCardEntity.getIsEnable()) ? "停用" : "");
        dto.setPriceUnitDesc(DictionaryNameEnum.getDictionaryItemMapValue(dictionaryItemMap, DictionaryNameEnum.SCM_QUOTATION_UNIT.getParentKey(), materialSupplierInfoEntity.getPriceUnit()));
        if (MaterialTypeEnum.MAIN_FABRIC.name().equals(dto.getMaterialType()) || MaterialTypeEnum.AUXILIARY_FABRIC.name().equals(dto.getMaterialType())) {
            dto.setTransverseShrinkRate(colorCardEntity.getTransverseShrinkRate());
            dto.setDirectShrinkRate(colorCardEntity.getDirectShrinkRate());
            Optional.ofNullable(dictionaryItemMap.get(DictionaryNameEnum.getDictionaryItemMapKey(DictionaryNameEnum.SCM_MEASURE_SHRINK_MODE.getParentKey(), colorCardEntity.getMeasureShrinkMode())))
                .ifPresent(bdDictionaryItemDto -> dto.setMeasureShrinkModeDesc(bdDictionaryItemDto.getLabel()));
        }
    }
}
