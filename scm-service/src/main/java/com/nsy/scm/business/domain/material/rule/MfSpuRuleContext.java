package com.nsy.scm.business.domain.material.rule;

import com.nsy.scm.enumstable.product.ProductSupplyStatusEnum;

import java.util.Set;

/**
 * MfSpuRuleContext
 *
 * <AUTHOR>
 * @since 2023/3/24 13:52
 */
public interface MfSpuRuleContext {

    ProductSupplyStatusEnum getSupplyStatus(Integer productId);

    Integer isSpot(Integer productId);

    Integer isPack(Integer productId);

    Set<Integer> getLabels(Integer productId);

    /**
     * 供应链品类
     */
    Integer getScmCategoryId(Integer productId);

    String getSpuPrefix(Integer productId);
}
