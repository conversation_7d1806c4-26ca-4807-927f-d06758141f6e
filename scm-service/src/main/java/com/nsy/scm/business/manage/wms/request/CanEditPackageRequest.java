package com.nsy.scm.business.manage.wms.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @description:
 * @author: caishao<PERSON>
 * @time: 2023/8/2 10:25
 */
@ApiModel("CanEditPackageRequest")
public class CanEditPackageRequest {

    @ApiModelProperty(value = "规格编码", name = "skuList")
    @Size(min = 1)
    private List<String> skuList;

    @ApiModelProperty(value = "采购计划单号", name = "purchasePlanNo")
    @NotBlank
    private String purchasePlanNo;

    public List<String> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<String> skuList) {
        this.skuList = skuList;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }
}
