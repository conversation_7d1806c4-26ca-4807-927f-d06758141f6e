package com.nsy.scm.business.service.bd.impl;

import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.scm.dto.constant.MatchCoefficientDimensionEnum;
import com.nsy.scm.business.domain.replenishment.ReplenishmentDateDto;
import com.nsy.scm.business.domain.replenishment.ScmCompanyReplenishmentConfigImport;
import com.nsy.scm.business.request.replenishment.BdReplenishmentConfigSaveOrUpdateRequest;
import com.nsy.scm.business.service.bd.BdReplenishmentConfigService;
import com.nsy.scm.business.service.upload.IUploadService;
import com.nsy.scm.business.service.upload.base.QuartzUploadQueueTypeEnum;
import com.nsy.scm.business.service.upload.base.UploadRequest;
import com.nsy.scm.business.service.upload.base.UploadResponse;
import com.nsy.scm.enums.bd.NsyCompanyEnum;
import com.nsy.scm.enums.bd.SeasonEnum;
import com.nsy.scm.constant.replenishment.ReplenishmentConfigTypeEnum;
import com.nsy.scm.repository.entity.replenishment.BdCategoryGroupEntity;
import com.nsy.scm.utils.JsonMapper;
import com.nsy.scm.utils.Validator;
import com.nsy.scm.utils.mp.TenantContext;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class BdReplenishmentConfigCompanyUploadServiceImpl extends BaseReplenishmentConfigUploadService implements IUploadService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BdReplenishmentConfigCompanyUploadServiceImpl.class);

    @Autowired
    private BdReplenishmentConfigService bdReplenishmentConfigService;

    @Override
    public QuartzUploadQueueTypeEnum uploadType() {
        return QuartzUploadQueueTypeEnum.SCM_COMPANY_REPLENISHMENT_CONFIG_IMPORT;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        LOGGER.info("上传中心发送的数据为：{}", JsonMapper.toJson(request));
        String location = request.getLocation();
        TenantContext.setTenant(location);
        UploadResponse response = new UploadResponse();
        if (StringUtils.isBlank(request.getDataJsonStr())) {
            return response;
        }
        List<ScmCompanyReplenishmentConfigImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), ScmCompanyReplenishmentConfigImport.class);
        if (CollectionUtils.isEmpty(importList)) {
            return response;
        }
        List<ScmCompanyReplenishmentConfigImport> errorList = new LinkedList<>();
        List<BdReplenishmentConfigSaveOrUpdateRequest> saveRequestList = new LinkedList<>();
        List<String> groupNameList = importList.stream().map(ScmCompanyReplenishmentConfigImport::getCategoryGroupName).distinct().collect(Collectors.toList());
        Map<String, BdCategoryGroupEntity> groupEntityMap = queryGroupMap(groupNameList, location);

        for (ScmCompanyReplenishmentConfigImport importDto : importList) {
            try {
                isValid(importDto, groupEntityMap);
                List<Integer> invalidMonths = importDto.getFbaSafetyDaysInvalidMonths();
                if (!invalidMonths.isEmpty()) {
                    throw new InvalidRequestException(String.format("FBA安全天数不可为空且必须大于等于0,月份%s", invalidMonths));
                }
                BdReplenishmentConfigSaveOrUpdateRequest saveRequest = buildSaveRequest(importDto, groupEntityMap.get(importDto.getCategoryGroupName()).getGroupId());
                bdReplenishmentConfigService.validateRequest(saveRequest);
                Validator.isValid(saveRequest, r -> saveRequestList.stream().allMatch(t -> !checkExistsData(t, r)), String.format("导入数据重复，相同公司%s,季节%s,品类分组%s", saveRequest.getBusinessType(), saveRequest.getSeason(), saveRequest.getCategoryGroupId()));
                saveRequestList.add(saveRequest);
            } catch (InvalidRequestException | IllegalArgumentException ex) {
                importDto.setErrorMsg(ex.getMessage());
                errorList.add(importDto);
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage(), ex);
                importDto.setErrorMsg(ex.getMessage());
                errorList.add(importDto);
            }
        }
        if (!errorList.isEmpty()) {
            response.setDataJsonStr(JsonMapper.toJson(errorList));
        } else {
            bdReplenishmentConfigService.clearConfig(getConfigType());
            bdReplenishmentConfigService.saveOrUpdateConfigList(saveRequestList);
        }
        return response;
    }

    private void isValid(ScmCompanyReplenishmentConfigImport importDto, Map<String, BdCategoryGroupEntity> groupEntityMap) {
        Validator.isValid(importDto.getTargetSoldOutRate(), num -> Objects.nonNull(num) && num.compareTo(BigDecimal.ZERO) >= 0 && num.compareTo(BigDecimal.ONE) <= 0, "售罄率不可为空，值范围0-1");
        Validator.isValid(importDto.getMatchCoefficientDimension(), match -> StringUtils.isNotEmpty(match) && Objects.nonNull(MatchCoefficientDimensionEnum.getByName(match)), "匹配系数范围类型不存在");
        Validator.isValid(importDto.getPredictedSaleDays(), num -> num != null && num > 0, "预测销售天数不可为空且必须大于0");
        Validator.isValid(importDto.getReferenceLatestSaleDays(), num -> num != null && num > 0, "参考最近销量天数不可为空且必须大于0");
        Validator.isValid(importDto.getSeason(), season -> StringUtils.isNotEmpty(season) && Objects.nonNull(SeasonEnum.resolveName(season)), "季节不存在");
        Validator.isValid(importDto.getAirWarningDays(), num -> num != null && num > 0, "提前预警天数(发FBA)不可为空且必须大于0");
        Validator.isValid(importDto.getCompanyName(), company -> StringUtils.isNotEmpty(company) && Objects.nonNull(NsyCompanyEnum.resolveByName(company)), "平台不存在");
        Validator.isValid(importDto.getFbaMinShipmentQty(), num -> num != null && num >= 0, "最少建议量（发FBA）不可为空且必须大于等于0");
        Validator.isValid(importDto.getTargetMaximumSaleDays(), num -> num != null && num >= 0, "目标销量最长天数不可为空且必须大于等于0");
        Validator.isValid(importDto.getCategoryGroupName(), groupName -> StringUtils.isNotBlank(groupName) && groupEntityMap.containsKey(groupName), "品类分组不能为空，且必须存在");
        Validator.isValid(importDto.getSafeCoefficientRatio(), safeCoefficientRatio -> Objects.nonNull(safeCoefficientRatio) && safeCoefficientRatio.compareTo(BigDecimal.ZERO) >= 0, "安全系数比例不能为空，且必须大于0");
        Validator.isValid(importDto.getInventoryToSaleDayLimit(), inventoryToSaleDayLimit -> inventoryToSaleDayLimit != null && inventoryToSaleDayLimit > 0, "补货后安全天数不能为空，且必须是大于0的整数");
    }

    private BdReplenishmentConfigSaveOrUpdateRequest buildSaveRequest(ScmCompanyReplenishmentConfigImport importDto, Integer categoryGroupId) {
        BdReplenishmentConfigSaveOrUpdateRequest saveRequest = new BdReplenishmentConfigSaveOrUpdateRequest();
        saveRequest.setConfigName(importDto.getConfigName());
        saveRequest.setCompanyId(NsyCompanyEnum.resolveByName(importDto.getCompanyName()).getCompanyId());
        saveRequest.setTargetSoldOutRate(importDto.getTargetSoldOutRate());
        saveRequest.setPredictedSaleDays(importDto.getPredictedSaleDays());
        saveRequest.setReferenceLatestSaleDays(importDto.getReferenceLatestSaleDays());
        saveRequest.setSeason(SeasonEnum.resolveName(importDto.getSeason()).getValue());
        saveRequest.setConfigType(getConfigType().getCode());
        saveRequest.setIsEnabled(1);
        saveRequest.setCategoryGroupId(categoryGroupId);
        saveRequest.setInventoryToSaleDayLimit(importDto.getInventoryToSaleDayLimit());
        saveRequest.setSaleBeginDateDto(ReplenishmentDateDto.takeDateFromStr(importDto.getSaleBeginDate(), "销售开始日期"));
        saveRequest.setSaleEndDateDto(ReplenishmentDateDto.takeDateFromStr(importDto.getSaleEndDate(), "销售结束日期"));
        saveRequest.setSaleMaturityBeginDateDto(ReplenishmentDateDto.takeDateFromStr(importDto.getSaleMaturityBeginDate(), "销售成熟开始日期"));
        saveRequest.setSaleMaturityEndDateDto(ReplenishmentDateDto.takeDateFromStr(importDto.getSaleMaturityEndDate(), "销售成熟结束日期"));
        saveRequest.setLatestOrderDateDto(ReplenishmentDateDto.takeDateFromStr(importDto.getLatestOrderDate(), "最晚采购下单日期"));
        saveRequest.setHotLatestOrderDateDto(ReplenishmentDateDto.takeDateFromStr(importDto.getHotLatestOrderDate(), "爆款最晚采购下单日期"));
        saveRequest.setAirWarningDays(importDto.getAirWarningDays());
        saveRequest.setLatestSendFbaDateDto(ReplenishmentDateDto.takeDateFromStr(importDto.getLatestSendFbaDate(), "最晚发FBA日期"));
        saveRequest.setHotLatestSendFbaDateDto(ReplenishmentDateDto.takeDateFromStr(importDto.getHotLatestSendFbaDate(), "爆款最晚发FBA日期"));
        saveRequest.setUpdateExists(true); //导入覆盖更新
        saveRequest.setFbaMinShipmentQty(importDto.getFbaMinShipmentQty());
        saveRequest.setFbaSafetyDays(importDto.getFbaSafetyDays());
        saveRequest.setMatchCoefficientDimension(Objects.requireNonNull(MatchCoefficientDimensionEnum.getByName(importDto.getMatchCoefficientDimension())).getCode());
        saveRequest.setSafeInventoryToSaleDay(importDto.getSafeInventoryToSaleDay());
        saveRequest.setSafeCoefficientRatio(importDto.getSafeCoefficientRatio());
        saveRequest.setTargetMaximumSaleDays(importDto.getTargetMaximumSaleDays());
        saveRequest.setProductionStartDateDto(ReplenishmentDateDto.takeDateFromStrNotValid(importDto.getProductionStartDate(), "打款启动时间"));
        saveRequest.setProductionEndDateDto(ReplenishmentDateDto.takeDateFromStrNotValid(importDto.getProductionEndDate(), "打款结束时间"));
        return saveRequest;
    }

    private boolean checkExistsData(BdReplenishmentConfigSaveOrUpdateRequest t, BdReplenishmentConfigSaveOrUpdateRequest r) {
        return t.getCompanyId().equals(r.getCompanyId()) && t.getCategoryGroupId().equals(r.getCategoryGroupId()) && t.getSeason().equals(r.getSeason());
    }
    @Override
    protected ReplenishmentConfigTypeEnum getConfigType() {
        return ReplenishmentConfigTypeEnum.COMPANY;
    }
}
