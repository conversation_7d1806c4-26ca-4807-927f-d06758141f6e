package com.nsy.scm.business.request.product;

import com.nsy.api.scm.dto.request.common.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(value = "ProductScmCategoryPageRequest", description = "基础配置-供应商品类-分页列表 request")
public class ProductScmCategoryPageRequest extends PageRequest {

    @NotNull(message = "供应链分类id不能为空")
    @ApiModelProperty(value = "供应链分类id", name = "scmCategoryId")
    private Integer scmCategoryId;

    @ApiModelProperty(value = "供应链分类名称", name = "scmCategoryName")
    private String scmCategoryName;

    @ApiModelProperty(value = "商品分类id list", name = "productCategoryIdList")
    private List<Integer> productCategoryIdList;

    public Integer getScmCategoryId() {
        return scmCategoryId;
    }

    public void setScmCategoryId(Integer scmCategoryId) {
        this.scmCategoryId = scmCategoryId;
    }

    public String getScmCategoryName() {
        return scmCategoryName;
    }

    public void setScmCategoryName(String scmCategoryName) {
        this.scmCategoryName = scmCategoryName;
    }

    public List<Integer> getProductCategoryIdList() {
        return productCategoryIdList;
    }

    public void setProductCategoryIdList(List<Integer> productCategoryIdList) {
        this.productCategoryIdList = productCategoryIdList;
    }
}
