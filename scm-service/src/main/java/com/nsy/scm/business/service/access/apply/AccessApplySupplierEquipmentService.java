package com.nsy.scm.business.service.access.apply;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.scm.repository.entity.AccessApplySupplierEquipmentEntity;
import com.nsy.api.scm.dto.domain.access.apply.supplier.AccessApplySupplierEquipmentDto;

import java.util.List;

/**
 * <p>
 * 供应商准入设备信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-13
 */
public interface AccessApplySupplierEquipmentService extends IService<AccessApplySupplierEquipmentEntity> {

    void saveOrUpdateBatch(Integer accessApplyId, List<AccessApplySupplierEquipmentDto> accessApplySupplierEquipmentDtoList);

    List<AccessApplySupplierEquipmentDto> queryDtosByAccessApplyId(Integer accessApplyId);

    void batchTrashByAccessApplyIds(List<Integer> accessApplyIdList);

    List<AccessApplySupplierEquipmentEntity> queryBeansByAccessApplyId(Integer accessApplyId);
}
