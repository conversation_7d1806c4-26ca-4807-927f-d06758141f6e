package com.nsy.scm.business.manage.wms.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/***
 * <AUTHOR>
 * @since 2022-05-06
 */
@ApiModel(value = "StockinReturnProductSearchQtyResponse", description = "根据returnProductId查退货数量Response")
public class StockinReturnProductSearchQtyResponse {
    @ApiModelProperty(value = "退货库位信息主键", name = "returnProductId")
    private Integer returnProductId;
    @ApiModelProperty(value = "returnQty", name = "returnQty")
    private Integer returnQty;

    public Integer getReturnProductId() {
        return returnProductId;
    }

    public void setReturnProductId(Integer returnProductId) {
        this.returnProductId = returnProductId;
    }

    public Integer getReturnQty() {
        return returnQty;
    }

    public void setReturnQty(Integer returnQty) {
        this.returnQty = returnQty;
    }
}
