package com.nsy.scm.business.domain.material.sample;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <h3>面料打样任务明细DTO</h3>
 *
 * <AUTHOR>
 * @since 2024/03/18 11:25
 */
@ApiModel(value = "FlowTaskMaterialSampleItemDTO", description = "面料打样任务明细DTO")
public class FlowTaskMaterialSampleItemDTO {

    @ApiModelProperty(value = "任务ID", name = "taskId")
    private Integer taskId;

    @ApiModelProperty(value = "任务明细ID", name = "taskItemId")
    private Integer taskItemId;

    @ApiModelProperty(value = "面料供应商ID", name = "supplierId")
    private Integer supplierId;

    @ApiModelProperty(value = "面料供应商简称", name = "supplierShortName")
    private String supplierShortName;

    @ApiModelProperty(value = "打样跟进人ID", name = "followUpUserId")
    private Integer followUpUserId;

    @ApiModelProperty(value = "商品颜色编码", name = "taskId")
    private String skc;

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public Integer getTaskItemId() {
        return taskItemId;
    }

    public void setTaskItemId(Integer taskItemId) {
        this.taskItemId = taskItemId;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierShortName() {
        return supplierShortName;
    }

    public void setSupplierShortName(String supplierShortName) {
        this.supplierShortName = supplierShortName;
    }

    public Integer getFollowUpUserId() {
        return followUpUserId;
    }

    public void setFollowUpUserId(Integer followUpUserId) {
        this.followUpUserId = followUpUserId;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }
}
