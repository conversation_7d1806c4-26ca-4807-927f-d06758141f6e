package com.nsy.scm.business.service.material.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.scm.constant.TrueOrFalseConstant;
import com.nsy.scm.repository.entity.material.MaterialApplyAttachmentEntity;
import com.nsy.scm.repository.entity.material.MaterialApplyEntity;
import com.nsy.scm.repository.sql.mapper.material.MaterialApplyAttachmentMapper;
import com.nsy.scm.business.service.material.MaterialApplyAttachmentService;
import com.nsy.api.scm.dto.constant.AttachmentTypeEnum;
import com.nsy.api.scm.dto.domain.material.MaterialApplyAttachmentDto;
import com.nsy.api.scm.dto.domain.material.MaterialApplyAttachmentSaveDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 面料申请附件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Service
public class MaterialApplyAttachmentServiceImpl extends ServiceImpl<MaterialApplyAttachmentMapper, MaterialApplyAttachmentEntity> implements MaterialApplyAttachmentService {


    @Override
    public Map<Integer, List<MaterialApplyAttachmentDto>> queryDtoGbApplyIdMapByApplyIds(List<Integer> applyIds, AttachmentTypeEnum attachmentType) {
        if (CollectionUtils.isEmpty(applyIds)) {
            return new HashMap<>();
        }
        return super.list(new LambdaQueryWrapper<MaterialApplyAttachmentEntity>()
                .in(MaterialApplyAttachmentEntity::getApplyId, applyIds)
                .eq(MaterialApplyAttachmentEntity::getIsDelete, TrueOrFalseConstant.NOT_DELETED)
                .eq(MaterialApplyAttachmentEntity::getAttachmentType, attachmentType))
                .stream().map(entity -> {
                    MaterialApplyAttachmentDto dto = new MaterialApplyAttachmentDto();
                    BeanUtilsEx.copyProperties(entity, dto);
                    return dto;
                }).collect(Collectors.groupingBy(MaterialApplyAttachmentDto::getApplyId));
    }

    @Override
    public List<MaterialApplyAttachmentDto> queryDtoByApplyId(Integer applyId, AttachmentTypeEnum attachmentType) {
        return getValidAttachmentEntities(applyId, attachmentType)
                .stream().map(entity -> {
                    MaterialApplyAttachmentDto dto = new MaterialApplyAttachmentDto();
                    BeanUtilsEx.copyProperties(entity, dto);
                    return dto;
                }).collect(Collectors.toList());
    }

    @Override
    public List<MaterialApplyAttachmentEntity> getValidAttachmentEntities(Integer applyId, AttachmentTypeEnum attachmentType) {
        return list(new LambdaQueryWrapper<MaterialApplyAttachmentEntity>()
                .eq(MaterialApplyAttachmentEntity::getApplyId, applyId)
                .eq(MaterialApplyAttachmentEntity::getAttachmentType, attachmentType)
                .eq(MaterialApplyAttachmentEntity::getIsDelete, TrueOrFalseConstant.NOT_DELETED));

    }

    @Override
    public void saveMaterialApplyAttachments(MaterialApplyEntity materialApplyEntity, List<MaterialApplyAttachmentSaveDto> fabricImgList, AttachmentTypeEnum attachmentType) {
        Integer applyId = materialApplyEntity.getApplyId();
        List<MaterialApplyAttachmentEntity> existAttachments = list(new LambdaQueryWrapper<MaterialApplyAttachmentEntity>()
                .eq(MaterialApplyAttachmentEntity::getApplyId, applyId)
                .eq(MaterialApplyAttachmentEntity::getAttachmentType, attachmentType)
                .eq(MaterialApplyAttachmentEntity::getIsDelete, TrueOrFalseConstant.NOT_DELETED));
        HashMap<Integer, MaterialApplyAttachmentSaveDto> originDtoMap = new HashMap<>();
        // 新增的
        List<MaterialApplyAttachmentEntity> newCardEntities = fabricImgList.stream().filter(materialApplyAttachmentSaveDto -> {
            if (Objects.isNull(materialApplyAttachmentSaveDto.getAttachmentId())) {
                return true;
            }
            originDtoMap.putIfAbsent(materialApplyAttachmentSaveDto.getAttachmentId(), materialApplyAttachmentSaveDto);
            return false;
        }).map(materialApplyAttachmentSaveDto -> {
            MaterialApplyAttachmentEntity attachmentEntity = new MaterialApplyAttachmentEntity();
            BeanUtilsEx.copyProperties(materialApplyAttachmentSaveDto, attachmentEntity);
            attachmentEntity.setAttachmentType(attachmentType);
            attachmentEntity.setIsDelete(TrueOrFalseConstant.NOT_DELETED);
            attachmentEntity.setApplyId(applyId);
            return attachmentEntity;
        }).collect(Collectors.toList());
        // 更新
        existAttachments.forEach(attachmentEntity -> {
            MaterialApplyAttachmentSaveDto attachmentSaveDto = originDtoMap.get(attachmentEntity.getAttachmentId());
            if (attachmentSaveDto == null) {
                attachmentEntity.setIsDelete(TrueOrFalseConstant.DELETED);
            }
        });
        if (!newCardEntities.isEmpty()) {
            existAttachments.addAll(newCardEntities);
        }
        saveOrUpdateBatch(existAttachments);
    }
}
