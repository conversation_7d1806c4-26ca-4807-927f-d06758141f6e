package com.nsy.scm.business.service.purchase.refund;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.scm.repository.entity.purchase.refund.PurchaseRefundTaskEntity;
import com.nsy.scm.business.request.purchase.refund.PurchaseRefundTaskAddRequest;
import com.nsy.scm.business.request.purchase.refund.PurchaseRefundTaskHandFeedbackRequest;
import com.nsy.scm.business.request.purchase.refund.PurchaseRefundTaskPageRequest;
import com.nsy.scm.business.response.purchase.refund.PurchaseRefundTaskPageResponse;
import com.nsy.api.scm.dto.request.common.IdListRequest;
import com.nsy.api.scm.dto.response.common.PageResponse;

import java.util.List;

public interface PurchaseRefundTaskService extends IService<PurchaseRefundTaskEntity> {

    /**
     * 分页查询
     */
    PageResponse<PurchaseRefundTaskPageResponse> pageList(PurchaseRefundTaskPageRequest request);

    /**
     * 批量转处理中
     */
    void changeStatus(IdListRequest request);

    /**
     * 生成任务
     */
    void createTask(PurchaseRefundTaskAddRequest request);

    /**
     * 处理反馈
     */
    void handleFeedback(PurchaseRefundTaskHandFeedbackRequest request);

    void createPurchaseRefundOrder(PurchaseRefundTaskEntity taskEntity);

    /**
     * 更新 采购单退货退款任务-状态(已处理->处理中)，并清空处理方式，问题类型等内容
     * @param taskEntity 更新集合
     */
    void changeStatusToProcessing(List<PurchaseRefundTaskEntity> taskEntity);

    /**
     * 已处理的【其他】退回处理中
     */
    void returnProcessing(IdListRequest request);
}
