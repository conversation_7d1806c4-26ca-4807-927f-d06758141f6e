package com.nsy.scm.business.service.upload;

import com.nsy.scm.business.service.upload.base.QuartzUploadQueueTypeEnum;
import com.nsy.scm.business.service.upload.base.UploadRequest;
import com.nsy.scm.business.service.upload.base.UploadResponse;

public interface IUploadService {
    /**
     * 上传队列类型
     */
    QuartzUploadQueueTypeEnum uploadType();

    /**
     * 处理上传数据
     */
    UploadResponse processUploadData(UploadRequest request);
}
