package com.nsy.scm.business.request.purchase.spot;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@ApiModel(value = "SpotInStorageAddPurchasePlanRequest", description = "现货备货加入采购 request")
public class SpotInStorageAddPurchasePlanRequest {

    @NotEmpty(message = "明细集合 不可为空")
    @Size(max = 50, message = "一次最多操作50条")
    @ApiModelProperty(value = "明细集合", required = true)
    @Valid
    private List<Item> itemList;


    public List<Item> getItemList() {
        return itemList;
    }

    public void setItemList(List<Item> itemList) {
        this.itemList = itemList;
    }

    @ApiModel(value = "SpotInStorageAddPurchasePlanRequest.Item", description = "现货备货加入采购明细")
    public static class Item {
        @ApiModelProperty(value = "specId", required = true)
        @NotNull
        private Integer specId;
        @ApiModelProperty(value = "采购数", required = true)
        @NotNull(message = "采购数 不可为空")
        @Min(value = 1, message = "采购数必须大于0")
        private Integer purchaseQty;

        @ApiModelProperty(value = "现货链接id", required = true)
        @NotNull(message = "现货链接id 不可为空")
        private Integer supplierProductSpecId;

        public Integer getSpecId() {
            return specId;
        }

        public void setSpecId(Integer specId) {
            this.specId = specId;
        }

        public Integer getPurchaseQty() {
            return purchaseQty;
        }

        public void setPurchaseQty(Integer purchaseQty) {
            this.purchaseQty = purchaseQty;
        }

        public Integer getSupplierProductSpecId() {
            return supplierProductSpecId;
        }

        public void setSupplierProductSpecId(Integer supplierProductSpecId) {
            this.supplierProductSpecId = supplierProductSpecId;
        }
    }
}
