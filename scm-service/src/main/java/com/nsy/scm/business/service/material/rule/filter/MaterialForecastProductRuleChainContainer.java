package com.nsy.scm.business.service.material.rule.filter;

import com.alibaba.excel.util.CollectionUtils;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 预测商品规则配置详情表 职责链容器
 */
public class MaterialForecastProductRuleChainContainer<T> {

    // 任务链
    private List<MaterialForecastProductRuleInterface<T>> ruleChain = Lists.newArrayList();


    /**
     * 添加过滤规则
     */
    public MaterialForecastProductRuleChainContainer<T> append(MaterialForecastProductRuleInterface<T> rule) {
        if (rule == null) {
            return this;
        }
        ruleChain.add(rule);
        return this;
    }


    // 提交处理
    public final List<T> submit(List<T> datas) {
        // 没有配置规则
        if (CollectionUtils.isEmpty(datas)) {
            return datas;
        }
        List<T> chainDatas = Lists.newArrayList(datas.iterator());
        for (MaterialForecastProductRuleInterface<T> chain : ruleChain) {
            if (CollectionUtils.isEmpty(chainDatas)) {
                return chainDatas;
            }
            // 执行过滤链
            chainDatas = chain.dealWith(chainDatas);
        }
        return chainDatas;
    }

    public List<MaterialForecastProductRuleInterface<T>> getRuleChain() {
        return ruleChain;
    }

    public void setRuleChain(List<MaterialForecastProductRuleInterface<T>> ruleChain) {
        this.ruleChain = ruleChain;
    }
}
