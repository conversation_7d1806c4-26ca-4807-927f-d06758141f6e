package com.nsy.scm.business.manage.peer.supplier.request;

import com.nsy.scm.business.manage.peer.supplier.domain.ChangePurchaseOrderItemLaunchFifoRemark;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0.0 2023.02.28
 */
@ApiModel(value = "ChangePurchaseOrderItemLaunchFifoRemarkRequest", description = "SCM修改采购单明细的发起快进快出标识 - 请求体")
public class ChangePurchaseOrderItemLaunchFifoRemarkRequest {

    @ApiModelProperty(value = "修改采购单明细的发起快进快出标识-请求体", name = "items")
    @NotEmpty(message = "items不能为空")
    @Valid
    private List<ChangePurchaseOrderItemLaunchFifoRemark> items = new ArrayList<>();

    @ApiModelProperty(value = "操作人", name = "operator")
    private String operator;

    public List<ChangePurchaseOrderItemLaunchFifoRemark> getItems() {
        return items;
    }

    public void setItems(List<ChangePurchaseOrderItemLaunchFifoRemark> items) {
        this.items = items;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}

