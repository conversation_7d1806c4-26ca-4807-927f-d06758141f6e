package com.nsy.scm.business.manage.mes.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.util.Set;

/**
 * 启用账号请求
 *
 * <AUTHOR>
 * @since 2022/8/10 16:09
 */
@ApiModel(value = "EnableAccountRequest", description = "启用账号请求")
public class EnableAccountRequest {

    @ApiModelProperty(name = "mainAccount", value = "主账号，传当前登录用户账号")
    private String mainAccount;

    @ApiModelProperty(name = "userAccounts", value = "userAccount集合", required = true)
    @NotEmpty(message = "用户名不可为空")
    private Set<String> userAccounts;

    public Set<String> getUserAccounts() {
        return userAccounts;
    }

    public void setUserAccounts(Set<String> userAccounts) {
        this.userAccounts = userAccounts;
    }

    public String getMainAccount() {
        return mainAccount;
    }

    public void setMainAccount(String mainAccount) {
        this.mainAccount = mainAccount;
    }
}
