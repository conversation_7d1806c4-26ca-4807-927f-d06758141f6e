package com.nsy.scm.business.request.purchase.spot;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Set;

@ApiModel(value = "SpotPausePurchaseRequest", description = "暂停采购 request")
public class SpotPausePurchaseRequest {

    @ApiModelProperty(value = "计划单主键Id", required = true)
    @NotEmpty(message = "purchasePlanIds 不可为空")
    private Set<Integer> purchasePlanIds;

    @ApiModelProperty(value = "reason", required = true)
    @NotBlank(message = "reason 不可为空")
    private String reason;

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Set<Integer> getPurchasePlanIds() {
        return purchasePlanIds;
    }

    public void setPurchasePlanIds(Set<Integer> purchasePlanIds) {
        this.purchasePlanIds = purchasePlanIds;
    }
}
