package com.nsy.scm.business.domain.flow;

import com.google.common.collect.Lists;
import com.nsy.scm.enumstable.product.revision.FlowTaskProductRevisionLogTypeEnum;
import com.nsy.scm.enumstable.product.revision.FlowTaskProductRevisionStatusEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 改版流程任务状态机
 *
 * <AUTHOR>
 * @since 2022/12/29 14:38
 */
public class RevisionProductFlowTaskStatusMachine {

    /**
     * 任务id
     */
    private final Integer taskId;

    private final String spu;
    /**
     * 当前状态
     */
    private FlowTaskProductRevisionStatusEnum currentStatus;

    private final List<Log> logs;

    public RevisionProductFlowTaskStatusMachine(Integer taskId, String spu, Integer currentStatusCode) {
        FlowTaskProductRevisionStatusEnum revisionStatusEnum = FlowTaskProductRevisionStatusEnum.getInstance(currentStatusCode);
        if (FlowTaskProductRevisionStatusEnum.UNKNOWN == revisionStatusEnum) {
            throw new IllegalArgumentException("illegal currentStatusCode");
        }
        this.currentStatus = revisionStatusEnum;
        this.taskId = taskId;
        this.spu = spu;
        this.logs = Lists.newArrayList();
    }

    public RevisionProductFlowTaskStatusMachine(Integer taskId, String spu, FlowTaskProductRevisionStatusEnum currentStatus) {
        this.taskId = taskId;
        this.spu = spu;
        this.currentStatus = currentStatus;
        this.logs = Lists.newArrayList();
    }

    /**
     * 强制取消
     *
     * @return true if cancel success
     */
    public boolean forceCancel(String operatorName, String remark) {
        if (FlowTaskProductRevisionStatusEnum.CANNOT_FORCE_CANCEL_NUMBER_LIST.contains(currentStatus.getNumber())) {
            return false;
        }
        this.currentStatus = FlowTaskProductRevisionStatusEnum.CANCELLED;
        this.addLog(FlowTaskProductRevisionLogTypeEnum.FORCE_CANCEL_TASK, String.format("%s%s%s,商品编码:%s", operatorName, FlowTaskProductRevisionLogTypeEnum.FORCE_CANCEL_TASK.getDesc(), StringUtils.isEmpty(remark) ? "" : String.format(",备注:%s", remark), spu));
        return true;
    }

    private void addLog(FlowTaskProductRevisionLogTypeEnum logType, String content) {
        Log log = new Log();
        log.logType = logType;
        log.content = content;
        this.logs.add(log);
    }


    public Integer getTaskId() {
        return taskId;
    }

    public String getSpu() {
        return spu;
    }

    public FlowTaskProductRevisionStatusEnum getCurrentStatus() {
        return currentStatus;
    }

    public List<Log> getLogs() {
        return Collections.unmodifiableList(logs);
    }

    public class Log {
        private FlowTaskProductRevisionLogTypeEnum logType;

        private String content;

        public FlowTaskProductRevisionLogTypeEnum getLogType() {
            return logType;
        }

        public String getLogTypeName() {
            return Optional.ofNullable(logType).map(FlowTaskProductRevisionLogTypeEnum::name).orElse(StringUtils.EMPTY);
        }

        public String getContent() {
            return content;
        }

        public Integer getTaskId() {
            return RevisionProductFlowTaskStatusMachine.this.taskId;
        }

        public String getSpu() {
            return RevisionProductFlowTaskStatusMachine.this.spu;
        }
    }
}
