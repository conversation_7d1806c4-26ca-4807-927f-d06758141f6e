package com.nsy.scm.business.manage.purchase;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @create 2025-04-18 09:22
 */
@ApiModel(value = "PurchaseOrderItemBarcodeInfoDto", description = "采购单明细条码信息")
public class PurchaseOrderItemBarcodeInfoDto {

    @ApiModelProperty(value = "采购单明细id", name = "orderItemId")
    private Integer orderItemId;

    @ApiModelProperty(value = "店铺sku", name = "sellerSku")
    private String sellerSku;

    @ApiModelProperty(value = "业务条码", name = "sellerBarcode")
    private String sellerBarcode;

    @ApiModelProperty(value = "业务描述", name = "sellerTitle")
    private String sellerTitle;

    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    public String getSellerBarcode() {
        return sellerBarcode;
    }

    public void setSellerBarcode(String sellerBarcode) {
        this.sellerBarcode = sellerBarcode;
    }

    public String getSellerTitle() {
        return sellerTitle;
    }

    public void setSellerTitle(String sellerTitle) {
        this.sellerTitle = sellerTitle;
    }
}
