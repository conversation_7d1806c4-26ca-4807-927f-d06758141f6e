package com.nsy.scm.business.response.bom;

import com.nsy.scm.business.domain.bom.ProductBomAttachmentDto;
import com.nsy.scm.business.domain.bom.ProductSkcBomMaterialOfferingDto;
import com.nsy.scm.business.domain.bom.ProductSkcBomSecondaryDesignDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 排单计划页面-bom清单-详情
 * <AUTHOR>
 * @create 2023-12-29 09:50
 */
@ApiModel(value = "SchedulingOrderPlanBomDetailResponse", description = "排单计划页面-bom清单-详情")
public class SchedulingOrderPlanBomDetailResponse {

    @ApiModelProperty(value = "齐码纸样&工艺单", name = "productBomAttachmentDtoList")
    private List<ProductBomAttachmentDto> productBomAttachmentDtoList;

    @ApiModelProperty(value = "面辅料明细", name = "productSkcBomMaterialOfferingDtoList")
    private List<ProductSkcBomMaterialOfferingDto> productSkcBomMaterialOfferingDtoList;

    @ApiModelProperty(value = "二次工艺", name = "productSkcBomSecondaryDesignDtoList")
    private List<ProductSkcBomSecondaryDesignDto> productSkcBomSecondaryDesignDtoList;

    public List<ProductBomAttachmentDto> getProductBomAttachmentDtoList() {
        return productBomAttachmentDtoList;
    }

    public void setProductBomAttachmentDtoList(List<ProductBomAttachmentDto> productBomAttachmentDtoList) {
        this.productBomAttachmentDtoList = productBomAttachmentDtoList;
    }

    public List<ProductSkcBomMaterialOfferingDto> getProductSkcBomMaterialOfferingDtoList() {
        return productSkcBomMaterialOfferingDtoList;
    }

    public void setProductSkcBomMaterialOfferingDtoList(List<ProductSkcBomMaterialOfferingDto> productSkcBomMaterialOfferingDtoList) {
        this.productSkcBomMaterialOfferingDtoList = productSkcBomMaterialOfferingDtoList;
    }

    public List<ProductSkcBomSecondaryDesignDto> getProductSkcBomSecondaryDesignDtoList() {
        return productSkcBomSecondaryDesignDtoList;
    }

    public void setProductSkcBomSecondaryDesignDtoList(List<ProductSkcBomSecondaryDesignDto> productSkcBomSecondaryDesignDtoList) {
        this.productSkcBomSecondaryDesignDtoList = productSkcBomSecondaryDesignDtoList;
    }
}
