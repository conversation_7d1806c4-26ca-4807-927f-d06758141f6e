package com.nsy.scm.business.response.material.forecast;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.nsy.api.scm.dto.domain.material.MaterialDto;
import com.nsy.api.scm.dto.domain.material.MaterialSupplierColorCardDto;
import com.nsy.api.scm.dto.domain.material.MaterialSupplierInfoDto;
import com.nsy.scm.enumstable.material.MaterialTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * ColorCardDemandPageVo
 *
 * <AUTHOR>
 * @since 2023/3/15 10:46
 */
@ApiModel(value = "PageColorCardDemandVo", description = "分页查询结果")
public class PageColorCardDemandVo extends PageSupplierColorCardVo {

    @ApiModelProperty(value = "需备料花型（色卡）id", name = "preparationColorCardId")
    private Integer preparationColorCardId;

    @ApiModelProperty(value = "需备料物料信息", name = "materialInfo")
    private MaterialInfoVo materialInfo;

    @ApiModelProperty(value = "预测汇总量-商品销量预测量", name = "salesForecast")
    private Integer salesForecast = 0;

    @ApiModelProperty(value = "预测汇总量-需补货商品量", name = "replenishDemand")
    private Integer replenishDemand = 0;

    @ApiModelProperty(value = "预测汇总量-物料预测总量", name = "colorCardDemand")
    private Integer colorCardDemandForecast = 0;

    @ApiModelProperty(value = "预测汇总量-备料中总量", name = "colorCardPreparation")
    private Integer colorCardPreparationQty = 0;

    @ApiModelProperty(value = "需备料总量", name = "colorCardDemandRequire")
    private Integer colorCardDemandRequireQty = 0;

    @ApiModelProperty(value = "计划备料量（米/个/条）", name = "colorCardPlanQty")
    private Integer colorCardPlanQty = 0;

    @ApiModelProperty(value = "计划备料量（kg）", name = "colorCardPlanWeight")
    private BigDecimal colorCardPlanWeight = BigDecimal.ZERO;

    @ApiModelProperty(value = "计划备料金额", name = "colorCardPlanAmount")
    private BigDecimal colorCardPlanAmount = BigDecimal.ZERO;

    public Integer getPreparationColorCardId() {
        return preparationColorCardId;
    }

    public void setPreparationColorCardId(Integer preparationColorCardId) {
        this.preparationColorCardId = preparationColorCardId;
    }

    public MaterialInfoVo getMaterialInfo() {
        return materialInfo;
    }

    public void setMaterialInfo(MaterialInfoVo materialInfo) {
        this.materialInfo = materialInfo;
    }

    public Integer getSalesForecast() {
        return salesForecast;
    }

    public void setSalesForecast(Integer salesForecast) {
        this.salesForecast = salesForecast;
    }

    public Integer getReplenishDemand() {
        return replenishDemand;
    }

    public void setReplenishDemand(Integer replenishDemand) {
        this.replenishDemand = replenishDemand;
    }

    public Integer getColorCardDemandForecast() {
        return colorCardDemandForecast;
    }

    public void setColorCardDemandForecast(Integer colorCardDemandForecast) {
        this.colorCardDemandForecast = colorCardDemandForecast;
    }

    public Integer getColorCardPreparationQty() {
        return colorCardPreparationQty;
    }

    public void setColorCardPreparationQty(Integer colorCardPreparationQty) {
        this.colorCardPreparationQty = colorCardPreparationQty;
    }

    public Integer getColorCardDemandRequireQty() {
        return colorCardDemandRequireQty;
    }

    public void setColorCardDemandRequireQty(Integer colorCardDemandRequireQty) {
        this.colorCardDemandRequireQty = colorCardDemandRequireQty;
    }

    public Integer getColorCardPlanQty() {
        return colorCardPlanQty;
    }

    public void setColorCardPlanQty(Integer colorCardPlanQty) {
        this.colorCardPlanQty = colorCardPlanQty;
    }

    public BigDecimal getColorCardPlanWeight() {
        return colorCardPlanWeight;
    }

    public void setColorCardPlanWeight(BigDecimal colorCardPlanWeight) {
        this.colorCardPlanWeight = colorCardPlanWeight;
    }

    public BigDecimal getColorCardPlanAmount() {
        return colorCardPlanAmount;
    }

    public void setColorCardPlanAmount(BigDecimal colorCardPlanAmount) {
        this.colorCardPlanAmount = colorCardPlanAmount;
    }

    @JsonIgnore
    public BigDecimal fetchUnitPrice() {
        BdColorCardVo colorCard = getColorCard();
        if (colorCard == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal unitPrice = MaterialTypeEnum.FABRIC.name().equals(getMaterialCategory())
                ? colorCard.getWholePrice()
                : colorCard.getUnitPrice();
        return Optional.ofNullable(unitPrice).orElse(BigDecimal.ZERO);
    }

    @ApiModel(value = "MaterialForecastColorCardDemandPageVo.MaterialInfoVo", description = "需备料物料信息")
    public static class MaterialInfoVo {
        @ApiModelProperty("面料Id")
        private Integer materialId;
        @ApiModelProperty("面料统称")
        private String materialName;
        @ApiModelProperty("物料类型")
        private String materialCategory;
        @ApiModelProperty("物料类型描述")
        private String materialCategoryDesc;


        @ApiModelProperty("成分")
        private String ingredient;

        @ApiModelProperty(value = "克重", name = "grammage")
        private BigDecimal grammage;

        @ApiModelProperty(value = "幅宽", name = "breadth")
        private BigDecimal netBreadth;

        @ApiModelProperty(value = "花型颜色", name = "colorDictionaryItemValueDesc")
        private String colorDictionaryItemValueDesc;
        @ApiModelProperty(value = "花型", name = "floralStyleDesc")
        private String floralStyleDesc;


        public static MaterialInfoVo build(MaterialDto materialDto,
                                           MaterialSupplierInfoDto materialSupplierInfoDto,
                                           MaterialSupplierColorCardDto materialSupplierColorCardDto) {
            MaterialInfoVo materialInfoVo = new MaterialInfoVo();
            materialInfoVo.setMaterialId(materialDto.getMaterialId());
            String materialCategory = materialDto.getMaterialCategory();
            materialInfoVo.materialCategory = materialCategory;
            materialInfoVo.materialCategoryDesc = MaterialTypeEnum.getInstance(materialCategory).getDesc();
            materialInfoVo.materialName = materialDto.getMaterialName();

            materialInfoVo.ingredient = materialSupplierInfoDto.getIngredientDesc();
            materialInfoVo.grammage = materialSupplierInfoDto.getGrammage();
            materialInfoVo.netBreadth = materialSupplierInfoDto.getNetBreadth();

            materialInfoVo.colorDictionaryItemValueDesc = materialSupplierColorCardDto.getColorDictionaryItemValueDesc();
            materialInfoVo.floralStyleDesc = materialSupplierColorCardDto.getFloralStyleDesc();
            return materialInfoVo;
        }

        public Integer getMaterialId() {
            return materialId;
        }

        public void setMaterialId(Integer materialId) {
            this.materialId = materialId;
        }

        public String getMaterialCategoryDesc() {
            return materialCategoryDesc;
        }

        public void setMaterialCategoryDesc(String materialCategoryDesc) {
            this.materialCategoryDesc = materialCategoryDesc;
        }

        public String getMaterialName() {
            return materialName;
        }

        public void setMaterialName(String materialName) {
            this.materialName = materialName;
        }

        public String getMaterialCategory() {
            return materialCategory;
        }

        public void setMaterialCategory(String materialCategory) {
            this.materialCategory = materialCategory;
        }

        public String getIngredient() {
            return ingredient;
        }

        public void setIngredient(String ingredient) {
            this.ingredient = ingredient;
        }

        public BigDecimal getGrammage() {
            return grammage;
        }

        public void setGrammage(BigDecimal grammage) {
            this.grammage = grammage;
        }

        public BigDecimal getNetBreadth() {
            return netBreadth;
        }

        public void setNetBreadth(BigDecimal netBreadth) {
            this.netBreadth = netBreadth;
        }

        public String getColorDictionaryItemValueDesc() {
            return colorDictionaryItemValueDesc;
        }

        public void setColorDictionaryItemValueDesc(String colorDictionaryItemValueDesc) {
            this.colorDictionaryItemValueDesc = colorDictionaryItemValueDesc;
        }

        public String getFloralStyleDesc() {
            return floralStyleDesc;
        }

        public void setFloralStyleDesc(String floralStyleDesc) {
            this.floralStyleDesc = floralStyleDesc;
        }
    }


}
