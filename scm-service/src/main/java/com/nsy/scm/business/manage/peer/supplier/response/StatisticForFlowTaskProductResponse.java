package com.nsy.scm.business.manage.peer.supplier.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 1.0.0 2024.02.05
 */
@ApiModel(value = "StatisticForFlowTaskProductResponse", description = "改版任务查询统计信息")
public class StatisticForFlowTaskProductResponse {

    @ApiModelProperty("商品ID")
    private String spu;

    @ApiModelProperty("下单过的供应商，供应商名称多个,好隔开")
    private String supplierName;

    @ApiModelProperty("在途数量/未生产数量")
    private Integer notProducedProductQuantity;

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Integer getNotProducedProductQuantity() {
        return notProducedProductQuantity;
    }

    public void setNotProducedProductQuantity(Integer notProducedProductQuantity) {
        this.notProducedProductQuantity = notProducedProductQuantity;
    }
}
