package com.nsy.scm.business.service.download.impl;

import com.nsy.api.core.apicore.response.CustomExcelResponse;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.NsyExcelUtil;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.scm.dto.request.material.MaterialPageRequest;
import com.nsy.api.scm.dto.response.common.PageResponse;
import com.nsy.scm.business.domain.download.MaterialFabricExport;
import com.nsy.scm.business.response.material.MaterialPageForMaterialRes;
import com.nsy.scm.business.service.download.IDownloadService;
import com.nsy.scm.business.service.download.base.DownloadRequest;
import com.nsy.scm.business.service.download.base.DownloadResponse;
import com.nsy.scm.business.service.download.base.QuartzDownloadQueueTypeEnum;
import com.nsy.scm.business.service.material.impl.MaterialServiceFactory;
import com.nsy.scm.enumstable.material.MaterialTypeEnum;
import com.nsy.scm.utils.JsonMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <h3>公司面料列表-导出实现类</h3>
 *
 * <AUTHOR> Chao
 * @since 2024/04/26 09:37
 */
@Service
public class MaterialFabricListDownloadServiceImpl implements IDownloadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MaterialFabricListDownloadServiceImpl.class);

    @Autowired
    private MaterialServiceFactory materialServiceFactory;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.SCM_MATERIAL_FABRIC_LIST_EXPORT;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        LOGGER.info("供应链系统-公司面料列表接收到的下载请求: {}", JsonMapper.toJson(request));
        DownloadResponse downloadResponse = new DownloadResponse();
        MaterialPageRequest downRequest = JsonMapper.fromJson(request.getRequestContent(), MaterialPageRequest.class);
        downRequest.setPageIndex(request.getPageIndex());
        downRequest.setPageSize(request.getPageSize());
        PageResponse<MaterialPageForMaterialRes> dataList = materialServiceFactory.route(MaterialTypeEnum.FABRIC).pageForMaterial(downRequest);

        CustomExcelResponse excelResponse = new CustomExcelResponse();
        excelResponse.setHeaders(NsyExcelUtil.getCommonHeads(MaterialFabricExport.class));

        List<List<Object>> data = new ArrayList<>();
        dataList.getContent().forEach(i -> buildExport(i).forEach(e -> data.add(NsyExcelUtil.getData(MaterialFabricExport.class, e))));
        excelResponse.setData(data);

        downloadResponse.setTotalCount(dataList.getTotalCount());
        downloadResponse.setDataJsonStr(JsonMapper.toJson(excelResponse));
        LOGGER.info("供应链系统-公司面料列表返回数据的条数: {}", dataList.getContent().size());
        return downloadResponse;
    }

    private List<MaterialFabricExport> buildExport(MaterialPageForMaterialRes data) {
        if (CollectionUtils.isEmpty(data.getMaterialColorList())) {
            MaterialFabricExport export = new MaterialFabricExport();
            BeanUtilsEx.copyProperties(data, export);
            export.setMaterialLabel(StringUtils.hasText(data.getMaterialLabel()) ? String.format("%s%s", data.getMaterialLabel(), data.getMaterialLabelEffectiveDate()) : "");
            return Collections.singletonList(export);
        }
        return data.getMaterialColorList().stream().map(color -> {
            MaterialFabricExport export = new MaterialFabricExport();
            BeanUtilsEx.copyProperties(data, export);
            BeanUtilsEx.copyProperties(color, export);
            export.setMaterialLabel(StringUtils.hasText(data.getMaterialLabel()) ? String.format("%s%s", data.getMaterialLabel(), data.getMaterialLabelEffectiveDate()) : "");
            export.setMaterialColorLabel(StringUtils.hasText(color.getMaterialColorLabel()) ? String.format("%s%s", color.getMaterialColorLabel(), color.getMaterialColorLabelEffectiveDate()) : "");
            return export;
        }).collect(Collectors.toList());
    }
}
