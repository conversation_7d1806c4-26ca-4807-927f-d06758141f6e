package com.nsy.scm.business.manage.pms;

import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.nsy.api.scm.dto.domain.bd.AttachmentDto;
import com.nsy.api.scm.dto.domain.product.ProductRelationshipDto;
import com.nsy.api.scm.dto.request.product.LeadGenerationLabelScmRequest;
import com.nsy.api.scm.dto.request.product.ReplenishmentEliminateCutPitLabelScmRequest;
import com.nsy.api.scm.dto.request.product.SkcImageInfo;
import com.nsy.api.scm.dto.response.product.LeadGenerationLabelScmResponse;
import com.nsy.api.scm.dto.response.product.ReplenishmentEliminateCutPitLabelScmResponse;
import com.nsy.scm.business.domain.product.ProductSkcInfringeValidResult;
import com.nsy.scm.business.domain.product.workmanship.Workmanship;
import com.nsy.scm.business.domain.purchase.stronglyrecommend.BusinessTypeSpuEliminateLabel;
import com.nsy.scm.business.manage.fds.feign.ProductProcessExecutionFeignClient;
import com.nsy.scm.business.manage.pms.dto.ProductDevelopWorkmanshipDto;
import com.nsy.scm.business.manage.pms.dto.ProductRequirementDto;
import com.nsy.scm.business.manage.pms.dto.ProductSkcInfringeValidDTO;
import com.nsy.scm.business.manage.pms.dto.ProductSkuDto;
import com.nsy.scm.business.manage.pms.feign.PmsApiClient;
import com.nsy.scm.business.manage.pms.feign.ProductDevelopWorkmanshipFeignClient;
import com.nsy.scm.business.manage.pms.dto.NotifyPmsFirstApplyDto;
import com.nsy.scm.business.response.product.PurchaseApplyProductSpecResponse;
import com.nsy.scm.constant.replenishment.ReplenishmentEliminateCutPitLabelTypeEnum;
import com.nsy.scm.enums.product.attachment.ProductDevelopAttachmentTypeEnum;
import com.nsy.scm.enumstable.material.sample.FlowTaskMaterialSampleAttachmentFlagEnum;
import com.nsy.scm.enumstable.product.attachment.ProductAttachmentTypeEnum;
import com.nsy.scm.enumstable.product.revision.FlowTaskProductRevisionStatusEnum;
import com.nsy.scm.repository.dao.flow.FlowTaskProductRevisionDao;
import com.nsy.scm.repository.entity.flow.FlowTaskProductRevisionEntity;
import com.nsy.scm.utils.mp.TenantContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * PmsApi 接口
 *
 * <AUTHOR>
 * @since 2022/9/23 14:06
 */
@Service
public class PmsApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PmsApiService.class);

    @Autowired
    private PmsApiClient pmsApiClient;
    @Autowired
    private ProductDevelopWorkmanshipFeignClient developWorkmanshipFeignClient;
    @Autowired
    protected FlowTaskProductRevisionDao flowTaskProductRevisionDao;
    @Autowired
    protected ProductProcessExecutionFeignClient productProcessExecutionFeignClient;

    public ProductSkcInfringeValidResult validSkcInfringement(Set<String> skcSet) {
        if (CollectionUtils.isEmpty(skcSet)) {
            return new ProductSkcInfringeValidResult(Collections.emptyList());
        }
        List<ProductSkcInfringeValidDTO> results = Lists.newArrayList();
        Iterables.partition(skcSet, 200).forEach(partition -> {
            List<ProductSkcInfringeValidDTO> list = pmsApiClient.validSkcInfringement(partition).getContent();
            if (CollectionUtils.isNotEmpty(list)) {
                results.addAll(list);
            }
        });
        return new ProductSkcInfringeValidResult(results);
    }

    /**
     * @return 由于历史原因，商品那边存在商品开款工艺附件为空(注意：不是null，是附件集合为空)的情况
     * 返回null,表示查询出错。
     */
    public Workmanship getProductDevelopWorkmanship(Integer productId) {
        if (productId == null) {
            LOGGER.info("getProductDevelopWorkmanship ignore, empty productId");
            return null;
        }
        try {
            ProductDevelopWorkmanshipDto productDevelopWorkmanship = developWorkmanshipFeignClient.getProductDevelopWorkmanship(productId);
            List<Workmanship.Attachment> attachments = productDevelopWorkmanship.getDevelopAttachments()
                    .stream()
                    .map(attachmentDto -> {
                        ProductAttachmentTypeEnum productAttachmentType = ProductDevelopAttachmentTypeEnum.getInstanceByPmsEnumName(attachmentDto.getType())
                                .correspondingProductAttachmentType();
                        return new Workmanship.Attachment(productAttachmentType, attachmentDto.getOriginName(), attachmentDto.getName(),
                                attachmentDto.getUrl(), attachmentDto.getCreateBy(), attachmentDto.getCreateDate());
                    }).collect(Collectors.toList());
            return new Workmanship(attachments);
        } catch (RuntimeException e) {
            LOGGER.error("getProductDevelopWorkmanship error, productId = {}, cause {}", productId, e.getMessage(), e);
            return null;
        }
    }

    public List<ProductRequirementDto> productRequirementInfos(Collection<Integer> productIds) {
        try {
            return Optional.ofNullable(pmsApiClient.getProductRequirements(new ArrayList<>(productIds)))
                    .orElse(Collections.emptyList());
        } catch (RuntimeException e) {
            LOGGER.error("productRequirementInfos error, productIds = {}, cause {}", productIds, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public List<ProductSkuDto> listEmbryoSku(List<ProductSkuDto> list) {
        try {
            return Optional.ofNullable(pmsApiClient.listEmbryoSku(list))
                    .orElse(Collections.emptyList());
        } catch (RuntimeException e) {
            LOGGER.error("listEmbryoSku error, list = {}, cause {}", list, e.getMessage(), e);
        }
        return Collections.emptyList();
    }


    public Map<String, List<AttachmentDto>> flowTaskMaterialSampleGetSkcImg(List<Integer> productIds) {
        Map<String, List<AttachmentDto>> res = new HashMap<>();
        if (CollectionUtils.isEmpty(productIds)) {
            return res;
        }
        Map<String, SkcImageInfo> map = pmsApiClient.getSkcImageInfoMapByProductIds(productIds);
        if (map == null || map.isEmpty()) {
            return res;
        }
        map.forEach((skc, dto1) -> {
            List<AttachmentDto> list1 = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(dto1.getArtistImages())) {
                list1.addAll(dto1.getArtistImages().stream()
                        .map(s -> buildAttachmentDto(s)).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(dto1.getDevelopImages())) {
                // 商品图取前三条(包含一张主图)
                list1.addAll(dto1.getDevelopImages().stream()
                        .map(s -> buildAttachmentDto(s)).limit(3).collect(Collectors.toList()));
            }
            res.put(skc, list1);
        });
        return res;
    }

    private AttachmentDto buildAttachmentDto(SkcImageInfo.Attach s) {
        AttachmentDto attachmentDto1 = new AttachmentDto();
        attachmentDto1.setAttachmentUrl(s.getUrl());
        attachmentDto1.setAttachmentName(s.getName());
        attachmentDto1.setOriginName(s.getOriginName());
        // 商品主图
        attachmentDto1.setAttachmentFlag(s.getIsMain() == 1 ? FlowTaskMaterialSampleAttachmentFlagEnum.PRODUCT_MAIN.name() : "");
        return attachmentDto1;
    }

    public List<ProductRelationshipDto> findByProductRelationship(List<Integer> productIds) {
        return pmsApiClient.findByProductRelationship(productIds);
    }


    public List<BusinessTypeSpuEliminateLabel> querySpuWithTheEliminationTag(@RequestBody List<String> spuList) {
        return pmsApiClient.querySpuWithTheEliminationTag(spuList);
    }

    public List<LeadGenerationLabelScmResponse> getLeadGenerationLabelList(LeadGenerationLabelScmRequest request) {
        return pmsApiClient.getLeadGenerationLabelList(request);
    }

    public List<ProductRelationshipDto> findSameByProductRelationship(List<Integer> productIds) {
        List<ProductRelationshipDto> productRelationshipDtos = Lists.newArrayList();
        try {
            productRelationshipDtos = pmsApiClient.findSameByProductRelationship(productIds);
            if (CollectionUtils.isEmpty(productRelationshipDtos)) {
                return productRelationshipDtos;
            }
            Map<Integer, List<FlowTaskProductRevisionEntity>> flowTaskMap = flowTaskProductRevisionDao.mapBySpusAndTaskStatus(productRelationshipDtos.stream().map(ProductRelationshipDto::getRelatedProductId).collect(Collectors.toList()), FlowTaskProductRevisionStatusEnum.MISI_NUMBER_LIST);
            productRelationshipDtos.removeIf(p -> CollectionUtils.isNotEmpty(flowTaskMap.get(p.getRelatedProductId())));
        } catch (RuntimeException e) {
            LOGGER.error("pms:findSameByProductRelationship error, list = {}, cause {}", productIds, e.getMessage(), e);
        }
        return productRelationshipDtos;
    }

    public List<ReplenishmentEliminateCutPitLabelScmResponse> queryEliminateCutPitLabel(Map<String, PurchaseApplyProductSpecResponse> productSpecMap) {
        Collection<PurchaseApplyProductSpecResponse> specList = productSpecMap.values();
        if (CollectionUtils.isEmpty(specList)) {
            return Collections.emptyList();
        }
        ReplenishmentEliminateCutPitLabelScmRequest request = new ReplenishmentEliminateCutPitLabelScmRequest();
        request.setSpuList(specList.stream().map(PurchaseApplyProductSpecResponse::getSpu).distinct().collect(Collectors.toList()));
        request.setSkcList(specList.stream().map(PurchaseApplyProductSpecResponse::getSkc).distinct().collect(Collectors.toList()));
        request.setSkuList(specList.stream().map(PurchaseApplyProductSpecResponse::getSku).distinct().collect(Collectors.toList()));
        request.setLabelNameList(ReplenishmentEliminateCutPitLabelTypeEnum.getEliminateLabelList());
        request.setLocation(TenantContext.getTenant());
        return pmsApiClient.getAllTypeEliminateList(request);
    }

    public void notifyPmsFirstApply(List<NotifyPmsFirstApplyDto> notifyRequestList) {
        if (CollectionUtils.isEmpty(notifyRequestList)) {
            return;
        }
        pmsApiClient.notifyPmsFirstApply(notifyRequestList);
    }
}
