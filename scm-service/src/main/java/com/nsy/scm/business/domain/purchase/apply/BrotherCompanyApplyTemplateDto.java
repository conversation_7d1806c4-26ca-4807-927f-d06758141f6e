package com.nsy.scm.business.domain.purchase.apply;

import com.google.common.collect.Lists;
import com.nsy.scm.business.response.product.PurchaseApplyProductSpecResponse;
import com.nsy.scm.constant.bulk.PurchaseDepartmentEnum;
import com.nsy.scm.repository.entity.purchase.bulk.PurchaseApplyItemEntity;
import com.nsy.scm.utils.DateTimeUtils;
import com.nsy.scm.utils.IntegerUtils;
import com.nsy.scm.utils.mp.TenantContext;

import java.text.ParseException;
import java.util.List;
import java.util.Objects;

/**
 * <h3>兄弟公司申请单导入数据模板DTO</h3>
 *
 * <AUTHOR>
 * @since 2024/03/28 11:56
 */
public class BrotherCompanyApplyTemplateDto extends BaseApplyTempDto {

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 规格编码
     */
    private String sku;

    /**
     * 期望到货日期
     */
    private String expectedArrivalDate;

    /**
     * sellerSku
     */
    private String sellerSku;

    /**
     * 商品编码
     */
    private String spu;

    /**
     * 颜色编码
     */
    private String skc;

    /**
     * 店铺id
     */
    private Integer storeId;

    /**
     * 小组Id
     */
    private Integer groupId;

    /**
     * 小组名称
     */
    private String groupName;

    /**
     * 仓库id
     */
    private Integer spaceId;

    /**
     * 仓库名称
     */
    private String spaceName;

    /**
     * 品牌id
     */
    private Integer brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 店铺所属平台Id
     */
    private Integer storePlatformId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商id
     */
    private Integer supplierId;

    /**
     * 备注
     */
    private String remark;


    /**
     * 条码模版
     */
    private String barcodeTemplate;

    private String businessType;

    private String sellerBarcode;

    private String sellerTitle;


    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    @Override
    public String getSku() {
        return sku;
    }

    @Override
    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getExpectedArrivalDate() {
        return expectedArrivalDate;
    }

    public void setExpectedArrivalDate(String expectedArrivalDate) {
        this.expectedArrivalDate = expectedArrivalDate;
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Integer getStorePlatformId() {
        return storePlatformId;
    }

    public void setStorePlatformId(Integer storePlatformId) {
        this.storePlatformId = storePlatformId;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getSellerBarcode() {
        return sellerBarcode;
    }

    public void setSellerBarcode(String sellerBarcode) {
        this.sellerBarcode = sellerBarcode;
    }

    public String getSellerTitle() {
        return sellerTitle;
    }

    public void setSellerTitle(String sellerTitle) {
        this.sellerTitle = sellerTitle;
    }

    @Override
    public List<PurchaseApplyItemEntity> buildPurchaseApplyItemEntity(String userName, PurchaseApplyProductSpecResponse productSpec, boolean configIsOpenAndShortOrder) throws ParseException {
        List<PurchaseApplyItemEntity> ls = Lists.newArrayListWithExpectedSize(1);
        PurchaseApplyItemEntity entity = new PurchaseApplyItemEntity();
        entity.setSpaceId(this.getSpaceId());
        entity.setSpaceName(this.getSpaceName());
        entity.setBrandId(this.getBrandId());
        entity.setBrandName(this.getBrandName());
        entity.setSellerSku(this.getSellerSku());
        entity.setSellerBarcode(this.getSellerBarcode());
        entity.setSellerTitle(this.getSellerTitle());
        entity.setBarcodeTemplate(this.getBarcodeTemplate());
        entity.setApplyName(this.getApplyName());
        entity.setApplyQty(this.getApplyQty());
        entity.setSupplierId(IntegerUtils.null2Zero(this.supplierId));
        entity.setSku(this.getSku());
        entity.setStatus(this.getStatus());
        entity.setUseCompanyStock(0);
        entity.setUsePurchaseQty(0);
        entity.setAddPlanQty(0);
        entity.setCancelQty(0);
        entity.setCanceledQty(0);
        entity.setApplyReceivedQty(0);
        entity.setTid("");
        entity.setDevLog("");
        entity.setFifoQty(0);
        entity.setUseBaseSkuStock(0);
        if (Objects.nonNull(this.getExpectedArrivalDate())) {
            entity.setExpectedArrivalDate(DateTimeUtils.strictParseDate(this.getExpectedArrivalDate(), "yyyy/MM/dd"));
        }
        entity.appendRemark("");
        entity.setProductId(productSpec.getProductId());
        entity.setSpu(productSpec.getSpu());
        entity.setSpecId(productSpec.getSpecId());
        entity.setSkc(productSpec.getSkc());
        entity.setSkuSize(productSpec.getSkuSize());
        entity.setLocation(TenantContext.getTenant());
        entity.setCreateBy(userName);
        entity.setUpdateBy(userName);
        entity.setHandleDepartmentId(PurchaseDepartmentEnum.getByLocation(TenantContext.getTenant()).getValue());
        entity.setRemark(this.getRemark());
        ls.add(entity);
        return ls;
    }

    public String getUniqueKey() {
        String keyFormat = "店铺名称：%s；SKU：%s；预计到货日期：%s；";
        return String.format(keyFormat, this.getStoreName(), this.getSku(), this.getExpectedArrivalDate());
    }

    public String getStoreAndSpaceKey() {
        return String.format("%s-%s", getStoreName(), getSpaceName());
    }

    public String getStoreAndSpaceKey(String splitSpu, boolean isSplitBySpu) {
        String key = String.format("%s-%s", getStoreName(), getSpaceName());
        return isSplitBySpu ? key + "-" + splitSpu : key;
    }

    public String getStoreAndSpaceKey(String splitSpu, boolean isSplitBySpu, String expectedArrivalDate) {
        String key = String.format("%s-%s-%s", getStoreName(), getSpaceName(), expectedArrivalDate);
        return isSplitBySpu ? key + "-" + splitSpu : key;
    }

    public String getStoreKey(String splitSpu, boolean isSplitBySpu) {
        return isSplitBySpu ? getStoreName() + "-" + splitSpu : getStoreName();
    }

    public String getStoreKey(String splitSpu, boolean isSplitBySpu, String expectedArrivalDate) {
        String key = String.format("%s-%s", getStoreName(), expectedArrivalDate);
        return isSplitBySpu ? key + "-" + splitSpu : key;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getBarcodeTemplate() {
        return barcodeTemplate;
    }

    public void setBarcodeTemplate(String barcodeTemplate) {
        this.barcodeTemplate = barcodeTemplate;
    }
}
