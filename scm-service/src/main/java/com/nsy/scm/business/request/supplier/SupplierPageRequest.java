package com.nsy.scm.business.request.supplier;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.nsy.api.scm.dto.request.common.PageRequest;
import com.nsy.scm.constant.DictionaryNameEnum;
import com.nsy.scm.enumstable.supplier.SupplierOperateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@ApiModel(value = "SupplierPageRequest", description = "供应商-分页查询条件")
public class SupplierPageRequest extends PageRequest implements Serializable {
    private static final long serialVersionUID = 8462132567780168851L;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称", name = "supplierName")
    private List<String> supplierNameList;
    /**
     * 供应商简称
     */
    @ApiModelProperty(value = "供应商简称", name = "supplierShortName")
    private List<String> supplierShortNameList;

    @ApiModelProperty(value = "供应商IdList", name = "supplierIds")
    private List<Integer> supplierIds;
    /**
     * 供应编号
     */
    @ApiModelProperty(value = "供应编号", name = "supplierNo")
    private String supplierNo;

    @ApiModelProperty(value = "供应编号List", name = "supplierNos")
    private List<String> supplierNos;
    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码", name = "supplierCode")
    private String supplierCode;
    /**
     * 供应商类型
     */
    @ApiModelProperty(value = "供应商类型", name = "supplierType")
    private List<String> supplierType;

    @ApiModelProperty(value = "供应类型，字典值scm_supplier_supply_type", name = "supplyType")
    private String supplyType;
    /**
     * 擅长品类
     */
    @ApiModelProperty(value = "擅长品类", name = "extensionTypeValues")
    private List<String> extensionTypeValues;
    /**
     * 面料品类
     */
    @ApiModelProperty(value = "面料品类values", name = "fabricTypeValues")
    private List<String> fabricTypeValues;
    /**
     * 生产类型
     */
    @ApiModelProperty(value = "生产类型", name = "productionTypeValues")
    private List<String> productionTypeValues;
    /**
     * 供应商等级字典值
     */
    @ApiModelProperty(value = "供应商等级字典值", name = "supplierLevel")
    private String supplierLevel;
    /**
     * 合作状态
     */
    @ApiModelProperty(value = "合作状态", name = "cooperateStatus")
    private String cooperateStatus;

    @ApiModelProperty(value = "合作状态", name = "cooperateStatusList")
    private List<String> cooperateStatusList;

    /**
     * 归属（对接）部门编码
     */
    @ApiModelProperty(value = "归属（对接）部门编码", name = "affiliateDeptCode")
    private List<String> affiliateDeptCodeList;
    private List<Integer> affiliateDeptIds;
    /**
     * 采购对接人员工编码
     */
    @ApiModelProperty(value = "采购对接人员工编码", name = "contactPurchaserEmpCode")
    private List<String> contactPurchaserEmpCode;
    private List<Integer> contactPurchaserEmpIds;

    @ApiModelProperty(value = "样板跟单员id列表", name = "modelMerchandiserEmpIds")
    private List<Integer> modelMerchandiserEmpIds;


    @ApiModelProperty(value = "面料跟单员id列表", name = "fabricMerchandiserIds")
    private List<Integer> fabricMerchandiserIds;
    /**
     * 国家
     */
    @ApiModelProperty(value = "国家", name = "country")
    private String country;

    /**
     * 省
     */
    @ApiModelProperty(value = "省", name = "province")
    private String province;

    /**
     * 市
     */
    @ApiModelProperty(value = "市", name = "city")
    private String city;

    /**
     * 区、县
     */
    @ApiModelProperty(value = "区、县", name = "district")
    private String district;
    /**
     * 开始合作日期
     */
    @ApiModelProperty(value = "开始合作日期", name = "cooperateDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date cooperateDate;

    @ApiModelProperty(value = "开始合作日期-开始", name = "cooperateDateStart")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date cooperateDateStart;

    @ApiModelProperty(value = "开始合作日期-结束", name = "cooperateDateEnd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date cooperateDateEnd;

    @ApiModelProperty(value = "开发员编码", name = "businessSpecialistCode")
    private String businessSpecialistCode;
    private Integer businessSpecialistId;

    @ApiModelProperty(value = "是否自行送货, (0-否 1-是)", name = "selfDelivery")
    private Integer selfDelivery;

    @ApiModelProperty(value = "是否可开发票. 0-不可开，1-可开发票", example = "1")
    private Integer canInvoice;

    @ApiModelProperty(value = "是否有合同. 0-否，1-是", example = "isContract")
    private Integer isContract;

    @ApiModelProperty(value = "是否资金风险，0-否，1-是", name = "isFinancialRisk")
    private Integer isFinancialRisk;

    @ApiModelProperty(value = "是否包含运费，0-否，1-是", name = "isIncludeCourierFees")
    private Integer isIncludeCourierFees;

    /**
     * 合作模式
     */
    @ApiModelProperty(value = "合作模式", name = "cooperationMode")
    private String cooperationMode;
    /**
     * 擅长品类 - 标识
     */
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String productCategory = DictionaryNameEnum.PRODUCT_CATEGORY.getParentKey();

    /**
     * 面料品类 - 标识
     */
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String scmFabricType = DictionaryNameEnum.SCM_FABRIC_TYPE.getParentKey();
    /**
     * 生产类型 - 标识
     */
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String scmProductionType = DictionaryNameEnum.SCM_PRODUCTION_TYPE.getParentKey();

    @ApiModelProperty(value = "参与集采标识", example = "inPurchase")
    private Integer inPurchase;

    private Long totalCount;

    private List<Integer> selectIdList;

    @ApiModelProperty(value = "淘汰日期-开始", name = "signOutDateStart")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signOutDateStart;

    @ApiModelProperty(value = "淘汰日期-结束", name = "signOutDateEnd")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signOutDateEnd;

    @ApiModelProperty(value = "日志操作类型", name = "logOperateType")
    private String logOperateType;

    public List<Integer> getFabricMerchandiserIds() {
        return fabricMerchandiserIds;
    }

    public void setFabricMerchandiserIds(List<Integer> fabricMerchandiserIds) {
        this.fabricMerchandiserIds = fabricMerchandiserIds;
    }

    public List<String> getSupplierNameList() {
        return supplierNameList;
    }

    public void setSupplierNameList(List<String> supplierNameList) {
        this.supplierNameList = supplierNameList;
    }

    public List<String> getSupplierShortNameList() {
        return supplierShortNameList;
    }

    public void setSupplierShortNameList(List<String> supplierShortNameList) {
        this.supplierShortNameList = supplierShortNameList;
    }

    public String getSupplierNo() {
        return supplierNo;
    }

    public void setSupplierNo(String supplierNo) {
        this.supplierNo = supplierNo;
    }

    public List<String> getSupplierType() {
        return supplierType;
    }

    public void setSupplierType(List<String> supplierType) {
        this.supplierType = supplierType;
    }

    public List<String> getExtensionTypeValues() {
        return extensionTypeValues;
    }

    public void setExtensionTypeValues(List<String> extensionTypeValues) {
        this.extensionTypeValues = extensionTypeValues;
    }

    public List<String> getCooperateStatusList() {
        return cooperateStatusList;
    }

    public void setCooperateStatusList(List<String> cooperateStatusList) {
        this.cooperateStatusList = cooperateStatusList;
    }

    public List<String> getFabricTypeValues() {
        return fabricTypeValues;
    }

    public void setFabricTypeValues(List<String> fabricTypeValues) {
        this.fabricTypeValues = fabricTypeValues;
    }

    public String getSupplierLevel() {
        return supplierLevel;
    }

    public void setSupplierLevel(String supplierLevel) {
        this.supplierLevel = supplierLevel;
    }

    public String getCooperateStatus() {
        return cooperateStatus;
    }

    public void setCooperateStatus(String cooperateStatus) {
        this.cooperateStatus = cooperateStatus;
    }

    public List<String> getContactPurchaserEmpCode() {
        return contactPurchaserEmpCode;
    }

    public void setContactPurchaserEmpCode(List<String> contactPurchaserEmpCode) {
        this.contactPurchaserEmpCode = contactPurchaserEmpCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public Date getCooperateDate() {
        return cooperateDate;
    }

    public void setCooperateDate(Date cooperateDate) {
        this.cooperateDate = cooperateDate;
    }

    public List<String> getAffiliateDeptCodeList() {
        return affiliateDeptCodeList;
    }

    public void setAffiliateDeptCodeList(List<String> affiliateDeptCodeList) {
        this.affiliateDeptCodeList = affiliateDeptCodeList;
    }

    public String getProductCategory() {
        return productCategory;
    }

    public void setProductCategory(String productCategory) {
        this.productCategory = productCategory;
    }

    public String getScmFabricType() {
        return scmFabricType;
    }

    public void setScmFabricType(String scmFabricType) {
        this.scmFabricType = scmFabricType;
    }

    public List<String> getProductionTypeValues() {
        return productionTypeValues;
    }

    public void setProductionTypeValues(List<String> productionTypeValues) {
        this.productionTypeValues = productionTypeValues;
    }

    public String getBusinessSpecialistCode() {
        return businessSpecialistCode;
    }

    public void setBusinessSpecialistCode(String businessSpecialistCode) {
        this.businessSpecialistCode = businessSpecialistCode;
    }

    public String getCooperationMode() {
        return cooperationMode;
    }

    public void setCooperationMode(String cooperationMode) {
        this.cooperationMode = cooperationMode;
    }

    public String getScmProductionType() {
        return scmProductionType;
    }

    public void setScmProductionType(String scmProductionType) {
        this.scmProductionType = scmProductionType;
    }

    public Integer getSelfDelivery() {
        return selfDelivery;
    }

    public void setSelfDelivery(Integer selfDelivery) {
        this.selfDelivery = selfDelivery;
    }

    public Integer getCanInvoice() {
        return canInvoice;
    }

    public void setCanInvoice(Integer canInvoice) {
        this.canInvoice = canInvoice;
    }

    public Integer getBusinessSpecialistId() {
        return businessSpecialistId;
    }

    public void setBusinessSpecialistId(Integer businessSpecialistId) {
        this.businessSpecialistId = businessSpecialistId;
    }

    public List<Integer> getAffiliateDeptIds() {
        return affiliateDeptIds;
    }

    public void setAffiliateDeptIds(List<Integer> affiliateDeptIds) {
        this.affiliateDeptIds = affiliateDeptIds;
    }

    public List<Integer> getContactPurchaserEmpIds() {
        return contactPurchaserEmpIds;
    }

    public void setContactPurchaserEmpIds(List<Integer> contactPurchaserEmpIds) {
        this.contactPurchaserEmpIds = contactPurchaserEmpIds;
    }

    public List<Integer> getModelMerchandiserEmpIds() {
        return modelMerchandiserEmpIds;
    }

    public void setModelMerchandiserEmpIds(List<Integer> modelMerchandiserEmpIds) {
        this.modelMerchandiserEmpIds = modelMerchandiserEmpIds;
    }

    public String getSupplyType() {
        return supplyType;
    }

    public void setSupplyType(String supplyType) {
        this.supplyType = supplyType;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public List<String> getSupplierNos() {
        return supplierNos;
    }

    public void setSupplierNos(List<String> supplierNos) {
        this.supplierNos = supplierNos;
    }

    public List<Integer> getSupplierIds() {
        return supplierIds;
    }

    public void setSupplierIds(List<Integer> supplierIds) {
        this.supplierIds = supplierIds;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public Date getCooperateDateStart() {
        return cooperateDateStart;
    }

    public void setCooperateDateStart(Date cooperateDateStart) {
        this.cooperateDateStart = cooperateDateStart;
    }

    public Date getCooperateDateEnd() {
        return cooperateDateEnd;
    }

    public void setCooperateDateEnd(Date cooperateDateEnd) {
        this.cooperateDateEnd = cooperateDateEnd;
    }

    public Integer getIsContract() {
        return isContract;
    }

    public void setIsContract(Integer isContract) {
        this.isContract = isContract;
    }

    public Integer getIsFinancialRisk() {
        return isFinancialRisk;
    }

    public void setIsFinancialRisk(Integer isFinancialRisk) {
        this.isFinancialRisk = isFinancialRisk;
    }

    @Override
    public List<Integer> getSelectIdList() {
        return selectIdList;
    }

    @Override
    public void setSelectIdList(List<Integer> selectIdList) {
        this.selectIdList = selectIdList;
    }

    public Integer getInPurchase() {
        return inPurchase;
    }

    public void setInPurchase(Integer inPurchase) {
        this.inPurchase = inPurchase;
    }

    public Date getSignOutDateStart() {
        return signOutDateStart;
    }

    public void setSignOutDateStart(Date signOutDateStart) {
        this.signOutDateStart = signOutDateStart;
    }

    public Date getSignOutDateEnd() {
        return signOutDateEnd;
    }

    public void setSignOutDateEnd(Date signOutDateEnd) {
        this.signOutDateEnd = signOutDateEnd;
    }

    public String getLogOperateType() {
        return logOperateType;
    }

    public void setLogOperateType(String logOperateType) {
        this.logOperateType = logOperateType;
    }

    public Integer getIsIncludeCourierFees() {
        return isIncludeCourierFees;
    }

    public void setIsIncludeCourierFees(Integer isIncludeCourierFees) {
        this.isIncludeCourierFees = isIncludeCourierFees;
    }

    public void buildRequest() {
        if (Objects.nonNull(signOutDateStart) && Objects.nonNull(signOutDateEnd)) {
            logOperateType = SupplierOperateTypeEnum.UPDATE_STATUS_SIGN_OUT.getDesc();
        }
    }
}
