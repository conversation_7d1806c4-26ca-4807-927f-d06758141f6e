package com.nsy.scm.business.service.retry.impl;

import com.google.common.collect.Lists;
import com.nsy.scm.mq.KafkaTopicConstant;
import com.nsy.scm.mq.QMessage;
import com.nsy.scm.mq.consumer.PurchaseReceivedForBranchCompanySyncConsumer;
import com.nsy.scm.mq.message.TopicSyncPurchaseReceivedForBranchCompanyMessage;
import com.nsy.scm.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 工厂直发消息重试策略
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Component
public class TopicPurchaseReceivedSyncForBranchCompanyRetryStrategy extends AbsKafkaConsumeRetryStrategy<TopicSyncPurchaseReceivedForBranchCompanyMessage.TopicSyncPurchaseReceivedForBranchCompanyMsgBody> {

    @Autowired
    private PurchaseReceivedForBranchCompanySyncConsumer purchaseReceivedForBranchCompanySyncConsumer;

    @Override
    public List<String> supportTopics() {
        return Lists.newArrayList(KafkaTopicConstant.WMS_SYNC_PURCHASE_RECEIVED_FOR_BRANCH_COMPANY_TOPIC);
    }

    @Override
    public QMessage<TopicSyncPurchaseReceivedForBranchCompanyMessage.TopicSyncPurchaseReceivedForBranchCompanyMsgBody> convertMsgContent(String msgContent) {
        return JsonMapper.fromJson(msgContent, TopicSyncPurchaseReceivedForBranchCompanyMessage.class);
    }

    @Override
    public void doProcess(QMessage<TopicSyncPurchaseReceivedForBranchCompanyMessage.TopicSyncPurchaseReceivedForBranchCompanyMsgBody> message) {
        purchaseReceivedForBranchCompanySyncConsumer.retryProcessMessage(message);
    }
}
