package com.nsy.scm.business.response.material;


import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "TreeForErpResponse", description = "全部数据 - 树状")
public class TreeForErpResponse implements Serializable {

    private static final long serialVersionUID = 7574071569323332706L;

    private String text;
    private Integer value;
    private boolean checked = false;
    private boolean collapsed = false;
    private List<TreeForErpResponse> children;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public List<TreeForErpResponse> getChildren() {
        return children;
    }

    public void setChildren(List<TreeForErpResponse> children) {
        this.children = children;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public boolean isCollapsed() {
        return collapsed;
    }

    public void setCollapsed(boolean collapsed) {
        this.collapsed = collapsed;
    }
}
