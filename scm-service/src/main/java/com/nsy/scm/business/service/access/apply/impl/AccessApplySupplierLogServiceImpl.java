package com.nsy.scm.business.service.access.apply.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.scm.business.manage.peer.user.dto.SysUserInfo;
import com.nsy.scm.enumstable.supplier.SupplierTypeEnum;
import com.nsy.scm.repository.entity.AccessApplySupplierEntity;
import com.nsy.scm.repository.entity.AccessApplySupplierLogEntity;
import com.nsy.scm.repository.sql.mapper.AccessApplySupplierLogMapper;
import com.nsy.scm.business.service.access.apply.AccessApplySupplierLogService;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.scm.dto.domain.access.apply.supplier.AccessApplySupplierLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商准入申请单日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-01
 */
@Service
public class AccessApplySupplierLogServiceImpl extends ServiceImpl<AccessApplySupplierLogMapper, AccessApplySupplierLogEntity> implements AccessApplySupplierLogService {

    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private AccessApplySupplierServiceFactory accessApplySupplierServiceFactory;

    @Override
    public void add(AccessApplySupplierEntity accessApplySupplierEntity, String operateType, String content) {
        AccessApplySupplierLogEntity accessApplySupplierLogEntity = new AccessApplySupplierLogEntity();
        accessApplySupplierLogEntity.setAccessApplyId(accessApplySupplierEntity.getAccessApplyId());
        accessApplySupplierLogEntity.setSupplierName(accessApplySupplierEntity.getSupplierName());
        accessApplySupplierLogEntity.setContent(content);
        accessApplySupplierLogEntity.setOperateType(operateType);
        accessApplySupplierLogEntity.setIp(loginInfoService.getIpAddress());
        accessApplySupplierLogEntity.setOperatorEmpCode(loginInfoService.getUserCode());
        accessApplySupplierLogEntity.setOperatorEmpName(loginInfoService.getName());
        save(accessApplySupplierLogEntity);
    }

    @Override
    public void add(AccessApplySupplierEntity accessApplySupplierEntity, String operateType, String content, SysUserInfo userInfo) {
        AccessApplySupplierLogEntity accessApplySupplierLogEntity = new AccessApplySupplierLogEntity();
        accessApplySupplierLogEntity.setAccessApplyId(accessApplySupplierEntity.getAccessApplyId());
        accessApplySupplierLogEntity.setSupplierName(accessApplySupplierEntity.getSupplierName());
        accessApplySupplierLogEntity.setContent(content);
        accessApplySupplierLogEntity.setOperateType(operateType);
        accessApplySupplierLogEntity.setIp("");
        accessApplySupplierLogEntity.setOperatorEmpCode(userInfo.getUserCode());
        accessApplySupplierLogEntity.setOperatorEmpName(userInfo.getUserName());
        save(accessApplySupplierLogEntity);
    }


    @Override
    public List<AccessApplySupplierLog> list(Integer accessApplyId) {

        accessApplySupplierServiceFactory.route(SupplierTypeEnum.FABRIC_SUPPLIER).getBeanById(accessApplyId);
        LambdaQueryWrapper<AccessApplySupplierLogEntity> wrapper = new LambdaQueryWrapper();
        wrapper.eq(AccessApplySupplierLogEntity::getAccessApplyId, accessApplyId);
        wrapper.orderByDesc(AccessApplySupplierLogEntity::getLogId);
        return super.list(wrapper).stream().map(e -> {
            AccessApplySupplierLog accessApplySupplierLog = new AccessApplySupplierLog();
            BeanUtilsEx.copyProperties(e, accessApplySupplierLog);
            return accessApplySupplierLog;
        }).collect(Collectors.toList());
    }
}
