package com.nsy.scm.business.manage.wms.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 获取所有待上架的采购单信息
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@ApiModel(value = "QueryAllWaitUpShelvesInfoResponse", description = "获取所有待上架的采购单信息")
public class QueryAllWaitShelveInfoResponse implements Serializable {

    private static final long serialVersionUID = -5917431873969413693L;
    @ApiModelProperty("采购计划单号")
    public String purchasePlanNo;

    @ApiModelProperty("规格编码")
    public String sku;

    @ApiModelProperty("待上架数")
    public Integer waitUpShelfQty;

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getWaitUpShelfQty() {
        return waitUpShelfQty;
    }

    public void setWaitUpShelfQty(Integer waitUpShelfQty) {
        this.waitUpShelfQty = waitUpShelfQty;
    }
}
