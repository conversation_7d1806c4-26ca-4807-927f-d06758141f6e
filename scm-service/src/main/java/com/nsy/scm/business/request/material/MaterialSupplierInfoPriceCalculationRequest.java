package com.nsy.scm.business.request.material;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <h3>供应商面料相关价格计算请求</h3>
 *
 * <AUTHOR>
 * @since 2024/11/15 17:41
 */
@ApiModel(value = "MaterialSupplierInfoPriceCalculationRequest", description = "供应商面料相关价格计算请求")
public class MaterialSupplierInfoPriceCalculationRequest {

    @ApiModelProperty("面料分类ID")
    @NotNull(message = "面料分类ID不能为空")
    private Integer materialTypeId;

    @ApiModelProperty("物料类别")
    @NotBlank(message = "物料类别不能为空")
    private String materialCategory;

    @ApiModelProperty("面料：足米价")
    private BigDecimal wholePrice;

    @ApiModelProperty("面料：公斤价，辅料：单价")
    private BigDecimal unitPrice;

    @ApiModelProperty("空差")
    private BigDecimal emptyDifferenceDesc;

    @ApiModelProperty("包边幅宽")
    private BigDecimal grossBreadth;

    @ApiModelProperty("有效幅宽")
    private BigDecimal netBreadth;

    @ApiModelProperty("克重")
    private BigDecimal grammage;

    public Integer getMaterialTypeId() {
        return materialTypeId;
    }

    public void setMaterialTypeId(Integer materialTypeId) {
        this.materialTypeId = materialTypeId;
    }

    public String getMaterialCategory() {
        return materialCategory;
    }

    public void setMaterialCategory(String materialCategory) {
        this.materialCategory = materialCategory;
    }

    public BigDecimal getWholePrice() {
        return wholePrice;
    }

    public void setWholePrice(BigDecimal wholePrice) {
        this.wholePrice = wholePrice;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getEmptyDifferenceDesc() {
        return emptyDifferenceDesc;
    }

    public void setEmptyDifferenceDesc(BigDecimal emptyDifferenceDesc) {
        this.emptyDifferenceDesc = emptyDifferenceDesc;
    }

    public BigDecimal getGrossBreadth() {
        return grossBreadth;
    }

    public void setGrossBreadth(BigDecimal grossBreadth) {
        this.grossBreadth = grossBreadth;
    }

    public BigDecimal getNetBreadth() {
        return netBreadth;
    }

    public void setNetBreadth(BigDecimal netBreadth) {
        this.netBreadth = netBreadth;
    }

    public BigDecimal getGrammage() {
        return grammage;
    }

    public void setGrammage(BigDecimal grammage) {
        this.grammage = grammage;
    }
}
