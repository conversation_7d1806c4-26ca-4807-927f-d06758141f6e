package com.nsy.scm.business.service.purchase.status.machine.plan;

import com.nsy.scm.constant.StatusMachineKeyConstant;
import com.nsy.scm.constant.bulk.PurchasePlanLogTypeEnum;
import com.nsy.scm.constant.purchase.spot.SpotPlanOperationType;
import com.nsy.scm.constant.purchase.spot.SpotPurchasePlanStatus;
import com.nsy.scm.business.domain.StatusMachineContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.atomic.AtomicReference;


/**
 * 采买中
 */
@Component
public class PurchasingPlanStatus extends AbstractSpotPurchasePlanStatus {

    private static final AtomicReference<PurchasingPlanStatus> INSTANCE = new AtomicReference<>();

    public static PurchasingPlanStatus getInstance() {
        return INSTANCE.get();
    }

    @PostConstruct
    public void initInstance() {
        INSTANCE.set(this);
    }

    @Override
    public SpotPurchasePlanStatus getName() {
        return SpotPurchasePlanStatus.PURCHASING;
    }

    @Override
    public void returnBack(SpotPurchasePlanStatusMachine statusMachine, StatusMachineContext statusMachineContext) {
        super.returnBackLogic(statusMachine, statusMachineContext);
    }

    @Override
    public void revertPurchase(SpotPurchasePlanStatusMachine machine, StatusMachineContext statusMachineContext) {
        String reason = (String) statusMachineContext.getValue(StatusMachineKeyConstant.REASON);
        machine.getCurrentPlan().addLog(PurchasePlanLogTypeEnum.CANCEL_PURCHASE, "取消采购单，回滚状态为待采买：" + reason);
        // 判断是否要合并
        if (mergeIfNecessary(machine, SpotPlanOperationType.REVERT_PURCHASE)) {
            return;
        }
        // 改为待采买
        machine.setCurrentStatus(PendingPurchasePlanStatus.getInstance());
    }
}
