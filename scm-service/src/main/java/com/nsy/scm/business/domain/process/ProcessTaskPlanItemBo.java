package com.nsy.scm.business.domain.process;

public class ProcessTaskPlanItemBo {

    /**
     * 订单ID
     */

    private String orderItemId;

    /**
     * 计划数
     */
    private Integer planQty;

    /**
     * 拣货数
     */
    private Integer pickQty;

    /**
     * 分拣数
     */
    private Integer pendingQty;

    /**
     * 完成加工数
     */
    private Integer completeQty;

    /**
     * 加工任务明细ID
     */
    private Integer taskItemId;

    /**
     * 加工任务ID
     */
    private Integer processTaskId;

    /**
     * 明细状态
     */
    private String itemStatus;

    /**
     * 胚款Sku
     */
    private String baseSku;

    /**
     * 定制款Sku
     */
    private String customSku;


    public String getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(String orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Integer getPickQty() {
        return pickQty;
    }

    public void setPickQty(Integer pickQty) {
        this.pickQty = pickQty;
    }

    public Integer getPendingQty() {
        return pendingQty;
    }

    public void setPendingQty(Integer pendingQty) {
        this.pendingQty = pendingQty;
    }

    public Integer getCompleteQty() {
        return completeQty;
    }

    public void setCompleteQty(Integer completeQty) {
        this.completeQty = completeQty;
    }

    public Integer getTaskItemId() {
        return taskItemId;
    }

    public void setTaskItemId(Integer taskItemId) {
        this.taskItemId = taskItemId;
    }

    public Integer getProcessTaskId() {
        return processTaskId;
    }

    public void setProcessTaskId(Integer processTaskId) {
        this.processTaskId = processTaskId;
    }

    public Integer getPlanQty() {
        return planQty;
    }

    public void setPlanQty(Integer planQty) {
        this.planQty = planQty;
    }

    public String getItemStatus() {
        return itemStatus;
    }

    public void setItemStatus(String itemStatus) {
        this.itemStatus = itemStatus;
    }

    public String getBaseSku() {
        return baseSku;
    }

    public void setBaseSku(String baseSku) {
        this.baseSku = baseSku;
    }

    public String getCustomSku() {
        return customSku;
    }

    public void setCustomSku(String customSku) {
        this.customSku = customSku;
    }
}
