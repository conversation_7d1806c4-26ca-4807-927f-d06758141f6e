package com.nsy.scm.enums.material.color;

import com.google.common.collect.ImmutableList;
import com.nsy.scm.utils.Validator;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <h3>花型色卡侵权标签枚举</h3>
 *
 * <AUTHOR>
 * @since 2024/08/29 17:09
 */
public enum MaterialSupplierColorCardInfringementEnum {

    INFRINGEMENT("侵权") {
        @Override
        public void verifyBeforeSet(String newLabel, String currentLabel, String colorCardNo) {
            super.verifyBeforeSet(newLabel, currentLabel, colorCardNo);
            Validator.test(currentLabel, l -> !StringUtils.equals(l, SECOND_CREATION.name()), String.format("色卡[%s]为二创花型无法标记为“侵权”", colorCardNo));
        }
    },
    INFRINGEMENT_CONFIRMING("侵权确认中") {
        @Override
        public void verifyBeforeSet(String newLabel, String currentLabel, String colorCardNo) {
            super.verifyBeforeSet(newLabel, currentLabel, colorCardNo);
            Validator.test(currentLabel, l -> !StringUtils.equals(l, SECOND_CREATION.name()), String.format("色卡[%s]为二创花型无法标记为“侵权确认中”", colorCardNo));
        }
    },
    SECOND_CREATION("二创") {
        @Override
        public void verifyBeforeSet(String newLabel, String currentLabel, String colorCardNo) {
            super.verifyBeforeSet(newLabel, currentLabel, colorCardNo);
            Validator.test(currentLabel,
                    l -> StringUtils.equals(l, INFRINGEMENT.name()) || StringUtils.equals(l, INFRINGEMENT_CONFIRMING.name()),
                    String.format("色卡[%s]非侵权/侵权确认中色卡无法绑定二创花型", colorCardNo));
        }

        @Override
        public void verifyBeforeUnbind(String currentLabel, String colorCardNo) {
            Validator.test(currentLabel, l -> INFRINGEMENT_LIST.contains(currentLabel), String.format("色卡[%s]非侵权/侵权确认中色卡无法取消二创标签", colorCardNo));
        }

        @Override
        public boolean filterBeforeSet(String newLabel, String currentLabel) {
            return true; // 二创色卡无需过滤
        }
    },

    UNKNOWN("未知");

    private String desc;

    public static final List<String> INFRINGEMENT_LIST = ImmutableList.of(INFRINGEMENT.name(), INFRINGEMENT_CONFIRMING.name());

    public static final List<String> INFRINGEMENT_DESC_LIST = ImmutableList.of(INFRINGEMENT.getDesc(), INFRINGEMENT_CONFIRMING.getDesc());

    MaterialSupplierColorCardInfringementEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public static MaterialSupplierColorCardInfringementEnum getInstance(String value) {
        return Arrays.stream(values())
                .filter(item -> item.name().equals(value))
                .findFirst()
                .orElse(UNKNOWN);
    }

    public static String getDescByValue(String value) {
        return Arrays.stream(values())
                .filter(item -> item.name().equals(value))
                .findFirst()
                .map(MaterialSupplierColorCardInfringementEnum::getDesc)
                .orElse(UNKNOWN.getDesc());
    }

    public static boolean exists(String value) {
        return Arrays.stream(values())
                .filter(item -> item != UNKNOWN)
                .anyMatch(item -> item.name().equals(value));
    }

    public void verifyBeforeSet(String newLabel, String currentLabel, String colorCardNo) {
        Validator.exists(newLabel, "请传入侵权标签参数");
        Validator.test(newLabel, MaterialSupplierColorCardInfringementEnum::exists, "侵权标签不正确，请确认");
    }

    public void verifyBeforeClear(String currentLabel, String colorCardNo) {
        Validator.test(currentLabel, l -> StringUtils.equals(l, this.name()), String.format("色卡[%s]非%s色卡无法取消标签", colorCardNo, this.getDesc()));
    }

    public void verifyBeforeUnbind(String currentLabel, String colorCardNo) {

    }

    public boolean filterBeforeSet(String newLabel, String currentLabel) {
        return !StringUtils.equals(newLabel, currentLabel);
    }

}
