package com.nsy.scm.repository.dao.pattern;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.scm.repository.entity.pattern.DevelopPatternAttachmentEntity;
import com.nsy.api.scm.dto.constant.AttachmentTypeEnum;
import com.nsy.api.scm.dto.domain.pattern.DevelopPatternAttachmentDTO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 开款版型附件表 Dao 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
public interface DevelopPatternAttachmentDao extends IService<DevelopPatternAttachmentEntity> {

    /**
     * 根据 attachmentTypeEnum 图片类型 和 developPatternIdList 获取该类型的图片
     *
     * @param developPatternIdList
     * @param attachmentTypeEnum
     * @return
     */
    Map<Integer, List<DevelopPatternAttachmentDTO>> queryDevelopPatternIdAttachmentDTOListMap(List<Integer> developPatternIdList, AttachmentTypeEnum attachmentTypeEnum);

    List<DevelopPatternAttachmentEntity> queryEntitiesByAttachmentAndDevelopPatternIds(AttachmentTypeEnum attachmentTypeEnum, List<Integer> developPatternIds);

    List<DevelopPatternAttachmentDTO> queryDtosByDevelopPatternIdAndAttachmentType(Integer developPatternId, AttachmentTypeEnum attachmentTypeEnum);

}
