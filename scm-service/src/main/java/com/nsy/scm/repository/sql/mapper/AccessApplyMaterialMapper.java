package com.nsy.scm.repository.sql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.scm.repository.entity.AccessApplyMaterialEntity;
import com.nsy.api.scm.dto.domain.material.AccessApplyMaterialAttributeMapping;
import com.nsy.api.scm.dto.domain.material.AccessApplyMaterialPage;
import com.nsy.api.scm.dto.request.access.material.AccessApplyMaterialPageRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 物料准入申请单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
public interface AccessApplyMaterialMapper extends BaseMapper<AccessApplyMaterialEntity> {

    IPage<AccessApplyMaterialPage> getAccessApplyMaterialsByConditions(IPage<AccessApplyMaterialPage> page,
                                                                       @Param("request") AccessApplyMaterialPageRequest request,
                                                                       @Param("attributeMappings") List<AccessApplyMaterialAttributeMapping> attributeMappings);
}
