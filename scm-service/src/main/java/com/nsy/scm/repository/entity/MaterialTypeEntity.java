package com.nsy.scm.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <p>
 * 物料分类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Entity
@Table(name = "material_type")
@TableName("material_type")
public class MaterialTypeEntity extends BaseMpEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer materialTypeId;

    /**
     * 物料类型名称
     */
    private String materialTypeName;

    /**
     * 父物料类型id
     */
    private Integer parentMaterialTypeId;

    /**
     * 父物料类型名称
     */
    private String parentMaterialTypeName;

    /**
     * 深度，树的第几层， 从0开始
     */
    private Integer deep;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否被删除：0-未删除，1-删除
     */
    private int isDelete;


    public Integer getMaterialTypeId() {
        return materialTypeId;
    }

    public void setMaterialTypeId(Integer materialTypeId) {
        this.materialTypeId = materialTypeId;
    }

    public String getMaterialTypeName() {
        return materialTypeName;
    }

    public void setMaterialTypeName(String materialTypeName) {
        this.materialTypeName = materialTypeName;
    }

    public Integer getParentMaterialTypeId() {
        return parentMaterialTypeId;
    }

    public void setParentMaterialTypeId(Integer parentMaterialTypeId) {
        this.parentMaterialTypeId = parentMaterialTypeId;
    }

    public String getParentMaterialTypeName() {
        return parentMaterialTypeName;
    }

    public void setParentMaterialTypeName(String parentMaterialTypeName) {
        this.parentMaterialTypeName = parentMaterialTypeName;
    }

    public Integer getDeep() {
        return deep;
    }

    public void setDeep(Integer deep) {
        this.deep = deep;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }
}
