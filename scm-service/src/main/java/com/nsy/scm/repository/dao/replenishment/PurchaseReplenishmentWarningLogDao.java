package com.nsy.scm.repository.dao.replenishment;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.scm.business.request.replenishment.PurchaseReplenishmentWarningLogPageRequest;
import com.nsy.scm.business.response.replenishment.PurchaseReplenishmentWarningLogPageResponse;
import com.nsy.scm.repository.entity.replenishment.PurchaseReplenishmentWarningLogEntity;

/**
 * 针对表【purchase_replenishment_warning_log(采购补货预警日志表)】的数据库操作Dao
 *
 * <AUTHOR>
 * @since 1.0 2023-02-01
 */
public interface PurchaseReplenishmentWarningLogDao extends IService<PurchaseReplenishmentWarningLogEntity> {


    IPage<PurchaseReplenishmentWarningLogPageResponse> pageQuery(PurchaseReplenishmentWarningLogPageRequest request);

}
