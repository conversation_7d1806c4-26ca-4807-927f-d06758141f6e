package com.nsy.scm.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.enumstable.product.bom.MaterialTypeEnum;
import com.nsy.scm.repository.entity.base.BaseMpEntity;
import com.nsy.api.scm.dto.request.material.MaterialPurchaseSetSupplierInfoRequest;
import com.nsy.scm.repository.entity.material.price.review.FlowTaskMaterialPriceReviewEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <p>
 * 物料供应商信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Entity
@Table(name = "material_supplier_info")
@TableName("material_supplier_info")
public class MaterialSupplierInfoEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer supplierInfoId;

    /**
     * 面料复制来源ID
     */
    private Integer sourceMaterialSupplierInfoId;

    /**
     * 准入申请ID
     */
    private Integer accessApplyId;

    /**
     * 物料id
     */
    private Integer materialId;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 供应商id
     */
    private Integer supplierId;

    // 供应商编码
    @TableField(exist = false)
    private String supplierCode;

    // 供应商名称
    @TableField(exist = false)
    private String supplierName;

    // 总采购数量
    @TableField(exist = false)
    private Integer totalPurchaseQuantity;

    private Integer deliverCount;

    // 总发货
    @TableField(exist = false)
    private Integer totalShippedCount;

    // 面料图片
    @TableField(exist = false)
    private String materialUrl;

    /**
     * 供应商物料编码
     */
    private String supplierMaterialCode;

    /**
     * 供应商物料名称
     */
    private String supplierMaterialName;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 采购价
     */
    private BigDecimal purchasePrice;

    /**
     * 足米价
     */
    private BigDecimal wholePrice;

    /**
     * 价格单位
     */
    private String priceUnit;

    /**
     * 克重
     */
    private BigDecimal grammage;


    private BigDecimal size;

    /**
     * 毛幅宽
     */
    private BigDecimal grossBreadth;

    /**
     * 净幅宽
     */
    private BigDecimal netBreadth;

    /**
     * 成分
     */
    private Integer ingredientId;

    /**
     * 面料开发员工编码
     */
    private String developEmpCode;

    private Integer developEmpId;

    /**
     * 面料开发员工名称
     */
    private String developEmpName;

    /**
     * 货源
     */
    private String supplySource;
    /**
     * 面料风格
     */
    private String fabricStyle;
    /**
     * 空差
     */
    private BigDecimal emptyDifferenceDesc;

    /**
     * 横缩率（%）
     */
    private BigDecimal transverseShrinkRate;

    /**
     * 直缩率（%）
     */
    private BigDecimal directShrinkRate;

    /**
     * 测缩方式 数据字典：scm_measure_shrink_mode
     */
    private String measureShrinkMode;

    /**
     * 测试规格（cm）
     */
    private String testSpecification;

    /**
     * 纱支规格
     */
    private String yarnCount;

    /**
     * 库区/库位
     */
    private String storageArea;

    /**
     * 中文卖点
     */
    private String sellingPointCn;

    /**
     * 大货吊卡 数据字典
     */
    private String largeCargoElevator;

    /**
     * 标识：BULK（集采面料）SPOT（现货面料）
     */
    private String identification;

    /**
     * 是否检测, 0-不检测 1-检测
     */
    private Integer isDetected;

    /**
     * 是否合格：0-不合格，1-合格
     */
    private Integer isQualified;

    /**
     * 是否环保面料
     */
    private Integer isDevFriendly;

    // 申请提交人编码
    private String applyUserCode;

    private Integer applyUserId;

    // 申请提交人名称
    private String applyUserName;

    // 绑定商品数量
    private Integer bindProductCount;

    // 商品销量
    private Integer productSaleVolume;
    // 最近一年商品销量
    private Integer productSaleVolumeLastYear;

    /**
     * b2b 销量
     */
    private Integer b2bSaleVolume;
    /**
     * b2c 销量
     */
    private Integer b2cSaleVolume;
    /**
     * dtc 销量
     */
    private Integer dtcSaleVolume;
    /**
     * 内贸 销量
     */
    private Integer domesticSaleVolume;
    /**
     * 分公司销量
     */
    private Integer branchCompanySaleVolume;
    /**
     * 主公司销量
     */
    private Integer mainCompanySaleVolume;

    // 是否默认供应商
    private Integer isDefault;

    // 是否被删除：0-未删除，1-删除
    private Integer isDelete;

    /**
     * 供应商自备量
     */
    private Integer supplierReserve;

    /**
     * 公司储备量
     */
    private Integer reserveForNsy;

    /**
     * 成衣供应商领取量
     */
    private Integer receivedCount;

    /**
     * 启用标识,enable,disable
     */
    private String enableStatus;

    /**
     * 是否为新增 面料供应商
     */
    @TableField(exist = false)
    private boolean isNew;

    /**
     * 创建公司
     */
    private Integer createByCompany;

    /**
     * 备注
     */
    private String remark;

    public Integer getBindProductCount() {
        return bindProductCount;
    }

    public void setBindProductCount(Integer bindProductCount) {
        this.bindProductCount = bindProductCount;
    }

    public Integer getSupplierInfoId() {
        return supplierInfoId;
    }

    public void setSupplierInfoId(Integer supplierInfoId) {
        this.supplierInfoId = supplierInfoId;
    }

    public Integer getSourceMaterialSupplierInfoId() {
        return sourceMaterialSupplierInfoId;
    }

    public void setSourceMaterialSupplierInfoId(Integer sourceMaterialSupplierInfoId) {
        this.sourceMaterialSupplierInfoId = sourceMaterialSupplierInfoId;
    }

    public Integer getAccessApplyId() {
        return accessApplyId;
    }

    public void setAccessApplyId(Integer accessApplyId) {
        this.accessApplyId = accessApplyId;
    }

    public Integer getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Integer materialId) {
        this.materialId = materialId;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierMaterialCode() {
        return supplierMaterialCode;
    }

    public void setSupplierMaterialCode(String supplierMaterialCode) {
        this.supplierMaterialCode = supplierMaterialCode;
    }

    public String getDevelopEmpCode() {
        return developEmpCode;
    }

    public void setDevelopEmpCode(String developEmpCode) {
        this.developEmpCode = developEmpCode;
    }

    public String getDevelopEmpName() {
        return developEmpName;
    }

    public void setDevelopEmpName(String developEmpName) {
        this.developEmpName = developEmpName;
    }

    public String getSupplierMaterialName() {
        return supplierMaterialName;
    }

    public void setSupplierMaterialName(String supplierMaterialName) {
        this.supplierMaterialName = supplierMaterialName;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public BigDecimal getWholePrice() {
        return wholePrice;
    }

    public void setWholePrice(BigDecimal wholePrice) {
        this.wholePrice = wholePrice;
    }

    public String getPriceUnit() {
        return priceUnit;
    }

    public void setPriceUnit(String priceUnit) {
        this.priceUnit = priceUnit;
    }

    public BigDecimal getGrammage() {
        return grammage;
    }

    public void setGrammage(BigDecimal grammage) {
        this.grammage = grammage;
    }

    public BigDecimal getGrossBreadth() {
        return grossBreadth;
    }

    public void setGrossBreadth(BigDecimal grossBreadth) {
        this.grossBreadth = grossBreadth;
    }

    public BigDecimal getNetBreadth() {
        return netBreadth;
    }

    public void setNetBreadth(BigDecimal netBreadth) {
        this.netBreadth = netBreadth;
    }

    public Integer getIngredientId() {
        return ingredientId;
    }

    public void setIngredientId(Integer ingredientId) {
        this.ingredientId = ingredientId;
    }

    public String getSupplySource() {
        return supplySource;
    }

    public void setSupplySource(String supplySource) {
        this.supplySource = supplySource;
    }

    public String getFabricStyle() {
        return fabricStyle;
    }

    public void setFabricStyle(String fabricStyle) {
        this.fabricStyle = fabricStyle;
    }

    public BigDecimal getEmptyDifferenceDesc() {
        return emptyDifferenceDesc;
    }

    public void setEmptyDifferenceDesc(BigDecimal emptyDifferenceDesc) {
        this.emptyDifferenceDesc = emptyDifferenceDesc;
    }

    public Integer getIsDetected() {
        return isDetected;
    }

    public void setIsDetected(Integer isDetected) {
        this.isDetected = isDetected;
    }

    public String getApplyUserCode() {
        return applyUserCode;
    }

    public void setApplyUserCode(String applyUserCode) {
        this.applyUserCode = applyUserCode;
    }

    public String getApplyUserName() {
        return applyUserName;
    }

    public void setApplyUserName(String applyUserName) {
        this.applyUserName = applyUserName;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public BigDecimal getSize() {
        return size;
    }

    public void setSize(BigDecimal size) {
        this.size = size;
    }

    public Integer getDevelopEmpId() {
        return developEmpId;
    }

    public void setDevelopEmpId(Integer developEmpId) {
        this.developEmpId = developEmpId;
    }

    public Integer getApplyUserId() {
        return applyUserId;
    }

    public void setApplyUserId(Integer applyUserId) {
        this.applyUserId = applyUserId;
    }

    public String getLargeCargoElevator() {
        return largeCargoElevator;
    }

    public void setLargeCargoElevator(String largeCargoElevator) {
        this.largeCargoElevator = largeCargoElevator;
    }

    public String getIdentification() {
        return identification;
    }

    public void setIdentification(String identification) {
        this.identification = identification;
    }

    public Integer getSupplierReserve() {
        return supplierReserve;
    }

    public void setSupplierReserve(Integer supplierReserve) {
        this.supplierReserve = supplierReserve;
    }

    public Integer getReserveForNsy() {
        return reserveForNsy;
    }

    public void setReserveForNsy(Integer reserveForNsy) {
        this.reserveForNsy = reserveForNsy;
    }

    public Integer getReceivedCount() {
        return receivedCount;
    }

    public void setReceivedCount(Integer receivedCount) {
        this.receivedCount = receivedCount;
    }

    public Integer getIsQualified() {
        return isQualified;
    }

    public void setIsQualified(Integer isQualified) {
        this.isQualified = isQualified;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Integer getTotalPurchaseQuantity() {
        return totalPurchaseQuantity;
    }

    public void setTotalPurchaseQuantity(Integer totalPurchaseQuantity) {
        this.totalPurchaseQuantity = totalPurchaseQuantity;
    }

    public Integer getTotalShippedCount() {
        return totalShippedCount;
    }

    public void setTotalShippedCount(Integer totalShippedCount) {
        this.totalShippedCount = totalShippedCount;
    }

    public Integer getProductSaleVolume() {
        return productSaleVolume;
    }

    public void setProductSaleVolume(Integer productSaleVolume) {
        this.productSaleVolume = productSaleVolume;
    }

    public void setMaterialPurchaseSetSupplierInfo(MaterialPurchaseSetSupplierInfoRequest request) {
        this.setIsDefault(request.getIsDefault());
        this.setReserveForNsy(request.getNewReserveForNsy());
        this.setSupplierReserve(request.getNewSupplierReserve());
    }

    /**
     * 可用量 = 自备量 + 公司储备量
     *
     * @return 自备量 + 公司储备量
     */
    public Integer getAvailableCount() {
        if (this.receivedCount == null && this.reserveForNsy == null) {
            return 0;
        }
        int rc = this.receivedCount == null ? 0 : this.receivedCount;
        int rf = this.reserveForNsy == null ? 0 : this.reserveForNsy;
        return rc + rf;
    }

    public String getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(String enableStatus) {
        this.enableStatus = enableStatus;
    }

    public String getMaterialUrl() {
        return materialUrl;
    }

    public void setMaterialUrl(String materialUrl) {
        this.materialUrl = materialUrl;
    }

    public Integer getDeliverCount() {
        return deliverCount;
    }

    public void setDeliverCount(Integer deliverCount) {
        this.deliverCount = deliverCount;
    }

    public Integer getNotReceiveCount() {
        int dc = this.deliverCount == null ? 0 : this.deliverCount;
        int rc = this.receivedCount == null ? 0 : this.receivedCount;
        return Math.max(dc - rc, 0);
    }

    public Integer getB2bSaleVolume() {
        return b2bSaleVolume;
    }

    public void setB2bSaleVolume(Integer b2bSaleVolume) {
        this.b2bSaleVolume = b2bSaleVolume;
    }

    public Integer getB2cSaleVolume() {
        return b2cSaleVolume;
    }

    public void setB2cSaleVolume(Integer b2cSaleVolume) {
        this.b2cSaleVolume = b2cSaleVolume;
    }

    public Integer getDtcSaleVolume() {
        return dtcSaleVolume;
    }

    public void setDtcSaleVolume(Integer dtcSaleVolume) {
        this.dtcSaleVolume = dtcSaleVolume;
    }

    public Integer getDomesticSaleVolume() {
        return domesticSaleVolume;
    }

    public void setDomesticSaleVolume(Integer domesticSaleVolume) {
        this.domesticSaleVolume = domesticSaleVolume;
    }

    public Integer getBranchCompanySaleVolume() {
        return branchCompanySaleVolume;
    }

    public void setBranchCompanySaleVolume(Integer branchCompanySaleVolume) {
        this.branchCompanySaleVolume = branchCompanySaleVolume;
    }

    public Integer getMainCompanySaleVolume() {
        return mainCompanySaleVolume;
    }

    public void setMainCompanySaleVolume(Integer mainCompanySaleVolume) {
        this.mainCompanySaleVolume = mainCompanySaleVolume;
    }

    public BigDecimal getTransverseShrinkRate() {
        return transverseShrinkRate;
    }

    public void setTransverseShrinkRate(BigDecimal transverseShrinkRate) {
        this.transverseShrinkRate = transverseShrinkRate;
    }

    public BigDecimal getDirectShrinkRate() {
        return directShrinkRate;
    }

    public void setDirectShrinkRate(BigDecimal directShrinkRate) {
        this.directShrinkRate = directShrinkRate;
    }

    public String getMeasureShrinkMode() {
        return measureShrinkMode;
    }

    public void setMeasureShrinkMode(String measureShrinkMode) {
        this.measureShrinkMode = measureShrinkMode;
    }

    public String getTestSpecification() {
        return testSpecification;
    }

    public void setTestSpecification(String testSpecification) {
        this.testSpecification = testSpecification;
    }

    public String getYarnCount() {
        return yarnCount;
    }

    public void setYarnCount(String yarnCount) {
        this.yarnCount = yarnCount;
    }

    public String getStorageArea() {
        return storageArea;
    }

    public void setStorageArea(String storageArea) {
        this.storageArea = storageArea;
    }

    public Boolean getIsNew() {
        return isNew;
    }

    public void setIsNew(Boolean isNew) {
        this.isNew = isNew;
    }

    /**
     * 新增面料供应商，足米价旧值为0
     */
    public void setWholePriceToZero() {
        if (!isNew) {
            return;
        }
        setWholePrice(BigDecimal.ZERO);
    }

    /**
     * 新增面料供应商，单价旧值为0
     */
    public void setUnitPriceToZero() {
        if (!isNew) {
            return;
        }
        setUnitPrice(BigDecimal.ZERO);
    }

    public BigDecimal getMaterialUnitPrice(String materialType) {
        BigDecimal price = BigDecimal.ZERO;
        if (Objects.isNull(this.getSupplierInfoId())) {
            return price;
        }
        if (MaterialTypeEnum.SIDE_FABRIC.name().equals(materialType)) {
            price = this.getUnitPrice();
        }
        return price;
    }

    public Integer getCreateByCompany() {
        return createByCompany;
    }

    public void setCreateByCompany(Integer createByCompany) {
        this.createByCompany = createByCompany;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getProductSaleVolumeLastYear() {
        return productSaleVolumeLastYear;
    }

    public void setProductSaleVolumeLastYear(Integer productSaleVolumeLastYear) {
        this.productSaleVolumeLastYear = productSaleVolumeLastYear;
    }

    public String getSellingPointCn() {
        return sellingPointCn;
    }

    public void setSellingPointCn(String sellingPointCn) {
        this.sellingPointCn = sellingPointCn;
    }

    public Integer getIsDevFriendly() {
        return isDevFriendly;
    }

    public void setIsDevFriendly(Integer isDevFriendly) {
        this.isDevFriendly = isDevFriendly;
    }

    /**
     * 同步公司面料信息，冗余到供应商面料信息中<br>
     * 克重、成分、毛幅宽、净幅宽
     * @param materialEntity 公司面料信息
     */
    public void syncMaterialInfo(MaterialEntity materialEntity) {
        this.setGrammage(materialEntity.getGrammage());
        this.setIngredientId(materialEntity.getIngredientId());
        this.setGrossBreadth(materialEntity.getGrossBreadth());
        this.setNetBreadth(materialEntity.getNetBreadth());
    }


    /**
     * 需要价格审核，不直接保存价格相关信息。审核通过之后再保存
     * @param isPriceReviewNeeded 是否需要审核
     */
    public void needReviewPrice(boolean isPriceReviewNeeded, MaterialSupplierInfoEntity existEntity) {
        if (isPriceReviewNeeded) {
            this.setWholePrice(existEntity.getWholePrice());
            this.setUnitPrice(existEntity.getUnitPrice());
        }
    }

    /**
     * 面料价格审核通过后，复制价格到当前对象中
     * @param task
     */
    public void copyOldPriceFromReviewTask(FlowTaskMaterialPriceReviewEntity task) {
        this.setWholePrice(task.getOldWholePrice());
        this.setUnitPrice(task.getOldUnitPrice());
    }
}
