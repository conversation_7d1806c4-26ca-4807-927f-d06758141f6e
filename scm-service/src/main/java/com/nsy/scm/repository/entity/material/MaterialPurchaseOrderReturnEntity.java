package com.nsy.scm.repository.entity.material;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <p>
 * 领料退货单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
@Entity
@Table(name = "material_purchase_order_return")
@TableName("material_purchase_order_return")
public class MaterialPurchaseOrderReturnEntity extends BaseMpEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer returnId;

    /**
     * 退货单号
     */
    private String returnNo;

    /**
     * 面料供应商id
     */
    private Integer materialSupplierId;

    /**
     * 发起退货成衣工厂id
     */
    private Integer sycSupplierId;

    /**
     * 发起退货时间
     */
    private Date activeDate;

    /**
     * 退货原因
     */
    private String returnReason;

    /**
     * 交货地址
     */
    private String receiveAddress;

    /**
     * 区域
     */
    private String location;


    public Integer getReturnId() {
        return returnId;
    }

    public void setReturnId(Integer returnId) {
        this.returnId = returnId;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public void setReturnNo(String returnNo) {
        this.returnNo = returnNo;
    }

    public Integer getMaterialSupplierId() {
        return materialSupplierId;
    }

    public void setMaterialSupplierId(Integer materialSupplierId) {
        this.materialSupplierId = materialSupplierId;
    }

    public Integer getSycSupplierId() {
        return sycSupplierId;
    }

    public void setSycSupplierId(Integer sycSupplierId) {
        this.sycSupplierId = sycSupplierId;
    }

    public Date getActiveDate() {
        return activeDate;
    }

    public void setActiveDate(Date activeDate) {
        this.activeDate = activeDate;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public String getReceiveAddress() {
        return receiveAddress;
    }

    public void setReceiveAddress(String receiveAddress) {
        this.receiveAddress = receiveAddress;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }


}
