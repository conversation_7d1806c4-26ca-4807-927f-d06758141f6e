package com.nsy.scm.repository.dao.material.sample;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.scm.repository.entity.material.sample.FlowTaskMaterialSampleEntity;

import java.util.List;

/**
 * 面料接单任务流程 DAO 实现类
 *
 * <AUTHOR>
 * @date 2023/10/28 18:03
 */
public interface FlowTaskMaterialSampleDao extends IService<FlowTaskMaterialSampleEntity> {

    /**
     * 来源任务查询打样任务集合
     *
     * @param sourceTaskId   来源任务id
     * @param sourceTaskType 来源任务类型
     * @param taskStatusList 任务状态
     * @return List<FlowTaskMaterialSampleEntity>
     */
    List<FlowTaskMaterialSampleEntity> listBySourceTaskId(Integer sourceTaskId, Integer sourceTaskType, List<Integer> taskStatusList);

    List<FlowTaskMaterialSampleEntity> listBySourceTaskIdList(List<Integer> sourceTaskIdList, Integer sourceTaskType, List<Integer> taskStatusList);
}
