package com.nsy.scm.repository.dao.supplier;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.api.scm.dto.request.supplier.SupplierAccountsReceivableRecordPageRequest;
import com.nsy.api.scm.dto.response.supplier.SupplierAccountsReceivableRecordPageResponse;
import com.nsy.api.scm.dto.response.supplier.SupplierAccountsReceivableRecordStatResponse;
import com.nsy.scm.repository.entity.supplier.SupplierAccountsReceivableRecordEntity;

/**
 * <h3>供应商应收账款登记表 持久层接口</h3>
 *
 * <AUTHOR>
 * @since 2024/10/23 15:31
 */
public interface SupplierAccountsReceivableRecordDao extends IService<SupplierAccountsReceivableRecordEntity> {

    IPage<SupplierAccountsReceivableRecordPageResponse> iPage(SupplierAccountsReceivableRecordPageRequest request);

    SupplierAccountsReceivableRecordStatResponse stat(SupplierAccountsReceivableRecordPageRequest request);
}
