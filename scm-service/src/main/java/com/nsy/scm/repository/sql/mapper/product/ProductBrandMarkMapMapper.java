package com.nsy.scm.repository.sql.mapper.product;

import com.nsy.scm.repository.entity.product.ProductBrandMarkMapEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 商品主唛映射表 Mapper实现类
 *
 * <AUTHOR>
 * @since  2024-03-14
 */
public interface ProductBrandMarkMapMapper extends BaseMapper<ProductBrandMarkMapEntity> {

    List<Integer> getIdsByProductAndBrandId(Integer productId, Integer brandId);

}




