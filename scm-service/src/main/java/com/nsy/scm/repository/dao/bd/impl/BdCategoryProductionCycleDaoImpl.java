package com.nsy.scm.repository.dao.bd.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.scm.constant.TrueOrFalseConstant;
import com.nsy.scm.repository.dao.bd.BdCategoryProductionCycleDao;
import com.nsy.scm.repository.entity.BdCategoryProductionCycleEntity;
import com.nsy.scm.repository.sql.mapper.BdCategoryProductionCycleMapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Repository
public class BdCategoryProductionCycleDaoImpl extends ServiceImpl<BdCategoryProductionCycleMapper, BdCategoryProductionCycleEntity> implements BdCategoryProductionCycleDao {


    @Override
    public List<BdCategoryProductionCycleEntity> listByCategoryProductionCycleIds(List<Integer> categoryProductionCycleIds) {
        if (CollectionUtils.isEmpty(categoryProductionCycleIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<BdCategoryProductionCycleEntity> queryWrapper = new LambdaQueryWrapper<BdCategoryProductionCycleEntity>()
                .eq(BdCategoryProductionCycleEntity::getIsDeleted, TrueOrFalseConstant.NOT_DELETED)
                .in(BdCategoryProductionCycleEntity::getCategoryProductionCycleId, categoryProductionCycleIds);

        return this.list(queryWrapper);
    }

    @Override
    public BdCategoryProductionCycleEntity getByScmCategoryId(Integer scmCategoryId) {
        LambdaQueryWrapper<BdCategoryProductionCycleEntity> queryWrapper = new LambdaQueryWrapper<BdCategoryProductionCycleEntity>()
                .eq(BdCategoryProductionCycleEntity::getIsDeleted, TrueOrFalseConstant.NOT_DELETED)
                .eq(BdCategoryProductionCycleEntity::getScmCategoryId, scmCategoryId);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<BdCategoryProductionCycleEntity> getByDeletedAndScmCategoryIdIn(Integer deleted, List<Integer> scmCategoryIds) {
        LambdaQueryWrapper<BdCategoryProductionCycleEntity> queryWrapper = new LambdaQueryWrapper<BdCategoryProductionCycleEntity>()
                .eq(BdCategoryProductionCycleEntity::getIsDeleted, deleted)
                .in(BdCategoryProductionCycleEntity::getScmCategoryId, scmCategoryIds);
        return this.list(queryWrapper);
    }

    @Override
    public Map<Integer, BdCategoryProductionCycleEntity> mapByScmCategoryIds(Set<Integer> scmCategoryIds, Integer isDeleted) {
        if (CollectionUtil.isEmpty(scmCategoryIds)) {
            return MapUtil.empty();
        }
        LambdaQueryWrapper<BdCategoryProductionCycleEntity> queryWrapper = new LambdaQueryWrapper<BdCategoryProductionCycleEntity>()
                .eq(ObjectUtil.isNotNull(isDeleted), BdCategoryProductionCycleEntity::getIsDeleted, isDeleted)
                .in(BdCategoryProductionCycleEntity::getScmCategoryId, scmCategoryIds);
        return this.list(queryWrapper).stream().collect(Collectors.toMap(BdCategoryProductionCycleEntity::getScmCategoryId, Function.identity(), (v1, v2) -> v2));
    }
}
