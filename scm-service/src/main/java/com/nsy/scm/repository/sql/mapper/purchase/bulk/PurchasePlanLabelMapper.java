package com.nsy.scm.repository.sql.mapper.purchase.bulk;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nsy.api.scm.dto.request.purchase.common.OrderNoAndSkcRequest;
import com.nsy.scm.business.response.purchase.plan.PurchasePlanLabelWithOrderNoAndSkcVo;
import com.nsy.scm.business.response.purchase.plan.PurchasePlanLabelWithOrderNoVo;
import com.nsy.scm.repository.entity.purchase.bulk.PurchasePlanLabelEntity;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 采购计划单标签 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface PurchasePlanLabelMapper extends BaseMapper<PurchasePlanLabelEntity> {
    List<PurchasePlanLabelWithOrderNoVo> listByPurchasePlanOrderNos(@Param("orderNos") List<String> orderNos);

    List<PurchasePlanLabelWithOrderNoAndSkcVo> listByPurchasePlanOrderNosAndSkcs(@Param("orderNoAndSkcRequestSet") List<OrderNoAndSkcRequest> orderNoAndSkcRequestSet);
}
