package com.nsy.scm.repository.entity.purchase;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 采购申请自动处理记录详情
 *
 * <AUTHOR>
 * @since 2022/12/29 10:17
 */
@Entity
@Table(name = "purchase_apply_auto_processing_job_item")
@TableName("purchase_apply_auto_processing_job_item")
public class PurchaseApplyAutoProcessingJobItemEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer jobItemId;

    private Integer jobId;

    private Integer applyId;

    private Integer applyItemId;

    private Integer applyQty;

    private Integer addPlanQty;

    private Integer productionQuantityMin;

    private Integer useCompanyStockQty;

    private Integer usePurchaseOrderQty;

    private Integer useSharedQty;

    private Integer isSuccess;

    private String errorMessage;

    private String location;

    public Integer getJobItemId() {
        return jobItemId;
    }

    public void setJobItemId(Integer jobItemId) {
        this.jobItemId = jobItemId;
    }

    public Integer getJobId() {
        return jobId;
    }

    public void setJobId(Integer jobId) {
        this.jobId = jobId;
    }

    public Integer getApplyId() {
        return applyId;
    }

    public void setApplyId(Integer applyId) {
        this.applyId = applyId;
    }

    public Integer getApplyItemId() {
        return applyItemId;
    }

    public void setApplyItemId(Integer applyItemId) {
        this.applyItemId = applyItemId;
    }

    public Integer getApplyQty() {
        return applyQty;
    }

    public void setApplyQty(Integer applyQty) {
        this.applyQty = applyQty;
    }

    public Integer getAddPlanQty() {
        return addPlanQty;
    }

    public void setAddPlanQty(Integer addPlanQty) {
        this.addPlanQty = addPlanQty;
    }

    public Integer getProductionQuantityMin() {
        return productionQuantityMin;
    }

    public void setProductionQuantityMin(Integer productionQuantityMin) {
        this.productionQuantityMin = productionQuantityMin;
    }

    public Integer getUseCompanyStockQty() {
        return useCompanyStockQty;
    }

    public void setUseCompanyStockQty(Integer useCompanyStockQty) {
        this.useCompanyStockQty = useCompanyStockQty;
    }

    public Integer getUsePurchaseOrderQty() {
        return usePurchaseOrderQty;
    }

    public void setUsePurchaseOrderQty(Integer usePurchaseOrderQty) {
        this.usePurchaseOrderQty = usePurchaseOrderQty;
    }

    public Integer getUseSharedQty() {
        return useSharedQty;
    }

    public void setUseSharedQty(Integer useSharedQty) {
        this.useSharedQty = useSharedQty;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getIsSuccess() {
        return isSuccess;
    }

    public void setIsSuccess(Integer isSuccess) {
        this.isSuccess = isSuccess;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        if (StringUtils.isNotBlank(errorMessage) && errorMessage.length() > 200) {
            this.errorMessage = errorMessage.substring(0, 200);
        } else {
            this.errorMessage = errorMessage;
        }
    }
}
