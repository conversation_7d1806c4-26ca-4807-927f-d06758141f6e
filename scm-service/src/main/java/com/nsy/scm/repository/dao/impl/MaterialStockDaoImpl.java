package com.nsy.scm.repository.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.scm.dto.request.material.PageQueryMaterialPurchaseRequest;
import com.nsy.api.scm.dto.response.material.TotalMaterialReserveCountRes;
import com.nsy.scm.repository.dao.MaterialStockDao;
import com.nsy.scm.repository.entity.material.MaterialStockEntity;
import com.nsy.scm.repository.sql.mapper.material.MaterialStockMapper;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 物料库存dao实现类
 *
 * <AUTHOR>
 * @since 2022-04-06 13:50
 */
@Service
public class MaterialStockDaoImpl extends ServiceImpl<MaterialStockMapper, MaterialStockEntity> implements MaterialStockDao {

    @Override
    public IPage<MaterialStockEntity> pageQuery(PageQueryMaterialPurchaseRequest request) {
        return baseMapper.pageQuery(new Page<>(request.getPageIndex(), request.getPageSize()), request);
    }

    @Override
    public List<MaterialStockEntity> getByMaterialIdList(List<Integer> materialIds) {
        LambdaQueryWrapper<MaterialStockEntity> queryWrapper = new LambdaQueryWrapper<MaterialStockEntity>()
                .in(MaterialStockEntity::getMaterialId, materialIds);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public Integer selectTotalMaterialCount() {
        return baseMapper.selectTotalMaterialCount();
    }

    @Override
    public TotalMaterialReserveCountRes getTotalMaterialReserveCount(Set<Integer> materialIds) {
        TotalMaterialReserveCountRes res = baseMapper.selectTotalMaterialReserveCount(materialIds);
        return res == null ? new TotalMaterialReserveCountRes() : res;
    }

    @Override
    public List<MaterialStockEntity> queryByMaterialIds(Set<Integer> materialIds) {
        if (CollectionUtils.isEmpty(materialIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<MaterialStockEntity> queryWrapper = new LambdaQueryWrapper<MaterialStockEntity>()
                .in(MaterialStockEntity::getMaterialId, materialIds);
        return this.baseMapper.selectList(queryWrapper);
    }
}
