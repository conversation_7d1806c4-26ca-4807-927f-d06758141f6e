package com.nsy.scm.repository.dao.material.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.scm.dto.domain.bd.AttachmentDto;
import com.nsy.scm.repository.dao.material.MaterialTypeAttachDao;
import com.nsy.scm.repository.entity.MaterialTypeAttachEntity;
import com.nsy.scm.repository.sql.mapper.MaterialTypeAttachMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class MaterialTypeAttachDaoImpl extends ServiceImpl<MaterialTypeAttachMapper, MaterialTypeAttachEntity> implements MaterialTypeAttachDao {


    @Override
    public List<AttachmentDto> getByMaterialTypeId(Integer materialTypeId) {
        List<MaterialTypeAttachEntity> materialTypeAttachEntities = this.list(new LambdaQueryWrapper<MaterialTypeAttachEntity>()
                .eq(MaterialTypeAttachEntity::getMaterialTypeId, materialTypeId));
        return materialTypeAttachEntities.stream().map(m -> {
            AttachmentDto attach = new AttachmentDto();
            attach.setOriginName(m.getOriginName());
            attach.setAttachmentName(m.getAttachmentName());
            attach.setAttachmentUrl(m.getAttachmentUrl());
            attach.setAttachmentId(m.getMaterialTypeAttachId());
            return attach;
        }).collect(Collectors.toList());

    }

    @Override
    public List<MaterialTypeAttachEntity> getByMaterialTypeIds(List<Integer> materialTypeIds) {
        if (CollectionUtils.isEmpty(materialTypeIds)) {
            return Collections.emptyList();
        }
        return this.list(new LambdaQueryWrapper<MaterialTypeAttachEntity>()
                .in(MaterialTypeAttachEntity::getMaterialTypeId, materialTypeIds));
    }


}
