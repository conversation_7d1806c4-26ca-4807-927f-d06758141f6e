package com.nsy.scm.repository.dao.material.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.scm.repository.dao.material.MaterialSupplierMeasureShrinkageDao;
import com.nsy.scm.repository.entity.material.MaterialSupplierMeasureShrinkageEntity;
import com.nsy.scm.repository.sql.mapper.material.MaterialSupplierMeasureShrinkageMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <h3>供应商物料测缩信息 DAO实现类</h3>
 *
 * <AUTHOR> Chao
 * @date 2023/11/17 10:21
 */
@Repository
public class MaterialSupplierMeasureShrinkageDaoImpl extends ServiceImpl<MaterialSupplierMeasureShrinkageMapper, MaterialSupplierMeasureShrinkageEntity> implements MaterialSupplierMeasureShrinkageDao {

    @Override
    public Map<Integer, MaterialSupplierMeasureShrinkageEntity> getMeasureShrinkModeMap(Collection<Integer> materialSupplierInfoIds) {
        if (CollectionUtils.isEmpty(materialSupplierInfoIds)) {
            return Collections.emptyMap();
        }
        return list(new LambdaQueryWrapper<MaterialSupplierMeasureShrinkageEntity>()
                .in(MaterialSupplierMeasureShrinkageEntity::getMaterialSupplierInfoId, materialSupplierInfoIds))
                .stream().collect(Collectors.toMap(MaterialSupplierMeasureShrinkageEntity::getMeasureShrinkageId, Function.identity(), (k1, k2) -> k1));
    }
}
