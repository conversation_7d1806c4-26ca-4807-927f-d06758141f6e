package com.nsy.scm.repository.entity.bd;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 商品阶梯价规则配置明细表
 * <AUTHOR>
 * @TableName bd_stage_price_rule_item
 */
@Entity
@Table(name = "bd_stage_price_rule_item")
@TableName("bd_stage_price_rule_item")
public class BdStagePriceRuleItemEntity extends BaseMpEntity {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer bdStagePriceRuleItemId;

    /**
     * 阶梯价规则Id
     */
    private Integer bdStagePriceRuleId;

    /**
     * 采购下单总量起
     */
    private Integer totalPurchaseOrderQtyStart;

    /**
     * 采购下单总量止
     */
    private Integer totalPurchaseOrderQtyEnd;

    /**
     * 倍率
     */
    private BigDecimal rate;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer isDeleted;

    /**
     * 区域
     */
    private String location;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public Integer getBdStagePriceRuleItemId() {
        return bdStagePriceRuleItemId;
    }

    /**
     * 主键
     */
    public void setBdStagePriceRuleItemId(Integer bdStagePriceRuleItemId) {
        this.bdStagePriceRuleItemId = bdStagePriceRuleItemId;
    }

    /**
     * 阶梯价规则Id
     */
    public Integer getBdStagePriceRuleId() {
        return bdStagePriceRuleId;
    }

    /**
     * 阶梯价规则Id
     */
    public void setBdStagePriceRuleId(Integer bdStagePriceRuleId) {
        this.bdStagePriceRuleId = bdStagePriceRuleId;
    }

    /**
     * 采购下单总量起
     */
    public Integer getTotalPurchaseOrderQtyStart() {
        return totalPurchaseOrderQtyStart;
    }

    /**
     * 采购下单总量起
     */
    public void setTotalPurchaseOrderQtyStart(Integer totalPurchaseOrderQtyStart) {
        this.totalPurchaseOrderQtyStart = totalPurchaseOrderQtyStart;
    }

    /**
     * 采购下单总量止
     */
    public Integer getTotalPurchaseOrderQtyEnd() {
        return totalPurchaseOrderQtyEnd;
    }

    /**
     * 采购下单总量止
     */
    public void setTotalPurchaseOrderQtyEnd(Integer totalPurchaseOrderQtyEnd) {
        this.totalPurchaseOrderQtyEnd = totalPurchaseOrderQtyEnd;
    }

    /**
     * 倍率
     */
    public BigDecimal getRate() {
        return rate;
    }

    /**
     * 倍率
     */
    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    /**
     * 是否删除：0-未删除，1-已删除
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * 是否删除：0-未删除，1-已删除
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * 区域
     */
    public String getLocation() {
        return location;
    }

    /**
     * 区域
     */
    public void setLocation(String location) {
        this.location = location;
    }

}
