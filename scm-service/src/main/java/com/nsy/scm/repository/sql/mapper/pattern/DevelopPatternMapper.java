package com.nsy.scm.repository.sql.mapper.pattern;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.scm.dto.domain.pattern.DevelopPatternPageDTO;
import com.nsy.api.scm.dto.request.pattern.DevelopPatternPageRequest;
import com.nsy.permission.annatation.Permission;
import com.nsy.scm.repository.entity.pattern.DevelopPatternEntity;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 开款版型库 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
public interface DevelopPatternMapper extends BaseMapper<DevelopPatternEntity> {

    @Permission
    IPage<DevelopPatternPageDTO> iPage(IPage<DevelopPatternPageRequest> page, @Param("query") DevelopPatternPageRequest request);

    String getLastOneMaxDevelopPatternNo();
}
