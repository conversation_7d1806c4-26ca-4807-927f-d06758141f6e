package com.nsy.scm.repository.dao.clothespart;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.api.scm.dto.request.clothespart.QueryMeasureMethodSizeDiffConfigRequest;
import com.nsy.scm.repository.entity.clothespart.BdClothesPartMeasureMethodSizeDiffConfigEntity;

import java.util.List;
import java.util.Map;

/**
 * 服装部位模板信息表DAO
 *
 * <AUTHOR>
 * @since 2024/7/21 15:33
 */
public interface BdClothesPartMeasureMethodSizeDiffConfigDao extends IService<BdClothesPartMeasureMethodSizeDiffConfigEntity> {

    List<BdClothesPartMeasureMethodSizeDiffConfigEntity> listByPartId(Integer partId);

    List<BdClothesPartMeasureMethodSizeDiffConfigEntity> listByCondition(QueryMeasureMethodSizeDiffConfigRequest request);

    Map<Integer, List<BdClothesPartMeasureMethodSizeDiffConfigEntity>> mapByPartIdList(List<Integer> partIdList);
}
