package com.nsy.scm.repository.entity.pattern;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.google.common.collect.Sets;
import com.nsy.api.scm.dto.domain.pattern.DevelopPatternSizeItemDTO;
import com.nsy.scm.repository.entity.base.BaseMpEntity;
import org.apache.commons.collections4.CollectionUtils;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 开款版型尺码信息表（旧数据）
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@Entity
@Deprecated
@Table(name = "develop_pattern_size_info")
@TableName("develop_pattern_size_info")
public class DevelopPatternSizeInfoEntity extends BaseMpEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer developPatternSizeInfoId;

    /**
     * 开款版型库ID
     */
    private Integer developPatternId;

    /**
     * 套装部位
     */
    private String suitPartName;

    /**
     * 尺码，数据字典
     */
    private String size;

    /**
     * ITEM
     */
    private String itemName;

    /**
     * 选项，JSON
     */
    private String value;

    public Integer getDevelopPatternSizeInfoId() {
        return developPatternSizeInfoId;
    }

    public void setDevelopPatternSizeInfoId(Integer developPatternSizeInfoId) {
        this.developPatternSizeInfoId = developPatternSizeInfoId;
    }

    public Integer getDevelopPatternId() {
        return developPatternId;
    }

    public void setDevelopPatternId(Integer developPatternId) {
        this.developPatternId = developPatternId;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getSuitPartName() {
        return suitPartName;
    }

    public void setSuitPartName(String suitPartName) {
        this.suitPartName = suitPartName;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static DevelopPatternSizeItemDTO buildItemDTO(DevelopPatternSizeInfoEntity entity) {
        if (Objects.isNull(entity)) {
            return new DevelopPatternSizeItemDTO();
        }
        DevelopPatternSizeItemDTO dto = new DevelopPatternSizeItemDTO();
        dto.setSize(entity.getSize());
        dto.setDevelopPatternId(entity.getDevelopPatternId());
        return dto;
    }

    public static List<DevelopPatternSizeItemDTO> buildItemDTOList(List<DevelopPatternSizeInfoEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        Set<String> sizeSet = Sets.newHashSet();
        return entityList.stream().filter(dto -> sizeSet.add(dto.getSize())).map(DevelopPatternSizeInfoEntity::buildItemDTO).collect(Collectors.toList());
    }

}
