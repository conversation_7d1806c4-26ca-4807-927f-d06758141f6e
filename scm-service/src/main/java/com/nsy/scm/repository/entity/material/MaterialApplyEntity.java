package com.nsy.scm.repository.entity.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <p>
 * 面料申请单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Entity
@Table(name = "material_apply")
@TableName("material_apply")
public class MaterialApplyEntity extends BaseMpEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer applyId;

    /**
     * 申请编号
     */
    private String applyNo;

    /**
     * 申请时间
     */
    private Date applyDate;

    /**
     * 申请员工编码
     */
    private String applyEmpCode;

    private Integer applyEmpId;

    /**
     * 申请员工名称
     */
    private String applyEmpName;

    /**
     * 申请部门编码
     */
    private String applyDeptCode;

    private Integer applyDeptId;
    /**
     * 申请部门名称
     */
    private String applyDeptName;

    /**
     * 商品编码
     */
    private String productSku;

    /**
     * 开发进度字典值
     */
    private String developPhase;

    /**
     * 进度反馈字典值
     */
    private String scheduleFeedback;

    /**
     * 面料供应商ID(反馈时选择的面料供应商)
     */
    private Integer materialSupplierId;

    /**
     * 物料供应商信息ID(反馈时选择的面料供应商)
     */
    private Integer feedbackMaterialSupplierInfoId;

    /**
     * 供应商面料名称（反馈类型为：新开发面料）
     */
    private String feedbackSupplierMaterialName;

    /**
     * 供应商名称（反馈类型为：新开发面料）
     */
    private String feedbackSupplierName;

    /**
     * 是否选用(确认反馈)0是；1否
     */
    private Integer confirmChoose;
    /**
     * 确认反馈时间
     */
    private Date confirmDate;

    /**
     * 是否重找(确认反馈)0是；1否
     */
    private Integer confirmFindAgain;

    /**
     * 是否重找记录（旧记录ID）
     */
    private Integer confirmFindAgainApplyId;

    // 不重找原因
    private String confirmNotFindReason;

    /**
     * 不选用原因(确认反馈)
     */
    private String confirmReasons;

    /**
     * 备注说明(确认反馈)
     */
    private String confirmDescr;

    /**
     * 取消说明(取消申请)
     */
    private String cancelDescr;

    /**
     * 需求说明
     */
    private String requirementDesc;

    /**
     * 参考链接
     */
    private String referenceLinking;

    /**
     * 备注
     */
    private String feedbackRemark;

    /**
     * 申请备注
     */
    private String applyRemark;

    /**
     * 准入备注
     */
    private String admissionRemark;

    /**
     * 面料开发员工
     */
    private Integer developEmpId;
    private String developEmpName;
    private String developEmpCode;


    /**
     * 面料开发员工（新增时的款式开发员）
     */
    private Integer styleDevelopEmpId;
    private String styleDevelopEmpName;

    // 是否更换开发员（新纪录是否更换了）：0是，1否
    private Integer isChangeDeveloper;

    /**
     * 是否被删除：0-未删除，1-删除
     */
    private Integer isDelete;

    /**
     * 提交员工编码
     */
    private String createEmpCode;

    private Integer createEmpId;

    /**
     * 提交员工部门编码
     */
    private String createDeptCode;

    private Integer createDeptId;

    /**
     * 反馈日期
     */
    private Date feedbackDate;
    /**
     * 处理超期天数
     */
    private Integer handleOverdueTime;
    /**
     * 确认反馈超期时数（小时）
     */
    private Integer confirmOverdueDay;

    // 完成日期（状态为已完成时赋值）
    private Date finishedDate;

    public Date getFeedbackDate() {
        return feedbackDate;
    }

    public void setFeedbackDate(Date feedbackDate) {
        this.feedbackDate = feedbackDate;
    }

    public Integer getHandleOverdueTime() {
        return handleOverdueTime;
    }

    public void setHandleOverdueTime(Integer handleOverdueTime) {
        this.handleOverdueTime = handleOverdueTime;
    }

    public Integer getConfirmOverdueDay() {
        return confirmOverdueDay;
    }

    public void setConfirmOverdueDay(Integer confirmOverdueDay) {
        this.confirmOverdueDay = confirmOverdueDay;
    }

    public Integer getApplyId() {
        return applyId;
    }

    public void setApplyId(Integer applyId) {
        this.applyId = applyId;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public String getApplyEmpName() {
        return applyEmpName;
    }

    public void setApplyEmpName(String applyEmpName) {
        this.applyEmpName = applyEmpName;
    }

    public String getApplyDeptCode() {
        return applyDeptCode;
    }

    public void setApplyDeptCode(String applyDeptCode) {
        this.applyDeptCode = applyDeptCode;
    }

    public String getApplyDeptName() {
        return applyDeptName;
    }

    public void setApplyDeptName(String applyDeptName) {
        this.applyDeptName = applyDeptName;
    }

    public String getProductSku() {
        return productSku;
    }

    public void setProductSku(String productSku) {
        this.productSku = productSku;
    }

    public String getDevelopPhase() {
        return developPhase;
    }

    public void setDevelopPhase(String developPhase) {
        this.developPhase = developPhase;
    }

    public String getScheduleFeedback() {
        return scheduleFeedback;
    }

    public void setScheduleFeedback(String scheduleFeedback) {
        this.scheduleFeedback = scheduleFeedback;
    }

    public String getRequirementDesc() {
        return requirementDesc;
    }

    public void setRequirementDesc(String requirementDesc) {
        this.requirementDesc = requirementDesc;
    }

    public String getReferenceLinking() {
        return referenceLinking;
    }

    public void setReferenceLinking(String referenceLinking) {
        this.referenceLinking = referenceLinking;
    }

    public String getFeedbackRemark() {
        return feedbackRemark;
    }

    public void setFeedbackRemark(String feedbackRemark) {
        this.feedbackRemark = feedbackRemark;
    }

    public String getDevelopEmpCode() {
        return developEmpCode;
    }

    public void setDevelopEmpCode(String developEmpCode) {
        this.developEmpCode = developEmpCode;
    }

    public String getDevelopEmpName() {
        return developEmpName;
    }

    public void setDevelopEmpName(String developEmpName) {
        this.developEmpName = developEmpName;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getCreateEmpCode() {
        return createEmpCode;
    }

    public void setCreateEmpCode(String createEmpCode) {
        this.createEmpCode = createEmpCode;
    }

    public String getCreateDeptCode() {
        return createDeptCode;
    }

    public void setCreateDeptCode(String createDeptCode) {
        this.createDeptCode = createDeptCode;
    }

    public String getApplyEmpCode() {
        return applyEmpCode;
    }

    public void setApplyEmpCode(String applyEmpCode) {
        this.applyEmpCode = applyEmpCode;
    }

    public Integer getMaterialSupplierId() {
        return materialSupplierId;
    }

    public void setMaterialSupplierId(Integer materialSupplierId) {
        this.materialSupplierId = materialSupplierId;
    }

    public Integer getIsChangeDeveloper() {
        return isChangeDeveloper;
    }

    public void setIsChangeDeveloper(Integer isChangeDeveloper) {
        this.isChangeDeveloper = isChangeDeveloper;
    }

    public String getFeedbackSupplierName() {
        return feedbackSupplierName;
    }

    public void setFeedbackSupplierName(String feedbackSupplierName) {
        this.feedbackSupplierName = feedbackSupplierName;
    }

    public Integer getConfirmChoose() {
        return confirmChoose;
    }

    public void setConfirmChoose(Integer confirmChoose) {
        this.confirmChoose = confirmChoose;
    }

    public Integer getConfirmFindAgain() {
        return confirmFindAgain;
    }

    public void setConfirmFindAgain(Integer confirmFindAgain) {
        this.confirmFindAgain = confirmFindAgain;
    }

    public String getConfirmReasons() {
        return confirmReasons;
    }

    public void setConfirmReasons(String confirmReasons) {
        this.confirmReasons = confirmReasons;
    }

    public String getApplyRemark() {
        return applyRemark;
    }

    public void setApplyRemark(String applyRemark) {
        this.applyRemark = applyRemark;
    }

    public String getConfirmDescr() {
        return confirmDescr;
    }

    public void setConfirmDescr(String confirmDescr) {
        this.confirmDescr = confirmDescr;
    }

    public String getCancelDescr() {
        return cancelDescr;
    }

    public void setCancelDescr(String cancelDescr) {
        this.cancelDescr = cancelDescr;
    }

    public String getAdmissionRemark() {
        return admissionRemark;
    }

    public Integer getApplyEmpId() {
        return applyEmpId;
    }

    public void setApplyEmpId(Integer applyEmpId) {
        this.applyEmpId = applyEmpId;
    }

    public Integer getApplyDeptId() {
        return applyDeptId;
    }

    public void setApplyDeptId(Integer applyDeptId) {
        this.applyDeptId = applyDeptId;
    }

    public Integer getDevelopEmpId() {
        return developEmpId;
    }

    public void setDevelopEmpId(Integer developEmpId) {
        this.developEmpId = developEmpId;
    }

    public Integer getCreateEmpId() {
        return createEmpId;
    }

    public void setCreateEmpId(Integer createEmpId) {
        this.createEmpId = createEmpId;
    }

    public Integer getCreateDeptId() {
        return createDeptId;
    }

    public void setCreateDeptId(Integer createDeptId) {
        this.createDeptId = createDeptId;
    }

    public void setAdmissionRemark(String admissionRemark) {
        this.admissionRemark = admissionRemark;
    }

    public Date getConfirmDate() {
        return confirmDate;
    }

    public void setConfirmDate(Date confirmDate) {
        this.confirmDate = confirmDate;
    }

    public Integer getConfirmFindAgainApplyId() {
        return confirmFindAgainApplyId;
    }

    public void setConfirmFindAgainApplyId(Integer confirmFindAgainApplyId) {
        this.confirmFindAgainApplyId = confirmFindAgainApplyId;
    }

    public Date getFinishedDate() {
        return finishedDate;
    }

    public void setFinishedDate(Date finishedDate) {
        this.finishedDate = finishedDate;
    }

    public String getConfirmNotFindReason() {
        return confirmNotFindReason;
    }

    public void setConfirmNotFindReason(String confirmNotFindReason) {
        this.confirmNotFindReason = confirmNotFindReason;
    }

    public Integer getFeedbackMaterialSupplierInfoId() {
        return feedbackMaterialSupplierInfoId;
    }

    public void setFeedbackMaterialSupplierInfoId(Integer feedbackMaterialSupplierInfoId) {
        this.feedbackMaterialSupplierInfoId = feedbackMaterialSupplierInfoId;
    }

    public String getFeedbackSupplierMaterialName() {
        return feedbackSupplierMaterialName;
    }

    public void setFeedbackSupplierMaterialName(String feedbackSupplierMaterialName) {
        this.feedbackSupplierMaterialName = feedbackSupplierMaterialName;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Integer getStyleDevelopEmpId() {
        return styleDevelopEmpId;
    }

    public void setStyleDevelopEmpId(Integer styleDevelopEmpId) {
        this.styleDevelopEmpId = styleDevelopEmpId;
    }

    public String getStyleDevelopEmpName() {
        return styleDevelopEmpName;
    }

    public void setStyleDevelopEmpName(String styleDevelopEmpName) {
        this.styleDevelopEmpName = styleDevelopEmpName;
    }
}
