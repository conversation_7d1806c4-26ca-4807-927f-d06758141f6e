package com.nsy.scm.repository.entity.purchase;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * skc首单信息表
 *
 * @TableName purchase_skc_first_order
 */
@Entity
@Table(name = "purchase_skc_first_order")
@TableName("purchase_skc_first_order")
public class PurchaseSkcFirstOrderEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    /**
     * 主键
     */
    private Integer skcFirstOrderId;
    /**
     * 采购单id
     */
    private Integer orderId = 0;

    /**
     * 采购单号
     */
    private String orderNo = "";
    /**
     * 商品编码
     */
    private String spu;
    /**
     * 商品Id
     */
    private Integer productId;
    /**
     * 颜色编码
     */
    private String skc;
    /**
     * 0:未下单(首单),1:已返单
     */
    private Integer status = 0;
    /**
     * 首单时间
     */
    private Date orderDate;
    /**
     * 区域
     */
    private String location;

    /**
     * 主键
     */
    public Integer getSkcFirstOrderId() {
        return skcFirstOrderId;
    }

    /**
     * 主键
     */
    public void setSkcFirstOrderId(Integer skcFirstOrderId) {
        this.skcFirstOrderId = skcFirstOrderId;
    }

    /**
     * 采购单id
     */
    public Integer getOrderId() {
        return orderId;
    }

    /**
     * 采购单id
     */
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    /**
     * 颜色编码
     */
    public String getSkc() {
        return skc;
    }

    /**
     * 颜色编码
     */
    public void setSkc(String skc) {
        this.skc = skc;
    }

    /**
     * 0:未下单(首单),1:已返单
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 0:未下单(首单),1:已返单
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 首单时间
     */
    public Date getOrderDate() {
        return orderDate;
    }

    /**
     * 首单时间
     */
    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }
}
