package com.nsy.scm.repository.entity.clothespart;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 部位量法档差配置
 * @since 2024/7/11
 */

@Entity
@Table(name = "bd_clothes_part_measure_method_size_diff_config")
@TableName("bd_clothes_part_measure_method_size_diff_config")
public class BdClothesPartMeasureMethodSizeDiffConfigEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer sizeDiffConfigId;

    private Integer partId;

    private Integer sizeTemplateId;

    private String productLine;

    private String sizeType;

    private BigDecimal publicDiff;

    private Integer isDeleted;

    private String location;

    public Integer getSizeDiffConfigId() {
        return sizeDiffConfigId;
    }

    public void setSizeDiffConfigId(Integer sizeDiffConfigId) {
        this.sizeDiffConfigId = sizeDiffConfigId;
    }

    public Integer getPartId() {
        return partId;
    }

    public void setPartId(Integer partId) {
        this.partId = partId;
    }

    public String getProductLine() {
        return productLine;
    }

    public void setProductLine(String productLine) {
        this.productLine = productLine;
    }

    public String getSizeType() {
        return sizeType;
    }

    public void setSizeType(String sizeType) {
        this.sizeType = sizeType;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public BigDecimal getPublicDiff() {
        return publicDiff;
    }

    public void setPublicDiff(BigDecimal publicDiff) {
        this.publicDiff = publicDiff;
    }

    public Integer getSizeTemplateId() {
        return sizeTemplateId;
    }

    public void setSizeTemplateId(Integer sizeTemplateId) {
        this.sizeTemplateId = sizeTemplateId;
    }

    public BdClothesPartMeasureMethodSizeDiffConfigEntity(Integer partId, Integer sizeTemplateId, String productLine, String sizeType, BigDecimal publicDiff, String location) {
        this.partId = partId;
        this.sizeTemplateId = sizeTemplateId;
        this.productLine = productLine;
        this.sizeType = sizeType;
        this.publicDiff = publicDiff;
        this.location = location;
    }

    public BdClothesPartMeasureMethodSizeDiffConfigEntity() {
    }
}
