package com.nsy.scm.repository.entity.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <p>
 * 物料发货单附件
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-22
 */
@Entity
@Table(name = "material_supplier_deliver_order_attach")
@TableName("material_supplier_deliver_order_attach")
public class MaterialSupplierDeliverOrderAttachEntity extends BaseMpEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer attachId;

    /**
     * 物料发货单id
     */
    private Integer deliverId;

    /**
     * 附件类型：枚举：MaterialSupplierDeliverOrderAttachTypeEnum
     */
    private String attachmentType;

    /**
     * 原始名称
     */
    private String originName;

    /**
     * oss文件名称
     */
    private String attachmentName;

    /**
     * 附件url
     */
    private String attachmentUrl;

    /**
     * 是否被删除：0-未删除，1-删除
     */
    private int isDelete;

    public Integer getAttachId() {
        return attachId;
    }

    public void setAttachId(Integer attachId) {
        this.attachId = attachId;
    }

    public Integer getDeliverId() {
        return deliverId;
    }

    public void setDeliverId(Integer deliverId) {
        this.deliverId = deliverId;
    }

    public String getOriginName() {
        return originName;
    }

    public void setOriginName(String originName) {
        this.originName = originName;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getAttachmentType() {
        return attachmentType;
    }

    public void setAttachmentType(String attachmentType) {
        this.attachmentType = attachmentType;
    }
}
