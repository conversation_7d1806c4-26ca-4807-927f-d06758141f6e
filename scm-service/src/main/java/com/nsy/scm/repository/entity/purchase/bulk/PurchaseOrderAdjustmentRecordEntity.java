package com.nsy.scm.repository.entity.purchase.bulk;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.api.scm.dto.domain.material.MaterialPurchaseOrderItemListByPurchaseOrderItemForSupplierDTO;
import com.nsy.api.scm.dto.domain.purchase.bulk.PurchaseOrderAdjustmentRecordAddDto;
import com.nsy.api.scm.dto.domain.purchase.bulk.PurchaseOrderAdjustmentRecordAuditDto;
import com.nsy.api.scm.dto.request.purchase.bulk.PurchaseOrderAdjustmentRecordAddRequest;
import com.nsy.api.scm.dto.request.purchase.bulk.PurchaseOrderAdjustmentRecordAuditRequest;
import com.nsy.scm.constant.MaterialAuditResponsiblePartyEnum;
import com.nsy.scm.constant.NumberConstant;
import com.nsy.scm.constant.bulk.PurchaseOrderAdjustmentRecordCancelSourceEnum;
import com.nsy.scm.constant.bulk.PurchaseOrderAdjustmentRecordOperationTypeEnum;
import com.nsy.scm.constant.bulk.PurchaseOrderAdjustmentRecordStatusEnum;
import com.nsy.scm.constant.bulk.PurchaseOrderAdjustmentRecordTypeEnum;
import com.nsy.scm.enums.bd.LocationEnum;
import com.nsy.scm.repository.entity.base.BaseMpEntity;
import com.nsy.scm.utils.DateTimeUtils;
import org.apache.logging.log4j.util.Strings;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.nsy.scm.constant.bulk.PurchaseOrderAdjustmentRecordOperationTypeEnum.DECREMENT_PASS;
import static com.nsy.scm.constant.bulk.PurchaseOrderAdjustmentRecordOperationTypeEnum.PASS;
import static com.nsy.scm.constant.bulk.PurchaseOrderAdjustmentRecordTypeEnum.DIRECT_DELIVERY_DATE_APPLY;
import static com.nsy.scm.constant.bulk.PurchaseOrderAdjustmentRecordTypeEnum.FACTORY_DELIVER_DELAYED_APPLY;
import static com.nsy.scm.constant.bulk.PurchaseOrderAdjustmentRecordTypeEnum.PURCHASE_CHANGE_DELIVERY_DATE_APPLY;
import static com.nsy.scm.constant.bulk.PurchaseOrderAdjustmentRecordTypeEnum.SECOND_DELIVERY_DATE_APPLY;

/**
 * <p>
 * 采购订单调整记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Entity
@Table(name = "purchase_order_adjustment_record")
@TableName("purchase_order_adjustment_record")
public class PurchaseOrderAdjustmentRecordEntity extends BaseMpEntity {


    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer orderAdjustmentRecordId;

    /**
     * 采购订单Id
     */
    private Integer orderId;

    /**
     * 采购订单明细id
     */
    private Integer orderItemId;

    /**
     * 类型:1-工厂延期申请，2-采购延期申请，3-取消数量申请，4-工厂拒单申请，5-二次货期申请，6-直发货期调整
     */
    private Integer adjustmentType;

    /**
     * 上个状态
     */
    private String previousStatus;

    /**
     * 状态: 1-待审核，2-已过审，3-已驳回
     */
    private Integer status;

    /**
     * 商品Id
     */
    private Integer productId;

    /**
     * spu
     */
    private String spu;

    /**
     * 规格Id
     */
    private Integer specId;

    /**
     * skc
     */
    private String skc;

    /**
     * sku
     */
    private String sku;

    /**
     * 预计到货日期
     */
    private Date expectDeliveryDate;

    /**
     * 申请时候的延期时间
     */
    private Date delayTime;

    /**
     * 申请延期天数
     */
    private Integer delayDay;

    /**
     * 审核时候的延期时间
     */
    private Date auditDelayTime;

    /**
     * 申请取消数
     */
    private Integer applyCancelQty;

    /**
     * 取消数
     */
    private Integer cancelQty;

    /**
     * 处理结果
     */
    private String handleResults;

    /**
     * 申请原因
     */
    private String applyReason;

    /**
     * 区域
     */
    private String location;

    /**
     * 工厂 - 备注/简述
     */
    private String remark;

    /**
     * 采购审核 - 备注/简述
     */
    private String auditRemark;

    /**
     * 责任方 新时颖 SHIYING 工厂 SUPPLIER
     */
    private String responsibleParty;
    /**
     * 是否待发货
     */
    private Integer isWaitReceiveOrder;
    /**
     * 申请数
     */
    private Integer applyQty;

    /**
     * 接单承诺货期
     */
    private Date receiveOrderExpectDeliveryDate;


    /**
     * 审核时间
     */
    private Date auditTime;
    /**
     * 采购过审 - 因公司延期天数
     */
    private Integer auditDelayDay;
    /**
     * 面料审核 - 判定的责任方 新时颖 SHIYING 工厂 SUPPLIER
     */
    private String materialAuditResponsibleParty;
    /**
     * 面料审核 - 面料延期天数
     */
    private Integer materialAuditDelayDay;
    /**
     * 面料审核 - 人
     */
    private String materialAuditBy;
    /**
     * 面料审核时间
     */
    private Date materialAuditTime;
    /**
     * 面料审核 - 备注
     */
    private String materialAuditRemark;
    /**
     * 审核 - 人
     */
    private String auditBy;

    /**
     * 取消来源，0-普通，1-排单，2-返工
     */
    private Integer cancelSource;

    /**
     * 来源单号，排单明细Id或退货单明细Id
     */
    private String sourceOrderNo;

    /**
     * 是否未更新货期，中间变量
     */
    @TableField(exist = false)
    private Integer unUpdateDeliveryTime = 0;

    public Date getReceiveOrderExpectDeliveryDate() {
        return receiveOrderExpectDeliveryDate;
    }

    public void setReceiveOrderExpectDeliveryDate(Date receiveOrderExpectDeliveryDate) {
        this.receiveOrderExpectDeliveryDate = receiveOrderExpectDeliveryDate;
    }

    public Integer getOrderAdjustmentRecordId() {
        return orderAdjustmentRecordId;
    }

    public void setOrderAdjustmentRecordId(Integer orderAdjustmentRecordId) {
        this.orderAdjustmentRecordId = orderAdjustmentRecordId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Integer getAdjustmentType() {
        return adjustmentType;
    }

    public void setAdjustmentType(Integer adjustmentType) {
        this.adjustmentType = adjustmentType;
    }

    public String getPreviousStatus() {
        return previousStatus;
    }

    public void setPreviousStatus(String previousStatus) {
        this.previousStatus = previousStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Date getExpectDeliveryDate() {
        return expectDeliveryDate;
    }

    public void setExpectDeliveryDate(Date expectDeliveryDate) {
        this.expectDeliveryDate = expectDeliveryDate;
    }

    public Date getDelayTime() {
        return delayTime;
    }

    public void setDelayTime(Date delayTime) {
        this.delayTime = delayTime;
    }

    public Integer getApplyCancelQty() {
        return applyCancelQty;
    }

    public void setApplyCancelQty(Integer applyCancelQty) {
        this.applyCancelQty = applyCancelQty;
    }

    public Integer getCancelQty() {
        return cancelQty;
    }

    public void setCancelQty(Integer cancelQty) {
        this.cancelQty = cancelQty;
    }

    public String getHandleResults() {
        return handleResults;
    }

    public void setHandleResults(String handleResults) {
        this.handleResults = handleResults;
    }

    public String getApplyReason() {
        return applyReason;
    }

    public void setApplyReason(String applyReason) {
        this.applyReason = applyReason;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getResponsibleParty() {
        return responsibleParty;
    }

    public void setResponsibleParty(String responsibleParty) {
        this.responsibleParty = responsibleParty;
    }

    public Integer getIsWaitReceiveOrder() {
        return isWaitReceiveOrder;
    }

    public void setIsWaitReceiveOrder(Integer isWaitReceiveOrder) {
        this.isWaitReceiveOrder = isWaitReceiveOrder;
    }

    public Integer getApplyQty() {
        return applyQty;
    }

    public void setApplyQty(Integer applyQty) {
        this.applyQty = applyQty;
    }

    public Date getAuditDelayTime() {
        return auditDelayTime;
    }

    public void setAuditDelayTime(Date auditDelayTime) {
        this.auditDelayTime = auditDelayTime;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getMaterialAuditResponsibleParty() {
        return materialAuditResponsibleParty;
    }

    public void setMaterialAuditResponsibleParty(String materialAuditResponsibleParty) {
        this.materialAuditResponsibleParty = materialAuditResponsibleParty;
    }

    public Integer getMaterialAuditDelayDay() {
        return materialAuditDelayDay;
    }

    public void setMaterialAuditDelayDay(Integer materialAuditDelayDay) {
        this.materialAuditDelayDay = materialAuditDelayDay;
    }

    public String getMaterialAuditBy() {
        return materialAuditBy;
    }

    public void setMaterialAuditBy(String materialAuditBy) {
        this.materialAuditBy = materialAuditBy;
    }

    public Date getMaterialAuditTime() {
        return materialAuditTime;
    }

    public void setMaterialAuditTime(Date materialAuditTime) {
        this.materialAuditTime = materialAuditTime;
    }

    public String getMaterialAuditRemark() {
        return materialAuditRemark;
    }

    public void setMaterialAuditRemark(String materialAuditRemark) {
        this.materialAuditRemark = materialAuditRemark;
    }

    public String getAuditBy() {
        return auditBy;
    }

    public void setAuditBy(String auditBy) {
        this.auditBy = auditBy;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public Integer getCancelSource() {
        return cancelSource;
    }

    public void setCancelSource(Integer cancelSource) {
        this.cancelSource = cancelSource;
    }

    public String getSourceOrderNo() {
        return sourceOrderNo;
    }

    public void setSourceOrderNo(String sourceOrderNo) {
        this.sourceOrderNo = sourceOrderNo;
    }

    public Integer getAuditDelayDay() {
        return auditDelayDay;
    }

    public void setAuditDelayDay(Integer auditDelayDay) {
        this.auditDelayDay = auditDelayDay;
    }

    public Integer getUnUpdateDeliveryTime() {
        return unUpdateDeliveryTime;
    }

    public void setUnUpdateDeliveryTime(Integer unUpdateDeliveryTime) {
        this.unUpdateDeliveryTime = unUpdateDeliveryTime;
    }

    public void buildByPurchaseOrderAdjustmentRecordAdd(PurchaseOrderAdjustmentRecordAddDto dto,
                                                        PurchaseOrderItemEntity purchaseOrderItemEntity,
                                                        PurchaseOrderAdjustmentRecordAddRequest request,
                                                        Integer status, String name) {
        setOrderId(dto.getOrderId());
        setOrderItemId(dto.getOrderItemId());
        setAdjustmentType(dto.getAdjustmentType());
        setPreviousStatus(dto.getPreviousStatus());
        setStatus(status);
        setProductId(purchaseOrderItemEntity.getProductId());
        setSpu(purchaseOrderItemEntity.getSpu());
        setSpecId(purchaseOrderItemEntity.getSpecId());
        setSkc(purchaseOrderItemEntity.getSkc());
        setSku(purchaseOrderItemEntity.getSku());
        setExpectDeliveryDate(dto.getExpectDeliveryDate());
        setDelayTime(dto.getDelayTime());
        setDelayDay(dto.getDelayTime() != null && dto.getExpectDeliveryDate() != null ? DateTimeUtils.getDaysOfTowDate(dto.getExpectDeliveryDate(), dto.getDelayTime()) : null);
        setReceiveOrderExpectDeliveryDate(dto.getReceiveOrderExpectDeliveryDate());
        setApplyCancelQty(Optional.ofNullable(dto.getApplyCancelQty()).orElse(NumberConstant.ZERO));
        setCancelQty(NumberConstant.ZERO);
        setApplyQty(dto.getApplyQty());
        // 工厂申请/采购申请/二次期货申请 - 原因拼装
        if (request.getAdjustmentType() != null && PurchaseOrderAdjustmentRecordTypeEnum.delayTypeList().contains(request.getAdjustmentType())) {
            setApplyReason(request.getReason());
        } else {
            setApplyReason(dto.getApplyReason());
        }
        setLocation(purchaseOrderItemEntity.getLocation());
        setCreateBy(Objects.nonNull(dto.getOperatorRealName()) ? dto.getOperatorRealName() : name);
        setUpdateBy(Objects.nonNull(dto.getOperatorRealName()) ? dto.getOperatorRealName() : name);
        setRemark(request.getRemark());
        setIsWaitReceiveOrder(dto.getIsWaitReceiveOrder());
        setCancelSource(Optional.ofNullable(dto.getCancelSource()).orElse(PurchaseOrderAdjustmentRecordCancelSourceEnum.NORMAL.getValue()));
        setSourceOrderNo(Optional.ofNullable(dto.getSourceOrderNo()).orElse(Strings.EMPTY));
    }

    public void buildByUpdatePurchaseOrderAdjustmentRecord(PurchaseOrderAdjustmentRecordAuditRequest request,
                                                           PurchaseOrderAdjustmentRecordAuditDto auditDto,
                                                           String name) {
        if (Objects.equals(PurchaseOrderAdjustmentRecordOperationTypeEnum.REJECT.name(), request.getOperationType())) {
            setStatus(PurchaseOrderAdjustmentRecordStatusEnum.REJECT.getValue());
            setAuditTime(new Date());
            setHandleResults(request.getReason());
            setAuditRemark(request.getReason());
            setAuditBy(Optional.ofNullable(request.getOperatorRealName()).orElse(name));
        }
        if (Objects.equals(PASS.name(), request.getOperationType())) {
            setStatus(PurchaseOrderAdjustmentRecordStatusEnum.PASS.getValue());
            setAuditTime(new Date());
            setAuditBy(Optional.ofNullable(request.getOperatorRealName()).orElse(name));
            setCancelQty(getApplyCancelQty());
            setAuditRemark(request.getAuditRemark());
            if (Arrays.asList(FACTORY_DELIVER_DELAYED_APPLY.getValue(), DIRECT_DELIVERY_DATE_APPLY.getValue(),
                    PURCHASE_CHANGE_DELIVERY_DATE_APPLY.getValue(), SECOND_DELIVERY_DATE_APPLY.getValue()).contains(getAdjustmentType())) {
                setHandleResults(request.getReason());
                setResponsibleParty(request.getResponsibleParty());
                setAuditDelayTime(request.getAuditDelayTime());
                setAuditDelayDay(request.getAuditDelayDay() == null ? NumberConstant.ZERO : request.getAuditDelayDay());
            }
        }
        if (Objects.equals(DECREMENT_PASS.name(), request.getOperationType())) {
            setStatus(PurchaseOrderAdjustmentRecordStatusEnum.PASS.getValue());
            setAuditTime(new Date());
            setAuditBy(Optional.ofNullable(request.getOperatorRealName()).orElse(name));
            setCancelQty(Optional.ofNullable(auditDto.getCancelQuantity()).orElse(NumberConstant.ZERO));
            if (Arrays.asList(FACTORY_DELIVER_DELAYED_APPLY.getValue(), PURCHASE_CHANGE_DELIVERY_DATE_APPLY.getValue(),
                    SECOND_DELIVERY_DATE_APPLY.getValue()).contains(getAdjustmentType())) {
                setHandleResults(request.getReason());
                setAuditRemark(request.getAuditRemark());
                setResponsibleParty(request.getResponsibleParty());
                setAuditDelayTime(request.getAuditDelayTime());
                setAuditDelayDay(request.getAuditDelayDay() == null ? NumberConstant.ZERO : request.getAuditDelayDay());
            }
        }
    }

    public void buildResponsibleParty(List<MaterialPurchaseOrderItemListByPurchaseOrderItemForSupplierDTO> listByPurchaseOrderItemForSupplierDTOS) {
        // 非MISI才有的逻辑
        if (LocationEnum.MISI.getValue().equals(location)) {
            return;
        }
        List<MaterialPurchaseOrderItemListByPurchaseOrderItemForSupplierDTO> hasMaxAbnormalNoList = listByPurchaseOrderItemForSupplierDTOS.stream().filter(s -> StrUtil.isNotEmpty(s.getMaxAbnormalNo())).collect(Collectors.toList());
        // 面料待审核 + 存在领料单（存在异常领料单） 则责任方为面料商 + 状态变更为待审核
        if (PurchaseOrderAdjustmentRecordStatusEnum.MATERIAL_PENDING.getValue().equals(status)
                && CollectionUtil.isNotEmpty(hasMaxAbnormalNoList)) {
            setMaterialAuditResponsibleParty(MaterialAuditResponsiblePartyEnum.FABRIC_SUPPLIER.getCode());
            setStatus(PurchaseOrderAdjustmentRecordStatusEnum.PENDING.getValue());
            setMaterialAuditDelayDay(NumberConstant.ZERO);
        }
    }

    public Integer getDelayDay() {
        return delayDay;
    }

    public void setDelayDay(Integer delayDay) {
        this.delayDay = delayDay;
    }
}
