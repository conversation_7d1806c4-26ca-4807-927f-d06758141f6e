package com.nsy.scm.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 供应商已读公告表(NoticeSupplierRelation)表实体类
 *
 * <AUTHOR>
 * @since 2022-08-05 15:40:53
 */
@Entity
@Table(name = "notice_supplier_relation")
@TableName("notice_supplier_relation")
public class NoticeSupplierRelationEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer noticeSupplierRelationId;

    /**
     * 供应商id
     */
    private Integer supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 公告id
     */
    private Integer noticeMessageId;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    public Integer getNoticeSupplierRelationId() {
        return noticeSupplierRelationId;
    }

    public void setNoticeSupplierRelationId(Integer noticeSupplierRelationId) {
        this.noticeSupplierRelationId = noticeSupplierRelationId;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Integer getNoticeMessageId() {
        return noticeMessageId;
    }

    public void setNoticeMessageId(Integer noticeMessageId) {
        this.noticeMessageId = noticeMessageId;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}

