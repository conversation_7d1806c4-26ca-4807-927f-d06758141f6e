package com.nsy.scm.repository.entity.purchase.urge;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.Date;

/**
 * 申请单重点款式催货跟进表
 *
 * <AUTHOR>
 * @since 2023/5/3 11:52
 */
@TableName("purchase_apply_urge_follow_up")
public class PurchaseApplyUrgeFollowUpEntity extends BaseMpEntity {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer urgeFollowUpId;

    /**
     * 采购申请单Id
     */
    private Integer applyId;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 生产类型：1-大货，2-现货
     */
    private Integer productionType;

    /**
     * 供应链品类Id
     */
    private Integer scmCategoryId;

    /**
     * 供应链品类名称
     */
    private String scmCategoryName;

    /**
     * 部门ID
     */
    private Integer departmentId;

    /**
     * 部门
     */
    private String businessType;

    /**
     * 小组Id
     */
    private Integer groupId;

    /**
     * 小组名称
     */
    private String groupName;

    /**
     * 店铺Id
     */
    private Integer storeId;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 申请时间
     */
    private Date applyDate;

    /**
     * 申请数量
     */
    private Integer applyQty;

    /**
     * spu
     */
    private String spu;

    /**
     * skc
     */
    private String skc;

    /**
     * 原图地址
     */
    private String imageUrl;

    /**
     * 当前催货级别
     */
    private String currentLabel;

    /**
     * 建议催货级别标签
     */
    private String suggestLabel;

    /**
     * 待确认催货级别
     */
    private String waitConfirmLabel;

    /**
     * 确认催货级别
     */
    private String confirmLabel;

    /**
     * 确认状态 0未确认, 1已确认, 2已执行, 3执行失败
     */
    private Integer status;

    /**
     * 重试次数: 默认重试3次
     */
    private Integer retryCount;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 爆款标签id
     */
    private Integer popularLabelId;

    /**
     * 爆款标签名称
     */
    private String popularLabelName;

    /**
     * 预测销量
     */
    private Integer predictedSaleQty;

    /**
     * 最近销量
     */
    private Integer recentSaleQty;

    /**
     * 私有库存
     */
    private Integer privateStock;

    /**
     * 待审申请数量
     */
    private Integer pendingApplyQty;

    /**
     * 在途数
     */
    private Integer inTransitQty;

    /**
     * 订单缺货数
     */
    private Integer oosQty;

    /**
     * 区域
     */
    private String location;

    /**
     * 主键
     */
    public Integer getUrgeFollowUpId() {
        return urgeFollowUpId;
    }

    /**
     * 主键
     */
    public void setUrgeFollowUpId(Integer urgeFollowUpId) {
        this.urgeFollowUpId = urgeFollowUpId;
    }

    /**
     * 采购申请单Id
     */
    public Integer getApplyId() {
        return applyId;
    }

    /**
     * 采购申请单Id
     */
    public void setApplyId(Integer applyId) {
        this.applyId = applyId;
    }

    /**
     * 申请单号
     */
    public String getApplyNo() {
        return applyNo;
    }

    /**
     * 申请单号
     */
    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    /**
     * 生产类型：1-大货，2-现货
     */
    public Integer getProductionType() {
        return productionType;
    }

    /**
     * 生产类型：1-大货，2-现货
     */
    public void setProductionType(Integer productionType) {
        this.productionType = productionType;
    }

    /**
     * 供应链品类Id
     */
    public Integer getScmCategoryId() {
        return scmCategoryId;
    }

    /**
     * 供应链品类Id
     */
    public void setScmCategoryId(Integer scmCategoryId) {
        this.scmCategoryId = scmCategoryId;
    }

    /**
     * 供应链品类名称
     */
    public String getScmCategoryName() {
        return scmCategoryName;
    }

    /**
     * 供应链品类名称
     */
    public void setScmCategoryName(String scmCategoryName) {
        this.scmCategoryName = scmCategoryName;
    }

    /**
     * 部门ID
     */
    public Integer getDepartmentId() {
        return departmentId;
    }

    /**
     * 部门ID
     */
    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    /**
     * 部门
     */
    public String getBusinessType() {
        return businessType;
    }

    /**
     * 部门
     */
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    /**
     * 小组Id
     */
    public Integer getGroupId() {
        return groupId;
    }

    /**
     * 小组Id
     */
    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    /**
     * 小组名称
     */
    public String getGroupName() {
        return groupName;
    }

    /**
     * 小组名称
     */
    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    /**
     * 店铺Id
     */
    public Integer getStoreId() {
        return storeId;
    }

    /**
     * 店铺Id
     */
    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    /**
     * 店铺名称
     */
    public String getStoreName() {
        return storeName;
    }

    /**
     * 店铺名称
     */
    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    /**
     * 申请时间
     */
    public Date getApplyDate() {
        return applyDate;
    }

    /**
     * 申请时间
     */
    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    /**
     * 申请数量
     */
    public Integer getApplyQty() {
        return applyQty;
    }

    /**
     * 申请数量
     */
    public void setApplyQty(Integer applyQty) {
        this.applyQty = applyQty;
    }

    /**
     * spu
     */
    public String getSpu() {
        return spu;
    }

    /**
     * spu
     */
    public void setSpu(String spu) {
        this.spu = spu;
    }

    /**
     * skc
     */
    public String getSkc() {
        return skc;
    }

    /**
     * skc
     */
    public void setSkc(String skc) {
        this.skc = skc;
    }

    /**
     * 原图地址
     */
    public String getImageUrl() {
        return imageUrl;
    }

    /**
     * 原图地址
     */
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    /**
     * 当前催货级别
     */
    public String getCurrentLabel() {
        return currentLabel;
    }

    public String getCurrentLabelOrEmpty() {
        return StringUtils.isBlank(currentLabel) ? "" : currentLabel;
    }

    /**
     * 当前催货级别
     */
    public void setCurrentLabel(String currentLabel) {
        this.currentLabel = currentLabel;
    }

    /**
     * 建议催货级别标签
     */
    public String getSuggestLabel() {
        return suggestLabel;
    }

    /**
     * 建议催货级别标签
     */
    public void setSuggestLabel(String suggestLabel) {
        this.suggestLabel = suggestLabel;
    }

    /**
     * 待确认催货级别
     */
    public String getWaitConfirmLabel() {
        return waitConfirmLabel;
    }

    public String getWaitConfirmLabelOrEmpty() {
        return StringUtils.isBlank(waitConfirmLabel) ? "" : waitConfirmLabel;
    }

    /**
     * 待确认催货级别
     */
    public void setWaitConfirmLabel(String waitConfirmLabel) {
        this.waitConfirmLabel = waitConfirmLabel;
    }

    /**
     * 确认催货级别
     */
    public String getConfirmLabel() {
        return confirmLabel;
    }

    public String getConfirmLabelOrEmpty() {
        return StringUtils.isBlank(confirmLabel) ? "" : confirmLabel;
    }

    /**
     * 确认催货级别
     */
    public void setConfirmLabel(String confirmLabel) {
        this.confirmLabel = confirmLabel;
    }

    /**
     * 确认状态 0未确认, 1已确认, 2已执行, 3执行失败
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 确认状态 0未确认, 1已确认, 2已执行, 3执行失败
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 重试次数: 默认重试3次
     */
    public Integer getRetryCount() {
        return retryCount;
    }

    /**
     * 重试次数: 默认重试3次
     */
    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    /**
     * 错误信息
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 错误信息
     */
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * 爆款标签id
     */
    public Integer getPopularLabelId() {
        return popularLabelId;
    }

    /**
     * 爆款标签id
     */
    public void setPopularLabelId(Integer popularLabelId) {
        this.popularLabelId = popularLabelId;
    }

    /**
     * 爆款标签名称
     */
    public String getPopularLabelName() {
        return popularLabelName;
    }

    /**
     * 爆款标签名称
     */
    public void setPopularLabelName(String popularLabelName) {
        this.popularLabelName = popularLabelName;
    }

    /**
     * 预测销量
     */
    public Integer getPredictedSaleQty() {
        return predictedSaleQty;
    }

    /**
     * 预测销量
     */
    public void setPredictedSaleQty(Integer predictedSaleQty) {
        this.predictedSaleQty = predictedSaleQty;
    }

    /**
     * 最近销量
     */
    public Integer getRecentSaleQty() {
        return recentSaleQty;
    }

    /**
     * 最近销量
     */
    public void setRecentSaleQty(Integer recentSaleQty) {
        this.recentSaleQty = recentSaleQty;
    }

    /**
     * 私有库存
     */
    public Integer getPrivateStock() {
        return privateStock;
    }

    /**
     * 私有库存
     */
    public void setPrivateStock(Integer privateStock) {
        this.privateStock = privateStock;
    }

    /**
     * 待审申请数量
     */
    public Integer getPendingApplyQty() {
        return pendingApplyQty;
    }

    /**
     * 待审申请数量
     */
    public void setPendingApplyQty(Integer pendingApplyQty) {
        this.pendingApplyQty = pendingApplyQty;
    }

    /**
     * 在途数
     */
    public Integer getInTransitQty() {
        return inTransitQty;
    }

    /**
     * 在途数
     */
    public void setInTransitQty(Integer inTransitQty) {
        this.inTransitQty = inTransitQty;
    }

    /**
     * 订单缺货数
     */
    public Integer getOosQty() {
        return oosQty;
    }

    /**
     * 订单缺货数
     */
    public void setOosQty(Integer oosQty) {
        this.oosQty = oosQty;
    }

    /**
     * 区域
     */
    public String getLocation() {
        return location;
    }

    /**
     * 区域
     */
    public void setLocation(String location) {
        this.location = location;
    }
}
