package com.nsy.scm.repository.sql.mapper.bd;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.permission.annatation.Permission;
import com.nsy.scm.business.request.replenishment.BdReplenishmentResultConfigPageRequest;
import com.nsy.scm.business.response.replenishment.BdReplenishmentResultConfigPageResponse;
import com.nsy.scm.repository.entity.bd.BdReplenishmentResultConfigEntity;
import org.apache.ibatis.annotations.Param;


/**
 * 针对表【bd_replenishment_result_config(补货结果配置表)】的数据库操作Mapper
 *
 * <AUTHOR>
 * @since 2023/5/23 13:52
 */
public interface BdReplenishmentResultConfigMapper extends BaseMapper<BdReplenishmentResultConfigEntity> {

    @Permission
    IPage<BdReplenishmentResultConfigPageResponse> pageList(IPage page, @Param("request") BdReplenishmentResultConfigPageRequest request);

}




