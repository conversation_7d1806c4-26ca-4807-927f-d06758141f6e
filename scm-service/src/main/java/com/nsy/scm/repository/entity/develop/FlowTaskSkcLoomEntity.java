package com.nsy.scm.repository.entity.develop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 任务skc人工报价明细表
 */
@Entity
@Table(name = "flow_task_skc_loom")
@TableName("flow_task_skc_loom")
public class FlowTaskSkcLoomEntity extends BaseMpEntity {
    /**
     *
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer flowTaskSkcLoomId;

    /**
     * 记录报核价任务或编辑bom任务的任务号
     */
    private Integer taskId;

    /**
     * EDIT_BOM编辑bom
     */
    private String taskType;

    /**
     * skc
     */
    private String skc;

    /**
     * 织机类型
     */
    private String loomType;

    /**
     * 机台
     */
    private String loomMachine;

    /**
     * 针种
     */
    private String loomNeedle;

    /**
     * 织机单价/分钟
     */
    private BigDecimal unitPrice;

    private BigDecimal confirmUnitPrice;

    /**
     * 织机时间(分钟)
     */
    private BigDecimal loomMinutes;

    private BigDecimal confirmLoomMinutes;

    /**
     * 价格，织机单价*织机时间
     */
    private BigDecimal price;

    /**
     * 核价价格
     */
    private BigDecimal confirmPrice;

    /**
     * 报价备注
     */
    private String offeringRemark;

    /**
     * 异议备注
     */
    private String rejectRemark;

    /**
     * 核价备注
     */
    private String checkRemark;

    /**
     * 地区
     */
    private String location;

    private String remark;

    public Integer getFlowTaskSkcLoomId() {
        return flowTaskSkcLoomId;
    }

    public void setFlowTaskSkcLoomId(Integer flowTaskSkcLoomId) {
        this.flowTaskSkcLoomId = flowTaskSkcLoomId;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getLoomType() {
        return loomType;
    }

    public void setLoomType(String loomType) {
        this.loomType = loomType;
    }

    public String getLoomMachine() {
        return loomMachine;
    }

    public void setLoomMachine(String loomMachine) {
        this.loomMachine = loomMachine;
    }

    public String getLoomNeedle() {
        return loomNeedle;
    }

    public void setLoomNeedle(String loomNeedle) {
        this.loomNeedle = loomNeedle;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getLoomMinutes() {
        return loomMinutes;
    }

    public void setLoomMinutes(BigDecimal loomMinutes) {
        this.loomMinutes = loomMinutes;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getConfirmPrice() {
        return confirmPrice;
    }

    public void setConfirmPrice(BigDecimal confirmPrice) {
        this.confirmPrice = confirmPrice;
    }

    public String getOfferingRemark() {
        return offeringRemark;
    }

    public void setOfferingRemark(String offeringRemark) {
        this.offeringRemark = offeringRemark;
    }

    public String getRejectRemark() {
        return rejectRemark;
    }

    public void setRejectRemark(String rejectRemark) {
        this.rejectRemark = rejectRemark;
    }

    public String getCheckRemark() {
        return checkRemark;
    }

    public void setCheckRemark(String checkRemark) {
        this.checkRemark = checkRemark;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public BigDecimal getConfirmUnitPrice() {
        return confirmUnitPrice;
    }

    public void setConfirmUnitPrice(BigDecimal confirmUnitPrice) {
        this.confirmUnitPrice = confirmUnitPrice;
    }

    public BigDecimal getConfirmLoomMinutes() {
        return confirmLoomMinutes;
    }

    public void setConfirmLoomMinutes(BigDecimal confirmLoomMinutes) {
        this.confirmLoomMinutes = confirmLoomMinutes;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
