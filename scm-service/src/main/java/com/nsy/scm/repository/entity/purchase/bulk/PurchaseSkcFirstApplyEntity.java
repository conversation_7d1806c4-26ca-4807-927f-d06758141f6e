package com.nsy.scm.repository.entity.purchase.bulk;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 部门skc申请首单信息表
 *
 * @TableName purchase_skc_first_apply
 */
@TableName("purchase_skc_first_apply")
public class PurchaseSkcFirstApplyEntity extends BaseMpEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    /**
     * 主键
     */
    private Integer skcFirstApplyId;
    /**
     * 商品编码
     */
    private String spu;
    /**
     * 商品Id
     */
    private Integer productId;
    /**
     * 颜色编码
     */
    private String skc;
    /**
     * 公司首次申请时间
     */
    private Date companyFirstApplyDate;
    /**
     * 公司首次申请单号
     */
    private String companyFirstApplyNo;
    /**
     * 公司首次申请单id
     */
    private Integer companyFirstApplyId;
    /**
     * B2C首次申请时间
     */
    private Date b2cFirstApplyDate;
    /**
     * B2C首次申请单号
     */
    private String b2cFirstApplyNo;
    /**
     * B2C首次申请单id
     */
    private Integer b2cFirstApplyId;
    /**
     * dokotoo首次申请时间
     */
    private Date dokotooFirstApplyDate;
    /**
     * dokotoo首次申请单号
     */
    private String dokotooFirstApplyNo;
    /**
     * dokotoo首次申请单id
     */
    private Integer dokotooFirstApplyId;
    /**
     * B2B首次申请时间
     */
    private Date b2bFirstApplyDate;
    /**
     * B2B首次申请单号
     */
    private String b2bFirstApplyNo;
    /**
     * B2B首次申请单id
     */
    private Integer b2bFirstApplyId;
    /**
     * DTC首次申请时间
     */
    private Date dtcFirstApplyDate;
    /**
     * DTC首次申请单号
     */
    private String dtcFirstApplyNo;
    /**
     * DTC首次申请单id
     */
    private Integer dtcFirstApplyId;
    /**
     * 内贸阿星首次申请时间 - 弃用
     */
    private Date axingFirstApplyDate;
    /**
     * 内贸阿星首次申请单号 - 弃用
     */
    private String axingFirstApplyNo;
    /**
     * 内贸阿星首次申请单id - 弃用
     */
    private Integer axingFirstApplyId;
    /**
     * 内贸志超首次申请时间 - 弃用
     */
    private Date zhichaoFirstApplyDate;
    /**
     * 内贸志超首次申请单号 - 弃用
     */
    private String zhichaoFirstApplyNo;
    /**
     * 内贸志超首次申请单id - 弃用
     */
    private Integer zhichaoFirstApplyId;
    /**
     * 区域
     */
    private String location;
    /**
     * 亚马逊首次申请时间
     */
    private Date amazonFirstApplyDate;
    /**
     * 亚马逊首次申请单号
     */
    private String amazonFirstApplyNo;
    /**
     * 亚马逊首次申请单id
     */
    private Integer amazonFirstApplyId;

    /**
     * 内贸首次申请时间
     */
    private Date domesticFirstApplyDate;
    /**
     * 内贸首次申请单号
     */
    private String domesticFirstApplyNo;
    /**
     * 内贸首次申请单id
     */
    private Integer domesticFirstApplyId;

    /**
     * 部门首单idList
     */
    public List<Integer> getApplyIdList() {
        return Arrays.asList(b2cFirstApplyId, b2bFirstApplyId, dtcFirstApplyId, dokotooFirstApplyId, domesticFirstApplyId);
    }

    /**
     * 主键
     */
    public Integer getSkcFirstApplyId() {
        return skcFirstApplyId;
    }

    /**
     * 主键
     */
    public void setSkcFirstApplyId(Integer skcFirstApplyId) {
        this.skcFirstApplyId = skcFirstApplyId;
    }

    /**
     * 商品编码
     */
    public String getSpu() {
        return spu;
    }

    /**
     * 商品编码
     */
    public void setSpu(String spu) {
        this.spu = spu;
    }

    /**
     * 颜色编码
     */
    public String getSkc() {
        return skc;
    }

    /**
     * 颜色编码
     */
    public void setSkc(String skc) {
        this.skc = skc;
    }

    /**
     * 公司首次申请时间
     */
    public Date getCompanyFirstApplyDate() {
        return companyFirstApplyDate;
    }

    /**
     * 公司首次申请时间
     */
    public void setCompanyFirstApplyDate(Date companyFirstApplyDate) {
        this.companyFirstApplyDate = companyFirstApplyDate;
    }

    /**
     * 公司首次申请单号
     */
    public String getCompanyFirstApplyNo() {
        return companyFirstApplyNo;
    }

    /**
     * 公司首次申请单号
     */
    public void setCompanyFirstApplyNo(String companyFirstApplyNo) {
        this.companyFirstApplyNo = companyFirstApplyNo;
    }

    /**
     * 公司首次申请单id
     */
    public Integer getCompanyFirstApplyId() {
        return companyFirstApplyId;
    }

    /**
     * 公司首次申请单id
     */
    public void setCompanyFirstApplyId(Integer companyFirstApplyId) {
        this.companyFirstApplyId = companyFirstApplyId;
    }

    /**
     * B2C首次申请时间
     */
    public Date getB2CFirstApplyDate() {
        return b2cFirstApplyDate;
    }

    /**
     * B2C首次申请时间
     */
    public void setB2CFirstApplyDate(Date b2cFirstApplyDate) {
        this.b2cFirstApplyDate = b2cFirstApplyDate;
    }

    /**
     * B2C首次申请单号
     */
    public String getB2CFirstApplyNo() {
        return b2cFirstApplyNo;
    }

    /**
     * B2C首次申请单号
     */
    public void setB2CFirstApplyNo(String b2cFirstApplyNo) {
        this.b2cFirstApplyNo = b2cFirstApplyNo;
    }

    /**
     * B2C首次申请单id
     */
    public Integer getB2CFirstApplyId() {
        return b2cFirstApplyId;
    }

    /**
     * B2C首次申请单id
     */
    public void setB2CFirstApplyId(Integer b2cFirstApplyId) {
        this.b2cFirstApplyId = b2cFirstApplyId;
    }

    /**
     * B2B首次申请时间
     */
    public Date getB2BFirstApplyDate() {
        return b2bFirstApplyDate;
    }

    /**
     * B2B首次申请时间
     */
    public void setB2BFirstApplyDate(Date b2bFirstApplyDate) {
        this.b2bFirstApplyDate = b2bFirstApplyDate;
    }

    /**
     * B2B首次申请单号
     */
    public String getB2BFirstApplyNo() {
        return b2bFirstApplyNo;
    }

    /**
     * B2B首次申请单号
     */
    public void setB2BFirstApplyNo(String b2bFirstApplyNo) {
        this.b2bFirstApplyNo = b2bFirstApplyNo;
    }

    /**
     * B2B首次申请单id
     */
    public Integer getB2BFirstApplyId() {
        return b2bFirstApplyId;
    }

    /**
     * B2B首次申请单id
     */
    public void setB2BFirstApplyId(Integer b2bFirstApplyId) {
        this.b2bFirstApplyId = b2bFirstApplyId;
    }

    /**
     * DTC首次申请时间
     */
    public Date getDtcFirstApplyDate() {
        return dtcFirstApplyDate;
    }

    /**
     * DTC首次申请时间
     */
    public void setDtcFirstApplyDate(Date dtcFirstApplyDate) {
        this.dtcFirstApplyDate = dtcFirstApplyDate;
    }

    /**
     * DTC首次申请单号
     */
    public String getDtcFirstApplyNo() {
        return dtcFirstApplyNo;
    }

    /**
     * DTC首次申请单号
     */
    public void setDtcFirstApplyNo(String dtcFirstApplyNo) {
        this.dtcFirstApplyNo = dtcFirstApplyNo;
    }

    /**
     * DTC首次申请单id
     */
    public Integer getDtcFirstApplyId() {
        return dtcFirstApplyId;
    }

    /**
     * DTC首次申请单id
     */
    public void setDtcFirstApplyId(Integer dtcFirstApplyId) {
        this.dtcFirstApplyId = dtcFirstApplyId;
    }

    /**
     * 内贸阿星首次申请时间
     */
    public Date getAxingFirstApplyDate() {
        return axingFirstApplyDate;
    }

    /**
     * 内贸阿星首次申请时间
     */
    public void setAxingFirstApplyDate(Date axingFirstApplyDate) {
        this.axingFirstApplyDate = axingFirstApplyDate;
    }

    /**
     * 内贸阿星首次申请单号
     */
    public String getAxingFirstApplyNo() {
        return axingFirstApplyNo;
    }

    /**
     * 内贸阿星首次申请单号
     */
    public void setAxingFirstApplyNo(String axingFirstApplyNo) {
        this.axingFirstApplyNo = axingFirstApplyNo;
    }

    /**
     * 内贸阿星首次申请单id
     */
    public Integer getAxingFirstApplyId() {
        return axingFirstApplyId;
    }

    /**
     * 内贸阿星首次申请单id
     */
    public void setAxingFirstApplyId(Integer axingFirstApplyId) {
        this.axingFirstApplyId = axingFirstApplyId;
    }

    /**
     * 内贸志超首次申请时间
     */
    public Date getZhichaoFirstApplyDate() {
        return zhichaoFirstApplyDate;
    }

    /**
     * 内贸志超首次申请时间
     */
    public void setZhichaoFirstApplyDate(Date zhichaoFirstApplyDate) {
        this.zhichaoFirstApplyDate = zhichaoFirstApplyDate;
    }

    /**
     * 内贸志超首次申请单号
     */
    public String getZhichaoFirstApplyNo() {
        return zhichaoFirstApplyNo;
    }

    /**
     * 内贸志超首次申请单号
     */
    public void setZhichaoFirstApplyNo(String zhichaoFirstApplyNo) {
        this.zhichaoFirstApplyNo = zhichaoFirstApplyNo;
    }

    /**
     * 内贸志超首次申请单id
     */
    public Integer getZhichaoFirstApplyId() {
        return zhichaoFirstApplyId;
    }

    /**
     * 内贸志超首次申请单id
     */
    public void setZhichaoFirstApplyId(Integer zhichaoFirstApplyId) {
        this.zhichaoFirstApplyId = zhichaoFirstApplyId;
    }

    /**
     * 区域
     */
    public String getLocation() {
        return location;
    }

    /**
     * 区域
     */
    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Date getAmazonFirstApplyDate() {
        return amazonFirstApplyDate;
    }

    public void setAmazonFirstApplyDate(Date amazonFirstApplyDate) {
        this.amazonFirstApplyDate = amazonFirstApplyDate;
    }

    public String getAmazonFirstApplyNo() {
        return amazonFirstApplyNo;
    }

    public void setAmazonFirstApplyNo(String amazonFirstApplyNo) {
        this.amazonFirstApplyNo = amazonFirstApplyNo;
    }

    public Integer getAmazonFirstApplyId() {
        return amazonFirstApplyId;
    }

    public void setAmazonFirstApplyId(Integer amazonFirstApplyId) {
        this.amazonFirstApplyId = amazonFirstApplyId;
    }

    public Date getDokotooFirstApplyDate() {
        return dokotooFirstApplyDate;
    }

    public void setDokotooFirstApplyDate(Date dokotooFirstApplyDate) {
        this.dokotooFirstApplyDate = dokotooFirstApplyDate;
    }

    public String getDokotooFirstApplyNo() {
        return dokotooFirstApplyNo;
    }

    public void setDokotooFirstApplyNo(String dokotooFirstApplyNo) {
        this.dokotooFirstApplyNo = dokotooFirstApplyNo;
    }

    public Integer getDokotooFirstApplyId() {
        return dokotooFirstApplyId;
    }

    public void setDokotooFirstApplyId(Integer dokotooFirstApplyId) {
        this.dokotooFirstApplyId = dokotooFirstApplyId;
    }

    public Date getDomesticFirstApplyDate() {
        return domesticFirstApplyDate;
    }

    public void setDomesticFirstApplyDate(Date domesticFirstApplyDate) {
        this.domesticFirstApplyDate = domesticFirstApplyDate;
    }

    public String getDomesticFirstApplyNo() {
        return domesticFirstApplyNo;
    }

    public void setDomesticFirstApplyNo(String domesticFirstApplyNo) {
        this.domesticFirstApplyNo = domesticFirstApplyNo;
    }

    public Integer getDomesticFirstApplyId() {
        return domesticFirstApplyId;
    }

    public void setDomesticFirstApplyId(Integer domesticFirstApplyId) {
        this.domesticFirstApplyId = domesticFirstApplyId;
    }
}
