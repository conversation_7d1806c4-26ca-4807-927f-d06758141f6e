package com.nsy.scm.repository.entity.stock;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <p>
 * 订单预配货信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-04
 */
@Entity
@Table(name = "order_pre_match")
@TableName("order_pre_match")
public class OrderPreMatchEntity extends BaseMpEntity {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer orderPreMatchId;

    /**
     * erp的预配Id
     */
    private Integer erpOrderPreMatchId;

    /**
     * 订单
     */
    private String tid;

    /**
     * 订单明细
     */
    private String oid;

    /**
     * 仓库Id
     */
    private Integer spaceId;

    /**
     * erp的product_spec.id
     */
    private Integer erpSpecId;

    /**
     * 预配数量
     */
    private Integer preMatchNum;

    /**
     * erp的业务库存Id
     */
    private Integer erpPrivateStockId;


    public Integer getOrderPreMatchId() {
        return orderPreMatchId;
    }

    public void setOrderPreMatchId(Integer orderPreMatchId) {
        this.orderPreMatchId = orderPreMatchId;
    }

    public Integer getErpOrderPreMatchId() {
        return erpOrderPreMatchId;
    }

    public void setErpOrderPreMatchId(Integer erpOrderPreMatchId) {
        this.erpOrderPreMatchId = erpOrderPreMatchId;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public Integer getErpSpecId() {
        return erpSpecId;
    }

    public void setErpSpecId(Integer erpSpecId) {
        this.erpSpecId = erpSpecId;
    }

    public Integer getPreMatchNum() {
        return preMatchNum;
    }

    public void setPreMatchNum(Integer preMatchNum) {
        this.preMatchNum = preMatchNum;
    }

    public Integer getErpPrivateStockId() {
        return erpPrivateStockId;
    }

    public void setErpPrivateStockId(Integer erpPrivateStockId) {
        this.erpPrivateStockId = erpPrivateStockId;
    }
}
