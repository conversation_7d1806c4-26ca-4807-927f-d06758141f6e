package com.nsy.scm.repository.sql.mapper.material;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.scm.dto.domain.supplier.TabCountDto;
import com.nsy.api.scm.dto.request.material.MaterialPreparationOnlineImportPageRequest;
import com.nsy.api.scm.dto.request.material.MaterialPreparationOnlineImportProductCancelRequest;
import com.nsy.api.scm.dto.response.material.MaterialPreparationOnlineImportLastImportCountRes;
import com.nsy.api.scm.dto.response.material.MaterialPreparationOnlineImportPageRes;
import com.nsy.permission.annatation.Permission;
import com.nsy.scm.common.annotation.TableIgnoreTenant;
import com.nsy.scm.repository.entity.material.MaterialPreparationOnlineImportEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <h3>线上备料导入 Mapper 接口</h3>
 *
 * <AUTHOR> Chao
 * @date 2023/11/24 11:02
 */
public interface MaterialPreparationOnlineImportMapper extends BaseMapper<MaterialPreparationOnlineImportEntity> {
    /**
     * 先查询主键ID，后回表查询
     *
     * @param page
     * @param query
     * @return
     */
    @Permission
    @TableIgnoreTenant(ignoreTableNameSpEL = "supplier")
    IPage<String> pageByIdsForPermission(Page<MaterialPreparationOnlineImportPageRequest> page, @Param("query") MaterialPreparationOnlineImportPageRequest query, @Param("isPermission") boolean isPermission);

    @TableIgnoreTenant(ignoreTableNameSpEL = "supplier")
    IPage<String> pageByIds(Page<MaterialPreparationOnlineImportPageRequest> page, @Param("query") MaterialPreparationOnlineImportPageRequest query, @Param("isPermission") boolean isPermission);

    @Permission
    IPage<MaterialPreparationOnlineImportPageRes> pageByListForPermission(Page<MaterialPreparationOnlineImportPageRequest> page, @Param("query") MaterialPreparationOnlineImportPageRequest query);

    @Permission
    List<TabCountDto> tabCount(@Param("statusList") Collection<Integer> statusList);

    @Permission
    @TableIgnoreTenant(ignoreTableNameSpEL = "supplier")
    List<MaterialPreparationOnlineImportPageRes> listByRequestForPermission(@Param("query") MaterialPreparationOnlineImportPageRequest query, @Param("isPermission") boolean isPermission);

    @TableIgnoreTenant(ignoreTableNameSpEL = "supplier")
    List<MaterialPreparationOnlineImportPageRes> listByRequest(@Param("query") MaterialPreparationOnlineImportPageRequest query, @Param("isPermission") boolean isPermission);

    MaterialPreparationOnlineImportLastImportCountRes lastImportCount();

    /**
     * 不能过滤删除的
     *
     * @param batchNo
     * @param colorCardIds
     * @return
     */
    List<MaterialPreparationOnlineImportEntity> lastListByBatchNoAndColorCardIds(@Param("batchNo") String batchNo,
                                                                                 @Param("colorCardIds") Collection<Integer> colorCardIds,
                                                                                 @Param("mode") String mode);

    /**
     * 商品模式 - 逻辑删除 - 根部门+SKC
     *
     * @param list
     */
    void deleteEntityListForProduct(@Param("list") List<MaterialPreparationOnlineImportEntity> list, @Param("status") Integer status);

    /**
     * 商品模式 - 逻辑删除 - 根部门+色卡ID
     *
     * @param list
     */
    void deleteEntityListForMaterial(@Param("list") List<MaterialPreparationOnlineImportEntity> list, @Param("status") Integer status);

    /**
     * 根据批次号 + SKC + 状态查询
     *
     * @param requests
     * @param statusList
     * @return
     */
    List<MaterialPreparationOnlineImportEntity> listByBatchNosAndSkcsAndStatus(Set<MaterialPreparationOnlineImportProductCancelRequest> requests, Collection<Integer> statusList);
}
