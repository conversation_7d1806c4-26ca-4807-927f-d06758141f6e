package com.nsy.scm.repository.dao.lx.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.scm.repository.entity.lx.LxCompletedPurchaseOrderQuantityEntity;
import com.nsy.scm.repository.dao.lx.LxCompletedPurchaseOrderQuantityDao;
import com.nsy.scm.repository.sql.mapper.lx.LxCompletedPurchaseOrderQuantityMapper;
import org.springframework.stereotype.Repository;


/**
 * 领星已完成采购单数量表 dao实现类
 * <AUTHOR>
 * @createDate 2024-07-01 14:44:02
 */
@Repository
public class LxCompletedPurchaseOrderQuantityDaoImpl extends ServiceImpl<LxCompletedPurchaseOrderQuantityMapper, LxCompletedPurchaseOrderQuantityEntity>
    implements LxCompletedPurchaseOrderQuantityDao {

}
