package com.nsy.scm.repository.dao.bd.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.scm.dto.domain.bd.BdMainMarkDto;
import com.nsy.api.scm.dto.request.db.BdMainMarkPageRequest;
import com.nsy.scm.constant.NumberConstant;
import com.nsy.scm.repository.entity.bd.BdMainMarkEntity;
import com.nsy.scm.repository.dao.bd.BdMainMarkDao;
import com.nsy.scm.repository.sql.mapper.bd.BdMainMarkMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 主唛表 dao实现类
 *
 * <AUTHOR>
 * @since 2024-03-14
 */
@Repository
public class BdMainMarkDaoImpl extends ServiceImpl<BdMainMarkMapper, BdMainMarkEntity> implements BdMainMarkDao {

    @Override
    public Page<BdMainMarkEntity> page(BdMainMarkPageRequest request) {
        return page(new Page<>(request.getPageIndex(), request.getPageSize()), Wrappers.<BdMainMarkEntity>lambdaQuery()
                .eq(BdMainMarkEntity::getIsDelete, NumberConstant.ZERO)
                .like(StringUtils.hasText(request.getMainMarkName()), BdMainMarkEntity::getMainMarkName, request.getMainMarkName())
                .like(StringUtils.hasText(request.getMainMarkNumber()), BdMainMarkEntity::getMainMarkNumber, request.getMainMarkNumber())
                .orderByDesc(BdMainMarkEntity::getBdMainMarkId));
    }

    @Override
    public List<BdMainMarkDto> queryDtoByIdIn(Collection<Integer> mainMarkIds) {
        return this.baseMapper.queryDtoByIdIn(mainMarkIds);
    }

    @Override
    public List<BdMainMarkEntity> queryExist(Integer bdMainMarkId, String mainMarkNumber, String mainMarkName) {
        LambdaQueryWrapper<BdMainMarkEntity> queryWrapper = new LambdaQueryWrapper<BdMainMarkEntity>()
                .eq(BdMainMarkEntity::getIsDelete, NumberConstant.ZERO)
                .ne(Objects.nonNull(bdMainMarkId), BdMainMarkEntity::getBdMainMarkId, bdMainMarkId)
                .eq(BdMainMarkEntity::getMainMarkNumber, mainMarkNumber).eq(BdMainMarkEntity::getMainMarkName, mainMarkName);
        return this.list(queryWrapper);
    }

    @Override
    public void deleteBatchByIds(List<Integer> mainMarkIds) {
        if (CollectionUtils.isEmpty(mainMarkIds)) {
            return;
        }
        this.baseMapper.deleteBatchByIds(mainMarkIds);
    }

    @Override
    public List<BdMainMarkEntity> getAll() {
        LambdaQueryWrapper<BdMainMarkEntity> queryWrapper = new LambdaQueryWrapper<BdMainMarkEntity>()
                .eq(BdMainMarkEntity::getIsDelete, NumberConstant.ZERO);
        return this.list(queryWrapper);
    }
}




