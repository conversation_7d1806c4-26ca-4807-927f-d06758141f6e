package com.nsy.scm.repository.sql.mapper.returnrate;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.scm.dto.request.returnrate.CategoryReturnRateListRequest;
import com.nsy.api.scm.dto.response.returnrate.CategoryReturnRateListResponse;
import com.nsy.scm.repository.entity.returnrate.CategoryReturnRateConfigEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 针对表【bd_category_return_rate_config(品类退货率配置表)】的数据库操作Mapper
 *
 * <AUTHOR>
 * @since 2023/7/14 10:33
 */
public interface CategoryReturnRateConfigMapper extends BaseMapper<CategoryReturnRateConfigEntity> {

    IPage<CategoryReturnRateListResponse> pageQuery(IPage page, @Param("request") CategoryReturnRateListRequest request);

    Integer getCategoryIsExist(@Param("categoryIdList") List<Integer> categoryIdList, @Param("location") String location);

}




