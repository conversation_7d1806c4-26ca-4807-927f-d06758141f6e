package com.nsy.scm.repository.entity.bd;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.math.BigDecimal;

/**
 * 补货结果配置详情表
 *
 * <AUTHOR>
 * @since 2023/5/23 13:52
 */
@TableName("bd_replenishment_result_config_item")
public class BdReplenishmentResultConfigItemEntity extends BaseMpEntity {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer ruleItemId;

    /**
     * 标签配置Id
     */
    private Integer ruleId;

    /**
     * 订单数最小值
     */
    private Integer orderQtyMin;

    /**
     * 订单数最大值
     */
    private Integer orderQtyMax;

    /**
     * 补货倍数
     */
    private BigDecimal replenishmentMultiples;

    /**
     * 区域
     */
    private String location;

    /**
     * 主键
     */
    public Integer getRuleItemId() {
        return ruleItemId;
    }

    /**
     * 主键
     */
    public void setRuleItemId(Integer ruleItemId) {
        this.ruleItemId = ruleItemId;
    }

    /**
     * 标签配置Id
     */
    public Integer getRuleId() {
        return ruleId;
    }

    /**
     * 标签配置Id
     */
    public void setRuleId(Integer ruleId) {
        this.ruleId = ruleId;
    }

    /**
     * 订单数最小值
     */
    public Integer getOrderQtyMin() {
        return orderQtyMin;
    }

    /**
     * 订单数最小值
     */
    public void setOrderQtyMin(Integer orderQtyMin) {
        this.orderQtyMin = orderQtyMin;
    }

    /**
     * 订单数最大值
     */
    public Integer getOrderQtyMax() {
        return orderQtyMax;
    }

    /**
     * 订单数最大值
     */
    public void setOrderQtyMax(Integer orderQtyMax) {
        this.orderQtyMax = orderQtyMax;
    }

    /**
     * 补货倍数
     */
    public BigDecimal getReplenishmentMultiples() {
        return replenishmentMultiples;
    }

    /**
     * 补货倍数
     */
    public void setReplenishmentMultiples(BigDecimal replenishmentMultiples) {
        this.replenishmentMultiples = replenishmentMultiples;
    }

    /**
     * 区域
     */
    public String getLocation() {
        return location;
    }

    /**
     * 区域
     */
    public void setLocation(String location) {
        this.location = location;
    }
}
