package com.nsy.scm.repository.dao.product.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.scm.constant.MybatisQueryConstant;
import com.nsy.scm.constant.TrueOrFalseConstant;
import com.nsy.scm.repository.dao.product.ConfigStyleDao;
import com.nsy.scm.repository.entity.product.ConfigStyleEntity;
import com.nsy.scm.repository.sql.mapper.product.ConfigStyleMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 针对表【商品风格标签表】的数据库操作Dao实现
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
public class ConfigStyleDaoImpl extends ServiceImpl<ConfigStyleMapper, ConfigStyleEntity> implements ConfigStyleDao {

    @Override
    public List<ConfigStyleEntity> findByStyleIds(List<Integer> styleIds) {
        if (CollectionUtils.isEmpty(styleIds)) {
            return Collections.emptyList();
        }
        return list(Wrappers.<ConfigStyleEntity>lambdaQuery().in(ConfigStyleEntity::getStyleId, styleIds).eq(ConfigStyleEntity::getIsDelete, TrueOrFalseConstant.NOT_DELETED));
    }

    @Override
    public ConfigStyleEntity findByStyleId(Integer styleId) {
        if (Objects.isNull(styleId)) {
            return null;
        }
        return getOne(Wrappers.<ConfigStyleEntity>lambdaQuery().eq(ConfigStyleEntity::getStyleId, styleId).last(MybatisQueryConstant.QUERY_FIRST));
    }

    @Override
    public List<ConfigStyleEntity> findAllByStyleIds(List<Integer> styleIds) {
        if (CollectionUtils.isEmpty(styleIds)) {
            return Collections.emptyList();
        }
        return list(Wrappers.<ConfigStyleEntity>lambdaQuery().in(ConfigStyleEntity::getStyleId, styleIds));
    }

    @Override
    public void removeByStyleIds(List<Integer> styleIds) {
        if (CollectionUtils.isEmpty(styleIds)) {
            return;
        }
        remove(Wrappers.<ConfigStyleEntity>lambdaQuery().in(ConfigStyleEntity::getStyleId, styleIds));
    }
}
