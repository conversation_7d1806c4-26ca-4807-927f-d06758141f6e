package com.nsy.scm.repository.dao.purchase.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.scm.dto.domain.material.PurchasePlanMaterialUsageDTO;
import com.nsy.api.scm.dto.domain.supplier.TabCountDto;
import com.nsy.api.scm.dto.request.material.MaterialPurchaseOrderUsageConfirmTabCountRequest;
import com.nsy.scm.constant.bulk.PurchasePlanStatusEnum;
import com.nsy.scm.enumstable.product.bom.MaterialTypeEnum;
import com.nsy.scm.repository.dao.purchase.PurchasePlanMaterialUsageDao;
import com.nsy.scm.repository.entity.purchase.bulk.PurchasePlanMaterialUsageEntity;
import com.nsy.scm.repository.projection.material.PlanMaterialUsageProjection;
import com.nsy.scm.repository.sql.mapper.purchase.bulk.PurchasePlanMaterialUsageMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 采购计划物料用量 dao实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
@Repository
public class PurchasePlanMaterialUsageDaoImpl extends ServiceImpl<PurchasePlanMaterialUsageMapper, PurchasePlanMaterialUsageEntity> implements PurchasePlanMaterialUsageDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchasePlanMaterialUsageDaoImpl.class);

    @Override
    public void baseSaveBatch(List<PurchasePlanMaterialUsageEntity> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            entityList.forEach(s -> {
                // 原始色卡没有初始值的时候 = 当前值
                if (s.getOriginColorCardId() == null) {
                    s.setOriginColorCardId(s.getColorCardId());
                }
            });
            saveBatch(entityList);
        }
    }

    @Override
    public List<PurchasePlanMaterialUsageEntity> planMaterialUsage(List<Integer> allPlanIds, MaterialTypeEnum materialTypeEnum) {
        if (materialTypeEnum == null || CollectionUtils.isEmpty(allPlanIds)) {
            return Collections.emptyList();
        }
        return getBaseMapper().selectList(new LambdaQueryWrapper<PurchasePlanMaterialUsageEntity>()
                .eq(PurchasePlanMaterialUsageEntity::getMaterialType, materialTypeEnum.name())
                .in(PurchasePlanMaterialUsageEntity::getPlanId, allPlanIds));
    }

    @Override
    public List<PurchasePlanMaterialUsageEntity> planMaterialUsage(List<Integer> planIds) {
        if (CollectionUtils.isEmpty(planIds)) {
            return Collections.emptyList();
        }
        return getBaseMapper().selectList(new LambdaQueryWrapper<PurchasePlanMaterialUsageEntity>()
                .in(PurchasePlanMaterialUsageEntity::getPlanId, planIds));
    }

    @Override
    public void deleteMaterialUsage(Collection<Integer> planIds) {
        if (CollectionUtils.isEmpty(planIds)) {
            return;
        }
        getBaseMapper().delete(new LambdaQueryWrapper<PurchasePlanMaterialUsageEntity>()
                .in(PurchasePlanMaterialUsageEntity::getPlanId, planIds));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void overwriteInsert(List<Integer> activePlanIds, List<PurchasePlanMaterialUsageEntity> allUsages) {
        Set<Integer> delPlanIds = allUsages.stream().map(PurchasePlanMaterialUsageEntity::getPlanId).collect(Collectors.toSet());
        if (delPlanIds.size() != activePlanIds.size()) {
            LOGGER.warn("overwriteInsert actualPlanIds not match activePlanIds, actualPlanIds = {}, activePlanIds = {}", delPlanIds, activePlanIds);
        }
        delPlanIds.addAll(activePlanIds);

        deleteMaterialUsage(delPlanIds);
        baseSaveBatch(allUsages);
    }

    @Override
    public List<PlanMaterialUsageProjection> getPlanMaterialUsagesByOrderNos(Collection<String> orderNos, Collection<Integer> statusList) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return Collections.emptyList();
        }
        return getBaseMapper().getPlanMaterialUsagesByOrderNos(orderNos, statusList);
    }

    @Override
    public List<TabCountDto> getMaterialPurchaseOrderUsageConfirmTabCount(MaterialPurchaseOrderUsageConfirmTabCountRequest request) {
        return getBaseMapper().getMaterialPurchaseOrderUsageConfirmTabCount(request);
    }

    @Override
    public List<PurchasePlanMaterialUsageEntity> getByColorCardId(Integer colorCardId) {
        if (Objects.isNull(colorCardId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<PurchasePlanMaterialUsageEntity>()
                .eq(PurchasePlanMaterialUsageEntity::getColorCardId, colorCardId));
    }

    @Override
    public List<PurchasePlanMaterialUsageEntity> getByColorCardIdOrOriginColorCardId(Integer colorCardId) {
        return list(new LambdaQueryWrapper<PurchasePlanMaterialUsageEntity>()
                .and(t -> t.eq(PurchasePlanMaterialUsageEntity::getColorCardId, colorCardId)
                        .or()
                        .eq(PurchasePlanMaterialUsageEntity::getOriginColorCardId, colorCardId)));
    }

    @Override
    public List<PurchasePlanMaterialUsageDTO> getNoMaterialOrderByColorCardId(Integer colorCardId) {
        if (Objects.isNull(colorCardId)) {
            return Collections.emptyList();
        }
        return getBaseMapper().getNoMaterialOrderByColorCardId(colorCardId, PurchasePlanStatusEnum.NO_MATERIAL_ORDER_STATUS_VALUE_LIST);
    }

    @Override
    public List<PurchasePlanMaterialUsageDTO> getByMaterialOrderItemIds(Collection<Integer> materialOrderItemIds) {
        return getBaseMapper().getByMaterialOrderItemIds(materialOrderItemIds);
    }

    @Override
    public List<PurchasePlanMaterialUsageEntity> listByPlanIds(Set<Integer> planIds) {
        if (Objects.isNull(planIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<PurchasePlanMaterialUsageEntity>()
                .in(PurchasePlanMaterialUsageEntity::getPlanId, planIds));
    }
}
