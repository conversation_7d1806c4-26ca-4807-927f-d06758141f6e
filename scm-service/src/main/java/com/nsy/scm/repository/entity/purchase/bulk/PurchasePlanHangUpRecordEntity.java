package com.nsy.scm.repository.entity.purchase.bulk;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <p>
 * 大货计划单挂起记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@Entity
@Table(name = "purchase_plan_hang_up_record")
@TableName("purchase_plan_hang_up_record")
public class PurchasePlanHangUpRecordEntity extends BaseMpEntity {


    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer planHangUpRecordId;

    /**
     * skc
     */
    private String skc;
    /**
     * sku
     */
    private String sku;
    /**
     * sku
     */
    private Integer supplierId;

    /**
     * 是否挂起（1：是，0：否）
     */
    private Integer isHangUp;

    /**
     * 枚举类型：操作类型 HANG_UP-挂起 HANG_TO_FOLLOW_UP-挂起跟进 MANUAL_RELEASE-手动解挂 ORDER_RELEASE-下单解挂
     */
    private String operationType;

    /**
     * 处理时长（分钟）
     */
    private BigDecimal disposeTime;

    /**
     * 处理部门
     */
    private Integer handleDepartmentId;

    /**
     * 责任部门（bd_drop_down_data_source_config表:purchase_order_hang_up_duty_department）
     */
    private String dutyDepartment;

    private Integer dutyDepartmentId;

    /**
     * 责任人ID
     */
    private Integer dutyUserId;

    /**
     * 责任人名称
     */
    private String dutyUserName;

    /**
     * 主要原因:数据字典：scm_order_plan_hang_up_type
     */
    private String planHangUpType;

    /**
     * 内容
     */
    private String content;

    /**
     * 区域
     */
    private String location;


    /**
     * 一级部门
     */
    private Integer firstDepartmentId;

    /**
     * 一级部门名称
     */
    private String firstDepartmentName;

    /**
     * 批次号
     */
    private String batchNumber;

    public Integer getPlanHangUpRecordId() {
        return planHangUpRecordId;
    }

    public void setPlanHangUpRecordId(Integer planHangUpRecordId) {
        this.planHangUpRecordId = planHangUpRecordId;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getIsHangUp() {
        return isHangUp;
    }

    public void setIsHangUp(Integer isHangUp) {
        this.isHangUp = isHangUp;
    }

    public Integer getHandleDepartmentId() {
        return handleDepartmentId;
    }

    public void setHandleDepartmentId(Integer handleDepartmentId) {
        this.handleDepartmentId = handleDepartmentId;
    }

    public String getPlanHangUpType() {
        return planHangUpType;
    }

    public void setPlanHangUpType(String planHangUpType) {
        this.planHangUpType = planHangUpType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getDutyDepartment() {
        return dutyDepartment;
    }

    public void setDutyDepartment(String dutyDepartment) {
        this.dutyDepartment = dutyDepartment;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public BigDecimal getDisposeTime() {
        return disposeTime;
    }

    public void setDisposeTime(BigDecimal disposeTime) {
        this.disposeTime = disposeTime;
    }

    public String fetchBusinessKey() {
        return String.format("%s_%s", sku, supplierId);
    }

    public String fetchGroupKey() {
        return String.format("%s_%s", skc, supplierId);
    }

    public Integer getDutyUserId() {
        return dutyUserId;
    }

    public void setDutyUserId(Integer dutyUserId) {
        this.dutyUserId = dutyUserId;
    }

    public String getDutyUserName() {
        return dutyUserName;
    }

    public void setDutyUserName(String dutyUserName) {
        this.dutyUserName = dutyUserName;
    }

    public Integer getFirstDepartmentId() {
        return firstDepartmentId;
    }

    public void setFirstDepartmentId(Integer firstDepartmentId) {
        this.firstDepartmentId = firstDepartmentId;
    }

    public String getFirstDepartmentName() {
        return firstDepartmentName;
    }

    public void setFirstDepartmentName(String firstDepartmentName) {
        this.firstDepartmentName = firstDepartmentName;
    }

    public Integer getDutyDepartmentId() {
        return dutyDepartmentId;
    }

    public void setDutyDepartmentId(Integer dutyDepartmentId) {
        this.dutyDepartmentId = dutyDepartmentId;
    }

    public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }
}
