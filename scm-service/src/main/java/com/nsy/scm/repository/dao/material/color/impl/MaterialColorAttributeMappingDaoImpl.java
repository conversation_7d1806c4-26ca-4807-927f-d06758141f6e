package com.nsy.scm.repository.dao.material.color.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.scm.repository.dao.material.color.MaterialColorAttributeMappingDao;
import com.nsy.scm.repository.entity.MaterialColorAttributeMappingEntity;
import com.nsy.scm.repository.sql.mapper.material.color.MaterialColorAttributeMappingMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <h3>公司面料颜色属性映射表 持久层实现类</h3>
 *
 * <AUTHOR>
 * @since 2024/05/09 17:47
 */
@Repository
public class MaterialColorAttributeMappingDaoImpl extends ServiceImpl<MaterialColorAttributeMappingMapper, MaterialColorAttributeMappingEntity> implements MaterialColorAttributeMappingDao {

    @Override
    public List<MaterialColorAttributeMappingEntity> listByMaterialIdAndKey(Integer materialId, String attributeMappingKey) {
        return list(new LambdaQueryWrapper<MaterialColorAttributeMappingEntity>()
                .eq(MaterialColorAttributeMappingEntity::getMaterialId, materialId)
                .eq(MaterialColorAttributeMappingEntity::getAttributeMappingKey, attributeMappingKey));
    }

    @Override
    public List<MaterialColorAttributeMappingEntity> listByMaterialIdsAndKey(Collection<Integer> materialIds, String attributeMappingKey) {
        if (CollectionUtils.isEmpty(materialIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<MaterialColorAttributeMappingEntity>()
                .in(MaterialColorAttributeMappingEntity::getMaterialId, materialIds)
                .eq(MaterialColorAttributeMappingEntity::getAttributeMappingKey, attributeMappingKey));
    }

    @Override
    public List<MaterialColorAttributeMappingEntity> listByMaterialColorIdsAndKey(Collection<Integer> materialColorIds, String attributeMappingKey) {
        if (CollectionUtils.isEmpty(materialColorIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<MaterialColorAttributeMappingEntity>()
                .in(MaterialColorAttributeMappingEntity::getMaterialColorId, materialColorIds)
                .eq(MaterialColorAttributeMappingEntity::getAttributeMappingKey, attributeMappingKey));
    }

    @Override
    public Map<Integer, MaterialColorAttributeMappingEntity> mapUniqueAttributeMapByIdsAndType(Collection<Integer> materialColorIds, String attributeMappingKey) {
        if (CollectionUtils.isEmpty(materialColorIds)) {
            return Collections.emptyMap();
        }
        return list(new LambdaQueryWrapper<MaterialColorAttributeMappingEntity>()
                .in(MaterialColorAttributeMappingEntity::getMaterialColorId, materialColorIds)
                .eq(MaterialColorAttributeMappingEntity::getAttributeMappingKey, attributeMappingKey))
                .stream().collect(Collectors.toMap(MaterialColorAttributeMappingEntity::getMaterialColorId, Function.identity(), (k1, k2) -> k1));
    }
}
