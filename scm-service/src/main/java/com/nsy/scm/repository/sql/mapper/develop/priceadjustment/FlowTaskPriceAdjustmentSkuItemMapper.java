package com.nsy.scm.repository.sql.mapper.develop.priceadjustment;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.scm.business.request.develop.PriceAdjustmentSkuRemarkPageRequest;
import com.nsy.scm.business.response.develop.PriceAdjustmentSkuRemarkResponse;
import com.nsy.scm.repository.entity.develop.priceadjustment.FlowTaskPriceAdjustmentSkuItemEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 供应商调价sku表 的数据库操作Mapper
 *
 * <AUTHOR>
 * @since 2023-12-29
 */
public interface FlowTaskPriceAdjustmentSkuItemMapper extends BaseMapper<FlowTaskPriceAdjustmentSkuItemEntity> {

    IPage<PriceAdjustmentSkuRemarkResponse> pageQuery(IPage page, @Param("request") PriceAdjustmentSkuRemarkPageRequest request);

}
