package com.nsy.scm.repository.dao.config.impl;

import com.nsy.scm.repository.entity.config.BdLocationConfigEntity;
import com.nsy.scm.repository.sql.mapper.config.BdLocationConfigMapper;
import com.nsy.scm.repository.dao.config.BdLocationConfigDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 针对表【供应链-区域配置表】的数据库操作Dao实现
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
public class BdBdLocationConfigDaoImpl extends ServiceImpl<BdLocationConfigMapper, BdLocationConfigEntity> implements BdLocationConfigDao {

}
