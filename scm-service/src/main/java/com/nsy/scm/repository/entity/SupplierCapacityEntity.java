package com.nsy.scm.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import java.math.BigDecimal;

/**
 * 供应商产能
 *
 * @TableName supplier_capacity
 */
@TableName("supplier_capacity")
public class SupplierCapacityEntity extends BaseMpEntity {
    /**
     * 主键
     */
    @TableId(value = "supplier_capacity_id", type = IdType.AUTO)
    private Integer supplierCapacityId;

    /**
     * 供应商Id
     */
    @TableField("supplier_id")
    private Integer supplierId;

    /**
     * 状态:1-
     */
    @TableField("status")
    private Integer status;

    /**
     * 产能月份
     */
    @TableField("capacity_month")
    private String capacityMonth;

    /**
     * 时颖单量占比
     */
    @TableField("shiying_order_qty_proportion")
    private BigDecimal shiyingOrderQtyProportion;

    /**
     * 月产能
     */
    @TableField("month_capacity")
    private BigDecimal monthCapacity;

    /**
     * 周产能
     */
    @TableField("week_capacity")
    private BigDecimal weekCapacity;

    /**
     * 外发占比
     */
    @TableField("out_order_qty_proportion")
    private BigDecimal outOrderQtyProportion;

    /**
     * 平均周期
     */
    @TableField("average_period")
    private BigDecimal averagePeriod;

    /**
     * 平均准交率
     */
    @TableField("average_punctuality_deliver_rate")
    private BigDecimal averagePunctualityDeliverRate;

    /**
     * 评估备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 主键
     */
    public Integer getSupplierCapacityId() {
        return supplierCapacityId;
    }

    /**
     * 主键
     */
    public void setSupplierCapacityId(Integer supplierCapacityId) {
        this.supplierCapacityId = supplierCapacityId;
    }

    /**
     * 供应商Id
     */
    public Integer getSupplierId() {
        return supplierId;
    }

    /**
     * 供应商Id
     */
    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    /**
     * 状态:1-
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 状态:1-
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 产能月份
     */
    public String getCapacityMonth() {
        return capacityMonth;
    }

    /**
     * 产能月份
     */
    public void setCapacityMonth(String capacityMonth) {
        this.capacityMonth = capacityMonth;
    }

    /**
     * 时颖单量占比
     */
    public BigDecimal getShiyingOrderQtyProportion() {
        return shiyingOrderQtyProportion;
    }

    /**
     * 时颖单量占比
     */
    public void setShiyingOrderQtyProportion(BigDecimal shiyingOrderQtyProportion) {
        this.shiyingOrderQtyProportion = shiyingOrderQtyProportion;
    }

    /**
     * 月产能
     */
    public BigDecimal getMonthCapacity() {
        return monthCapacity;
    }

    /**
     * 月产能
     */
    public void setMonthCapacity(BigDecimal monthCapacity) {
        this.monthCapacity = monthCapacity;
    }

    /**
     * 周产能
     */
    public BigDecimal getWeekCapacity() {
        return weekCapacity;
    }

    /**
     * 周产能
     */
    public void setWeekCapacity(BigDecimal weekCapacity) {
        this.weekCapacity = weekCapacity;
    }

    /**
     * 外发占比
     */
    public BigDecimal getOutOrderQtyProportion() {
        return outOrderQtyProportion;
    }

    /**
     * 外发占比
     */
    public void setOutOrderQtyProportion(BigDecimal outOrderQtyProportion) {
        this.outOrderQtyProportion = outOrderQtyProportion;
    }

    /**
     * 平均周期
     */
    public BigDecimal getAveragePeriod() {
        return averagePeriod;
    }

    /**
     * 平均周期
     */
    public void setAveragePeriod(BigDecimal averagePeriod) {
        this.averagePeriod = averagePeriod;
    }

    /**
     * 平均准交率
     */
    public BigDecimal getAveragePunctualityDeliverRate() {
        return averagePunctualityDeliverRate;
    }

    /**
     * 平均准交率
     */
    public void setAveragePunctualityDeliverRate(BigDecimal averagePunctualityDeliverRate) {
        this.averagePunctualityDeliverRate = averagePunctualityDeliverRate;
    }

    /**
     * 评估备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 评估备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }
}