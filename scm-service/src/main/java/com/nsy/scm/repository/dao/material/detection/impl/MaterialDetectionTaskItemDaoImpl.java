package com.nsy.scm.repository.dao.material.detection.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.scm.dto.request.material.detection.MaterialDetectionItemCountRequest;
import com.nsy.api.scm.dto.request.material.detection.MaterialDetectionItemPageRequest;
import com.nsy.api.scm.dto.response.common.StatusTabResponse;
import com.nsy.api.scm.dto.response.material.detection.MaterialDetectionItemDto;
import com.nsy.api.scm.dto.response.material.detection.MaterialDetectionItemProjectDto;
import com.nsy.api.scm.dto.response.material.detection.MaterialDetectionTaskItemSkcDto;
import com.nsy.scm.repository.dao.material.detection.MaterialDetectionTaskItemDao;
import com.nsy.scm.repository.entity.material.detection.MaterialDetectionTaskItemEntity;
import com.nsy.scm.repository.sql.mapper.material.detection.MaterialDetectionTaskItemMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 面料接单任务流程 DAO 实现类
 *
 * <AUTHOR>
 * @date 2024/06/05 18:04
 */
@Repository
public class MaterialDetectionTaskItemDaoImpl extends ServiceImpl<MaterialDetectionTaskItemMapper, MaterialDetectionTaskItemEntity> implements MaterialDetectionTaskItemDao {

    @Override
    public IPage<MaterialDetectionItemDto> page(IPage page, MaterialDetectionItemPageRequest query) {
        return this.baseMapper.page(page, query);
    }

    @Override
    public IPage<MaterialDetectionItemProjectDto> pageForDownLoad(Page page, MaterialDetectionItemPageRequest request) {
        return this.baseMapper.pageForDownLoad(page, request);
    }

    @Override
    public List<MaterialDetectionTaskItemEntity> getListByTaskId(Integer taskId) {
        LambdaQueryWrapper<MaterialDetectionTaskItemEntity> queryWrapper = new LambdaQueryWrapper<MaterialDetectionTaskItemEntity>()
                .eq(MaterialDetectionTaskItemEntity::getTaskId, taskId);
        return this.list(queryWrapper);
    }

    @Override
    public List<StatusTabResponse> tabCount(MaterialDetectionItemCountRequest request) {
        return this.baseMapper.tabCount(request);
    }

    @Override
    public List<MaterialDetectionTaskItemEntity> getListByTaskIds(List<Integer> taskIds) {
        LambdaQueryWrapper<MaterialDetectionTaskItemEntity> queryWrapper = new LambdaQueryWrapper<MaterialDetectionTaskItemEntity>()
                .in(MaterialDetectionTaskItemEntity::getTaskId, taskIds);
        return this.list(queryWrapper);
    }

    @Override
    public List<MaterialDetectionTaskItemSkcDto> getBySkcAndColorCardIds(List<Integer> colorCardIds, List<String> skcList) {
        if (CollectionUtils.isEmpty(colorCardIds) || CollectionUtils.isEmpty(skcList)) {
            return Collections.emptyList();
        }
        return this.baseMapper.getBySkcAndColorCardIds(colorCardIds, skcList);
    }
}
