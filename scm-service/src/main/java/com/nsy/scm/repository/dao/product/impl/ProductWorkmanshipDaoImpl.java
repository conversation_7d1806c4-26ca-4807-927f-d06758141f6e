package com.nsy.scm.repository.dao.product.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.base.UserInfo;
import com.nsy.api.scm.dto.request.workmanship.SpuWorkmanshipPageRequest;
import com.nsy.scm.business.domain.product.workmanship.ProductWorkmanship;
import com.nsy.scm.constant.MybatisQueryConstant;
import com.nsy.scm.repository.dao.product.ProductWorkmanshipDao;
import com.nsy.scm.repository.entity.base.BaseMpEntity;
import com.nsy.scm.repository.entity.product.ProductWorkmanshipEntity;
import com.nsy.scm.repository.sql.mapper.product.ProductWorkmanshipMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 商品工艺库信息 dao实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-31
 */
@Repository
public class ProductWorkmanshipDaoImpl extends ServiceImpl<ProductWorkmanshipMapper, ProductWorkmanshipEntity> implements ProductWorkmanshipDao {

    @Override
    public ProductWorkmanshipEntity getProductWorkmanship(Integer productId) {
        if (Objects.isNull(productId)) {
            return null;
        }

        return getBaseMapper().selectOne(new LambdaQueryWrapper<ProductWorkmanshipEntity>()
                .eq(ProductWorkmanshipEntity::getProductId, productId)
                .last(MybatisQueryConstant.QUERY_FIRST));
    }

    @Override
    public List<ProductWorkmanshipEntity> batchGetProductWorkmanship(Collection<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        return getBaseMapper().selectList(new LambdaQueryWrapper<ProductWorkmanshipEntity>()
                .in(ProductWorkmanshipEntity::getProductId, productIds));
    }

    @Override
    public List<ProductWorkmanshipEntity> batchGetProductWorkmanshipBySpus(Collection<String> spus) {
        if (CollectionUtils.isEmpty(spus)) {
            return Collections.emptyList();
        }
        return getBaseMapper().selectList(new LambdaQueryWrapper<ProductWorkmanshipEntity>()
                .in(ProductWorkmanshipEntity::getSpu, spus));
    }

    @Override
    public void updateWorkmanshipVersionInfo(ProductWorkmanship productWorkmanship, Integer isStructured, UserInfo operateUserInfo) {
        lambdaUpdate()
                .set(ProductWorkmanshipEntity::getLatestVersionId, productWorkmanship.getLatestVersionId())
                .set(ProductWorkmanshipEntity::getLatestVersionNo, productWorkmanship.getLatestVersionNo())
                .set(ProductWorkmanshipEntity::getIsStructured, isStructured)
                .set(BaseMpEntity::getUpdateBy, operateUserInfo.getUserName())
                .eq(ProductWorkmanshipEntity::getWorkmanshipId, productWorkmanship.getProductWorkmanshipId())
                .update();
    }

    @Override
    public Integer getWorkmanshipIdByProductId(Integer productId) {
        ProductWorkmanshipEntity workmanshipEntity = getBaseMapper().selectOne(new LambdaQueryWrapper<ProductWorkmanshipEntity>()
                .select(ProductWorkmanshipEntity::getWorkmanshipId) // 只查主键id
                .eq(ProductWorkmanshipEntity::getProductId, productId)
                .last(MybatisQueryConstant.QUERY_FIRST));

        return Optional.ofNullable(workmanshipEntity)
                .map(ProductWorkmanshipEntity::getWorkmanshipId)
                .orElse(null);
    }

    @Override
    public void deleteById(Integer workmanshipId) {
        getBaseMapper().deleteById(workmanshipId);
    }

    @Override
    public IPage<Integer> getHasWorkmanshipVersionProductIds(SpuWorkmanshipPageRequest request) {
        return this.baseMapper.getHasWorkmanshipVersionProductIds(new Page<>(request.getPageIndex(), request.getPageSize()), request);
    }
}
