package com.nsy.scm.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <p>
 * 采购合同表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Entity
@Table(name = "purchase_contract")
@TableName("purchase_contract")
public class PurchaseContractEntity extends BaseMpEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer contractId;

    /**
     * 合同配置表ID
     */
    private Integer contractConfigId;

    /**
     * 采购单ID
     */
    private Integer orderId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 下载地址
     */
    private String url;


    /**
     * 是否删除
     */
    private Integer isDeleted;

    public Integer getContractId() {
        return contractId;
    }

    public void setContractId(Integer contractId) {
        this.contractId = contractId;
    }

    public Integer getContractConfigId() {
        return contractConfigId;
    }

    public void setContractConfigId(Integer contractConfigId) {
        this.contractConfigId = contractConfigId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}
