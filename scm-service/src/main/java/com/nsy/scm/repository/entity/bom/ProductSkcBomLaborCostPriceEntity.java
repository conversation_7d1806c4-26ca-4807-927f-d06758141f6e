package com.nsy.scm.repository.entity.bom;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
 * skc人工费用报价表 Entity
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@TableName("product_skc_bom_labor_cost_price")
public class ProductSkcBomLaborCostPriceEntity extends BaseMpEntity {


    /**
    * skc人工费用报价id
    */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer productSkcBomLaborCostPriceId;

    /**
    * bom清单列表id
    */
    private Integer productBomId;

    /**
    * skc
    */
    private String skc;

    /**
    * 人工单价
    */
    private BigDecimal laborUnitPrice;

    /**
    * 人工倍率
    */
    private BigDecimal laborRatio;

    /**
    * 人工费用
    */
    private BigDecimal laborCost;

    /**
    * 人工核价
    */
    private BigDecimal checkLaborCost;

    /**
    * GST核价
    */
    private BigDecimal gstPrice;

    /**
    * 备注
    */
    private String remark;

    /**
    * 地区
    */
    private String location;


    public Integer getProductSkcBomLaborCostPriceId() {
        return productSkcBomLaborCostPriceId;
    }

    public void setProductSkcBomLaborCostPriceId(Integer productSkcBomLaborCostPriceId) {
        this.productSkcBomLaborCostPriceId = productSkcBomLaborCostPriceId;
    }

    public Integer getProductBomId() {
        return productBomId;
    }

    public void setProductBomId(Integer productBomId) {
        this.productBomId = productBomId;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public BigDecimal getLaborUnitPrice() {
        return laborUnitPrice;
    }

    public void setLaborUnitPrice(BigDecimal laborUnitPrice) {
        this.laborUnitPrice = laborUnitPrice;
    }

    public BigDecimal getLaborRatio() {
        return laborRatio;
    }

    public void setLaborRatio(BigDecimal laborRatio) {
        this.laborRatio = laborRatio;
    }

    public BigDecimal getLaborCost() {
        return laborCost;
    }

    public void setLaborCost(BigDecimal laborCost) {
        this.laborCost = laborCost;
    }

    public BigDecimal getCheckLaborCost() {
        return checkLaborCost;
    }

    public void setCheckLaborCost(BigDecimal checkLaborCost) {
        this.checkLaborCost = checkLaborCost;
    }

    public BigDecimal getGstPrice() {
        return gstPrice;
    }

    public void setGstPrice(BigDecimal gstPrice) {
        this.gstPrice = gstPrice;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }


}
