package com.nsy.scm.repository.entity.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 物料备料单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Entity
@Table(name = "material_preparation_order_item")
@TableName("material_preparation_order_item")
public class MaterialPreparationOrderItemEntity extends BaseMpEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer orderItemId;

    /**
     * 备料单id
     */
    private Integer orderId;

    /**
     * 明细状态 1-待提交审核，10-待审核，20-待接单，30-备料中，40-已拒单，90-已取消，99-已完成
     */
    private int status;

    /**
     * 供应商物料ID
     */
    private Integer materialSupplierInfoId;

    /**
     * 供应商物料名称
     */
    private String supplierMaterialName;

    /**
     * 色卡id
     */
    private Integer colorCardId;

    /**
     * 面料商自备库存量
     */
    private Integer supplierReserve;

    /**
     * 时颖储备库存量
     */
    private Integer reserveForNsy;

    /**
     * 物料单位
     */
    private String materialUnit;

    /**
     * 备料中总量
     */
    private Integer preparingQty;

    /**
     * 预测需求量(米/个/条)
     */
    private Integer forecastDemandQty;

    /**
     * 预测需求量（kg）
     */
    private BigDecimal forecastDemandWeight;

    /**
     * 色卡单价(元)下单快照
     */
    private BigDecimal unitPrice;

    /**
     * 备料总量(米/个/条)
     */
    private Integer prepareQty;

    /**
     * 备料总量单位
     */
    private String prepareQtyUnit;

    /**
     * 备料总量（kg）
     */
    private BigDecimal prepareWeight;

    /**
     * 实际备料总量(米/个/条)
     */
    private Integer actualPrepareQty;

    /**
     * 实际备料总量（kg）
     */
    private BigDecimal actualPrepareWeight;

    /**
     * 费用合计（元）
     */
    private BigDecimal subtotalFee;

    /**
     * 要求备料完成日期
     */
    private Date requireCompleteDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除：0-未删除，1-删除
     */
    private int isDelete;

    /**
     * 区域
     */
    private String location;

    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Integer getMaterialSupplierInfoId() {
        return materialSupplierInfoId;
    }

    public void setMaterialSupplierInfoId(Integer materialSupplierInfoId) {
        this.materialSupplierInfoId = materialSupplierInfoId;
    }

    public String getSupplierMaterialName() {
        return supplierMaterialName;
    }

    public void setSupplierMaterialName(String supplierMaterialName) {
        this.supplierMaterialName = supplierMaterialName;
    }

    public Integer getColorCardId() {
        return colorCardId;
    }

    public void setColorCardId(Integer colorCardId) {
        this.colorCardId = colorCardId;
    }

    public Integer getSupplierReserve() {
        return supplierReserve;
    }

    public void setSupplierReserve(Integer supplierReserve) {
        this.supplierReserve = supplierReserve;
    }

    public Integer getReserveForNsy() {
        return reserveForNsy;
    }

    public void setReserveForNsy(Integer reserveForNsy) {
        this.reserveForNsy = reserveForNsy;
    }

    public String getMaterialUnit() {
        return materialUnit;
    }

    public void setMaterialUnit(String materialUnit) {
        this.materialUnit = materialUnit;
    }

    public Integer getPreparingQty() {
        return preparingQty;
    }

    public void setPreparingQty(Integer preparingQty) {
        this.preparingQty = preparingQty;
    }

    public Integer getForecastDemandQty() {
        return forecastDemandQty;
    }

    public void setForecastDemandQty(Integer forecastDemandQty) {
        this.forecastDemandQty = forecastDemandQty;
    }

    public BigDecimal getForecastDemandWeight() {
        return forecastDemandWeight;
    }

    public void setForecastDemandWeight(BigDecimal forecastDemandWeight) {
        this.forecastDemandWeight = forecastDemandWeight;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Integer getPrepareQty() {
        return prepareQty;
    }

    public void setPrepareQty(Integer prepareQty) {
        this.prepareQty = prepareQty;
    }

    public String getPrepareQtyUnit() {
        return prepareQtyUnit;
    }

    public void setPrepareQtyUnit(String prepareQtyUnit) {
        this.prepareQtyUnit = prepareQtyUnit;
    }

    public BigDecimal getPrepareWeight() {
        return prepareWeight;
    }

    public void setPrepareWeight(BigDecimal prepareWeight) {
        this.prepareWeight = prepareWeight;
    }

    public Integer getActualPrepareQty() {
        return actualPrepareQty;
    }

    public void setActualPrepareQty(Integer actualPrepareQty) {
        this.actualPrepareQty = actualPrepareQty;
    }

    public BigDecimal getActualPrepareWeight() {
        return actualPrepareWeight;
    }

    public void setActualPrepareWeight(BigDecimal actualPrepareWeight) {
        this.actualPrepareWeight = actualPrepareWeight;
    }

    public BigDecimal getSubtotalFee() {
        return subtotalFee;
    }

    public void setSubtotalFee(BigDecimal subtotalFee) {
        this.subtotalFee = subtotalFee;
    }

    public Date getRequireCompleteDate() {
        return requireCompleteDate;
    }

    public void setRequireCompleteDate(Date requireCompleteDate) {
        this.requireCompleteDate = requireCompleteDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

}
