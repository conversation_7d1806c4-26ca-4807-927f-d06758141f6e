package com.nsy.scm.repository.entity.product;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <p>
 *  商品工艺子版本附件
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-31
 */
@Entity
@Table(name = "product_workmanship_child_version_attachment")
@TableName("product_workmanship_child_version_attachment")
public class ProductWorkmanshipChildVersionAttachmentEntity extends BaseMpEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer attachmentId;

    /**
     * 工艺子版本库主键id
     */
    private Integer childVersionId;

    /**
     * 附件类型
     */
    private String attachmentType;

    /**
     * 附件名称
     */
    private String originName;

    /**
     * oss文件名称
     */
    private String attachmentName;

    /**
     * 附件url
     */
    private String attachmentUrl;


    public Integer getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(Integer attachmentId) {
        this.attachmentId = attachmentId;
    }

    public Integer getChildVersionId() {
        return childVersionId;
    }

    public void setChildVersionId(Integer childVersionId) {
        this.childVersionId = childVersionId;
    }

    public String getAttachmentType() {
        return attachmentType;
    }

    public void setAttachmentType(String attachmentType) {
        this.attachmentType = attachmentType;
    }

    public String getOriginName() {
        return originName;
    }

    public void setOriginName(String originName) {
        this.originName = originName;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }


}
