package com.nsy.scm.repository.entity.ali;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <p>
 * 1688应用信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-11
 */
@Entity
@Table(name = "ali1688_app")
@TableName("ali1688_app")
public class Ali1688AppEntity extends BaseMpEntity {


    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer appId;

    /**
     * 名称
     */
    private String appName;

    /**
     * key
     */
    private String appKey;

    /**
     * 密钥
     */
    private String secKey;

    /**
     * 主账号token
     */
    private String mainAccessToken;

    /**
     * 是否启用:0-启用,1-禁用
     */
    private Integer isDeleted;

    /**
     * 区域
     */
    private String location;

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getAppId() {
        return appId;
    }

    public void setAppId(Integer appId) {
        this.appId = appId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getSecKey() {
        return secKey;
    }

    public void setSecKey(String secKey) {
        this.secKey = secKey;
    }

    public String getMainAccessToken() {
        return mainAccessToken;
    }

    public void setMainAccessToken(String mainAccessToken) {
        this.mainAccessToken = mainAccessToken;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }


}
