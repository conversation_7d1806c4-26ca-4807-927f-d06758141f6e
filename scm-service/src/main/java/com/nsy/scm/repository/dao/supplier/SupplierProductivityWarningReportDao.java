package com.nsy.scm.repository.dao.supplier;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.api.scm.dto.request.supplier.SupplierProductivityWarningReportPageRequest;
import com.nsy.api.scm.dto.response.supplier.SupplierProductivityWarningReportResponse;
import com.nsy.scm.repository.entity.supplier.productionscheduling.SupplierProductivityWarningReportEntity;

import java.util.Date;

/**
 * <p>
 * 供应商-产值预警日报 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
public interface SupplierProductivityWarningReportDao extends IService<SupplierProductivityWarningReportEntity> {

    IPage<SupplierProductivityWarningReportResponse> warningReportPage(SupplierProductivityWarningReportPageRequest request, Page page);

    Date warningReportLatestUpdateTime(SupplierProductivityWarningReportPageRequest request);

}
