package com.nsy.scm.repository.entity.preparematerial;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 工厂备料库存变更表
 * @TableName prepare_material_inventory_record
 */
@Entity
@Table(name = "material_prepare_material_inventory_record")
@TableName("material_prepare_material_inventory_record")
public class PrepareMaterialInventoryRecordEntity {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer prepareMaterialInventoryRecordId;

    /**
     * 备料id
     */
    private Integer prepareMaterialItemId;

    /**
     * 新库存
     */
    private BigDecimal newInventory;

    /**
     * 旧库存
     */
    private BigDecimal oldInventory;

    /**
     * 创建者
     */
    private String createBy;


    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 地区
     */
    private String location;

    /**
     * 主键id
     */
    public Integer getPrepareMaterialInventoryRecordId() {
        return prepareMaterialInventoryRecordId;
    }

    /**
     * 主键id
     */
    public void setPrepareMaterialInventoryRecordId(Integer prepareMaterialInventoryRecordId) {
        this.prepareMaterialInventoryRecordId = prepareMaterialInventoryRecordId;
    }



    /**
     * 新库存
     */
    public BigDecimal getNewInventory() {
        return newInventory;
    }

    /**
     * 新库存
     */
    public void setNewInventory(BigDecimal newInventory) {
        this.newInventory = newInventory;
    }

    /**
     * 旧库存
     */
    public BigDecimal getOldInventory() {
        return oldInventory;
    }

    /**
     * 旧库存
     */
    public void setOldInventory(BigDecimal oldInventory) {
        this.oldInventory = oldInventory;
    }

    /**
     * 创建者
     */
    public String getCreateBy() {
        return createBy;
    }

    /**
     * 创建者
     */
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 更新时间
     */
    public Date getUpdateDate() {
        return updateDate;
    }

    /**
     * 更新时间
     */
    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    /**
     * 更新者
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     * 更新者
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 版本号
     */
    public Long getVersion() {
        return version;
    }

    /**
     * 版本号
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * 地区
     */
    public String getLocation() {
        return location;
    }

    /**
     * 地区
     */
    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getPrepareMaterialItemId() {
        return prepareMaterialItemId;
    }

    public void setPrepareMaterialItemId(Integer prepareMaterialItemId) {
        this.prepareMaterialItemId = prepareMaterialItemId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
}