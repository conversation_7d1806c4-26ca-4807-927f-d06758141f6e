package com.nsy.scm.repository.dao.material;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.scm.business.domain.material.MaterialPrepareSafetyStockProductRevisionDto;
import com.nsy.scm.repository.entity.material.MaterialPreparationOnlineImportEntity;
import com.nsy.scm.repository.entity.material.MaterialSafetyStockDetailEntity;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <h3>面料安全库存明细 DAO 接口</h3>
 *
 * <AUTHOR>
 * @date 2023/11/24 11:42
 */
public interface MaterialSafetyStockDetailDao extends IService<MaterialSafetyStockDetailEntity> {
    List<MaterialSafetyStockDetailEntity> listByMaterialPreparationOrderItemIdList(List<Integer> materialPreparationOrderItemIdList);

    /**
     * 根据滚动时间查询数据
     * @param rollingDeadline
     * @return
     */
    List<MaterialSafetyStockDetailEntity> listByGeRollingDeadline(Date rollingDeadline);

    /**
     * 查询未删除的库存信息
     * @param ids
     * @return
     */
    List<MaterialSafetyStockDetailEntity> listByIdsAndNotDelete(List<Integer> ids);

    /**
     * 根据色卡ID查询安全库存
     * @param colorCardIds
     * @return
     */
    List<MaterialSafetyStockDetailEntity> listByColorCardIds(Collection<Integer> colorCardIds);
    /**
     * 根据色卡ID查询安全库存
     * @param colorCardIds
     * @return
     */
    List<MaterialSafetyStockDetailEntity> listByColorCardIdsAndNotDelete(Collection<Integer> colorCardIds);
    /**
     * 通过色卡ID、部门、skc、模式查询库存信息
     *
     * @param entityList 线上备料集合
     */
    List<MaterialSafetyStockDetailEntity> listByColorCardIdAndBusinessTypeAndSkcAndMode(List<MaterialPreparationOnlineImportEntity> entityList);
    /**
     * 通过色卡ID、模式查询库存信息
     *
     * @param entityList 线上备料集合
     */
    List<MaterialSafetyStockDetailEntity> listByColorCardIdAndMode(List<MaterialPreparationOnlineImportEntity> entityList);

    /**
     * 通过数据来源模式查询list
     *
     * @param mode
     * @return
     */
    List<MaterialSafetyStockDetailEntity> listByMode(String mode);

    /**
     * 根据改版状态查询安全库存明细
     * @param taskStatus
     * @return
     */
    List<MaterialPrepareSafetyStockProductRevisionDto> listByProductRevisionTaskStatus(Collection<Integer> taskStatus);
}
