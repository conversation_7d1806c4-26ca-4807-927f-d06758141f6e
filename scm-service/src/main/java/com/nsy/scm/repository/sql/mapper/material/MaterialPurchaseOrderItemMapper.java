package com.nsy.scm.repository.sql.mapper.material;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.scm.dto.domain.material.MaterialPurchaseOrderDeliverySmsNotifyDTO;
import com.nsy.api.scm.dto.domain.material.MaterialPurchaseOrderItemDTO;
import com.nsy.api.scm.dto.domain.material.MaterialPurchaseOrderItemListByPurchaseOrderItemForSupplierDTO;
import com.nsy.api.scm.dto.domain.material.MaterialPurchaseOrderItemPageDTO;
import com.nsy.api.scm.dto.domain.material.MaterialPurchaseOrderItemPageStatisticsDTO;
import com.nsy.api.scm.dto.domain.material.MaterialPurchaseOrderItemReceivedPageDTO;
import com.nsy.api.scm.dto.domain.material.MaterialSupplierColorCardStockOrderDetailPageDTO;
import com.nsy.api.scm.dto.domain.material.PurchaseOrderItemPurchaseApplyDTO;
import com.nsy.api.scm.dto.domain.material.QueryByPurchaseOrderNoAndSkcDTO;
import com.nsy.api.scm.dto.domain.purchase.bulk.PurchaseOrderItemStatsByColorCardIdDto;
import com.nsy.api.scm.dto.domain.supplier.TabCountDto;
import com.nsy.api.scm.dto.request.material.MaterialPurchaseOrderItemListByPurchaseOrderItemForSupplierRequest;
import com.nsy.api.scm.dto.request.material.MaterialPurchaseOrderItemListRequest;
import com.nsy.api.scm.dto.request.material.MaterialPurchaseOrderItemPageRequest;
import com.nsy.api.scm.dto.request.material.MaterialPurchaseOrderItemSupplierPageRequest;
import com.nsy.api.scm.dto.request.material.MaterialSupplierColorCardStockOrderDetialPageRequest;
import com.nsy.api.scm.dto.request.material.PurchaseOrderItemTabRequest;
import com.nsy.api.scm.dto.response.material.MaterialPurchaseOrderItemByOrderItemIdsRes;
import com.nsy.api.scm.dto.response.material.TabInfoRes;
import com.nsy.permission.annatation.Permission;
import com.nsy.scm.business.request.material.PurchaseOrderItemStatsByColorCardIdRequest;
import com.nsy.scm.repository.entity.material.MaterialPurchaseOrderItemEntity;
import org.apache.ibatis.annotations.Param;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 面料采购订单详情Mapper
 *
 * <AUTHOR>
 * @since 2022-04-08 17:55
 */
public interface MaterialPurchaseOrderItemMapper extends BaseMapper<MaterialPurchaseOrderItemEntity> {
    @Permission
    IPage<MaterialPurchaseOrderItemPageDTO> iPage(Page page, @Param("request") MaterialPurchaseOrderItemPageRequest request);

    IPage<MaterialPurchaseOrderItemReceivedPageDTO> receivedPage(Page page, @Param("request") MaterialPurchaseOrderItemSupplierPageRequest request);

    List<MaterialPurchaseOrderItemReceivedPageDTO> receivedList(@Param("request") MaterialPurchaseOrderItemSupplierPageRequest request);

    IPage<MaterialPurchaseOrderItemDTO> pageQueryPurchaseOrderItem(Page page, @Param("request") MaterialPurchaseOrderItemPageRequest request);

    List<PurchaseOrderItemPurchaseApplyDTO> queryByPurchaseOrderInfoId(@Param("purchaseOrderInfoId") Integer purchaseOrderInfoId);

    List<TabInfoRes> queryPurchaseOrderTabInfo(@Param("supplierId") Integer supplierId);

    @Permission
    List<TabInfoRes> queryPurchaseOrderItemTabInfo(@Param("supplierId") Integer supplierId, @Param("supplyMode") Integer supplyMode);

    List<TabCountDto> queryPurchaseOrderItemTabListToGc(@Param("request") PurchaseOrderItemTabRequest request);

    List<TabInfoRes> queryPurchaseOrderItemReceiveAndAcceptanceCount(@Param("request") PurchaseOrderItemTabRequest request, Set<Integer> statusSet);

    List<PurchaseOrderItemStatsByColorCardIdDto> statsOrderInfoGroupByColorCardId(@Param("request") PurchaseOrderItemStatsByColorCardIdRequest ofFloralStock);

    IPage<MaterialSupplierColorCardStockOrderDetailPageDTO> floralStockOrderDetailPage(Page page, @Param("request") MaterialSupplierColorCardStockOrderDetialPageRequest request);

    List<MaterialPurchaseOrderItemByOrderItemIdsRes> queryPurchaseOrderItemByPurchaseOrderItemIds(@Param("purchaseOrderItemIds") Set<Integer> purchaseOrderItemIds);

    List<MaterialPurchaseOrderItemListByPurchaseOrderItemForSupplierDTO> listByPurchaseOrderItemForSupplier(@Param("request") MaterialPurchaseOrderItemListByPurchaseOrderItemForSupplierRequest request);

    /**
     * 采购延期申请查询领料单相关信息
     *
     * @param request
     * @return
     */
    List<MaterialPurchaseOrderItemListByPurchaseOrderItemForSupplierDTO> listByPurchaseOrderItemForPurchaseOrderAdjustmentRecordAdd(@Param("request") MaterialPurchaseOrderItemListByPurchaseOrderItemForSupplierRequest request);

    List<MaterialPurchaseOrderItemEntity> queryByBulkOrderItemIds(@Param("bulkOrderItemIds") Set<Integer> bulkOrderItemIds);

    /**
     * @param colorCardIds
     * @return
     */
    List<MaterialPurchaseOrderItemEntity> maxListByColorCardIdList(@Param("colorCardIds") Collection<Integer> colorCardIds, Collection<Integer> notInStatus);

    List<String> existOrderByByMaterialSupplierInfoId(@Param("materialSupplierInfoIdList") Collection<Integer> materialSupplierInfoIdList);

    List<QueryByPurchaseOrderNoAndSkcDTO> queryByPurchaseOrderNoAndSkc(@Param("orderNos") Set<String> orderNos, @Param("skcs") Set<String> skcs);

    Integer countByMaterialSupplierIdAndStatus(@Param("materialSupplierId") Integer materialSupplierId, @Param("status") Integer status);

    List<MaterialPurchaseOrderDeliverySmsNotifyDTO> getDeliveredSmsNotifyOrderItemList(@Param("statuses") Collection<Integer> statuses);

    List<MaterialPurchaseOrderItemDTO> listItem(@Param("request") MaterialPurchaseOrderItemListRequest request);

    /**
     * 领料单/ 领料收货 分页统计 - 采购单信息
     *
     * @param request
     * @return
     */
    MaterialPurchaseOrderItemPageStatisticsDTO receivedPageStatistics(@Param("request") MaterialPurchaseOrderItemSupplierPageRequest request);

    List<TabCountDto> queryPurchaseOrderItemTabListToHomeGc(@Param("request")PurchaseOrderItemTabRequest request);
}
