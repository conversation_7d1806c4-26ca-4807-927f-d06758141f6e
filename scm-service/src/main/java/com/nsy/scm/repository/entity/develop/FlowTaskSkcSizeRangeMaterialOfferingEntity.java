package com.nsy.scm.repository.entity.develop;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
 * 任务skc尺码段面料报价明细表 Entity
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@TableName("flow_task_skc_size_range_material_offering")
public class FlowTaskSkcSizeRangeMaterialOfferingEntity extends BaseMpEntity {


    /**
    * 核价skc尺码段面料报价id
    */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer flowTaskSkcSizeRangeMaterialOfferingId;

    /**
    * 任务id
    */
    private Integer taskId;

    /**
    * EDIT_BOM：核价
    */
    private String taskType;

    /**
     * 商品id
     */
    private Integer productId;

    /**
    * skc
    */
    private String skc;

    /**
    * skc面料报价明细id
    */
    private Integer flowTaskSkcMaterialOfferingId;

    /**
    * 尺码段id
    */
    private Integer sizeRangeId;

    /**
    * 原单价-不参与计算的
    */
    private BigDecimal originalUnitPrice;

    /**
    * 单价
    */
    private BigDecimal unitPrice;

    /**
    * 单件用量(米/条/个)
    */
    private BigDecimal amount;

    /**
    * 核价单件用量
    */
    private BigDecimal confirmAmount;

    /**
    * 损耗
    */
    private BigDecimal loss;

    /**
    * 核价损耗
    */
    private BigDecimal confirmLoss;

    /**
    * 物料总价
    */
    private BigDecimal materialCost;

    /**
    * 核价物料总价
    */
    private BigDecimal confirmMaterialCost;

    /**
    * 备注
    */
    private String remark;

    /**
    * 地区
    */
    private String location;


    public Integer getFlowTaskSkcSizeRangeMaterialOfferingId() {
        return flowTaskSkcSizeRangeMaterialOfferingId;
    }

    public void setFlowTaskSkcSizeRangeMaterialOfferingId(Integer flowTaskSkcSizeRangeMaterialOfferingId) {
        this.flowTaskSkcSizeRangeMaterialOfferingId = flowTaskSkcSizeRangeMaterialOfferingId;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public Integer getFlowTaskSkcMaterialOfferingId() {
        return flowTaskSkcMaterialOfferingId;
    }

    public void setFlowTaskSkcMaterialOfferingId(Integer flowTaskSkcMaterialOfferingId) {
        this.flowTaskSkcMaterialOfferingId = flowTaskSkcMaterialOfferingId;
    }

    public Integer getSizeRangeId() {
        return sizeRangeId;
    }

    public void setSizeRangeId(Integer sizeRangeId) {
        this.sizeRangeId = sizeRangeId;
    }

    public BigDecimal getOriginalUnitPrice() {
        return originalUnitPrice;
    }

    public void setOriginalUnitPrice(BigDecimal originalUnitPrice) {
        this.originalUnitPrice = originalUnitPrice;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getConfirmAmount() {
        return confirmAmount;
    }

    public void setConfirmAmount(BigDecimal confirmAmount) {
        this.confirmAmount = confirmAmount;
    }

    public BigDecimal getLoss() {
        return loss;
    }

    public void setLoss(BigDecimal loss) {
        this.loss = loss;
    }

    public BigDecimal getConfirmLoss() {
        return confirmLoss;
    }

    public void setConfirmLoss(BigDecimal confirmLoss) {
        this.confirmLoss = confirmLoss;
    }

    public BigDecimal getMaterialCost() {
        return materialCost;
    }

    public void setMaterialCost(BigDecimal materialCost) {
        this.materialCost = materialCost;
    }

    public BigDecimal getConfirmMaterialCost() {
        return confirmMaterialCost;
    }

    public void setConfirmMaterialCost(BigDecimal confirmMaterialCost) {
        this.confirmMaterialCost = confirmMaterialCost;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }


}
