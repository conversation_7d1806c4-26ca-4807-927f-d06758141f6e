package com.nsy.scm.repository.entity.develop;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 任务skc二次工艺表
 */
@Entity
@Table(name = "flow_task_skc_secondary_design")
@TableName("flow_task_skc_secondary_design")
public class FlowTaskSkcSecondaryDesignEntity extends BaseMpEntity {
    /**
     *
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer flowTaskSkcSecondaryDesignId;

    /**
     * 记录报核价任务或编辑bom任务的任务号
     */
    private Integer taskId;

    /**
     * EDIT_BOM编辑bom；OFFERING报价
     */
    private String taskType;

    /**
     * skc
     */
    private String skc;

    /**
     * 工艺类型id
     */
    private Integer technologyTypeId;

    /**
     * 工艺类型名称
     */
    private String technologyTypeName;

    /**
     * 二次工艺供应商
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer supplierId;

    /**
     * 二次工艺供应商名称
     */
    private String supplierName;

    /**
     * 价格
     */
    private BigDecimal unitPrice;

    /**
     * 价格核价
     */
    private BigDecimal confirmUnitPrice;

    /**
     * 单件用量(米/条/个)
     */
    private BigDecimal amount;

    /**
     * 核价单件用量
     */
    private BigDecimal confirmAmount;

    /**
     * 损耗
     */
    private BigDecimal loss;

    /**
     * 核价损耗
     */
    private BigDecimal confirmLoss;

    /**
     * 二次工艺总价，金额=单价*用量*损耗
     */
    private BigDecimal secondaryDesignCost;

    /**
     * 核价二次工艺总价
     */
    private BigDecimal confirmSecondaryDesignCost;

    /**
     * 报价备注
     */
    private String offeringRemark;

    /**
     * 异议备注
     */
    private String rejectRemark;

    /**
     * 核价备注
     */
    private String checkRemark;

    private String remark;

    /**
     * 地区
     */
    private String location;


    /**
     *
     */
    public Integer getFlowTaskSkcSecondaryDesignId() {
        return flowTaskSkcSecondaryDesignId;
    }

    /**
     *
     */
    public void setFlowTaskSkcSecondaryDesignId(Integer flowTaskSkcSecondaryDesignId) {
        this.flowTaskSkcSecondaryDesignId = flowTaskSkcSecondaryDesignId;
    }

    /**
     * 记录报核价任务或编辑bom任务的任务号
     */
    public Integer getTaskId() {
        return taskId;
    }

    /**
     * 记录报核价任务或编辑bom任务的任务号
     */
    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    /**
     * EDIT_BOM编辑bom；OFFERING报价
     */
    public String getTaskType() {
        return taskType;
    }

    /**
     * EDIT_BOM编辑bom；OFFERING报价
     */
    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    /**
     * skc
     */
    public String getSkc() {
        return skc;
    }

    /**
     * skc
     */
    public void setSkc(String skc) {
        this.skc = skc;
    }

    /**
     * 工艺类型id
     */
    public Integer getTechnologyTypeId() {
        return technologyTypeId;
    }

    /**
     * 工艺类型id
     */
    public void setTechnologyTypeId(Integer technologyTypeId) {
        this.technologyTypeId = technologyTypeId;
    }

    /**
     * 工艺类型名称
     */
    public String getTechnologyTypeName() {
        return technologyTypeName;
    }

    /**
     * 工艺类型名称
     */
    public void setTechnologyTypeName(String technologyTypeName) {
        this.technologyTypeName = technologyTypeName;
    }

    /**
     * 二次工艺供应商
     */
    public Integer getSupplierId() {
        return supplierId;
    }

    /**
     * 二次工艺供应商
     */
    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    /**
     * 二次工艺供应商名称
     */
    public String getSupplierName() {
        return supplierName;
    }

    /**
     * 二次工艺供应商名称
     */
    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    /**
     * 价格
     */
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    /**
     * 价格
     */
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getConfirmUnitPrice() {
        return confirmUnitPrice;
    }

    public void setConfirmUnitPrice(BigDecimal confirmUnitPrice) {
        this.confirmUnitPrice = confirmUnitPrice;
    }

    /**
     * 单件用量(米/条/个)
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 单件用量(米/条/个)
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 核价单件用量
     */
    public BigDecimal getConfirmAmount() {
        return confirmAmount;
    }

    /**
     * 核价单件用量
     */
    public void setConfirmAmount(BigDecimal confirmAmount) {
        this.confirmAmount = confirmAmount;
    }

    /**
     * 损耗
     */
    public BigDecimal getLoss() {
        return loss;
    }

    /**
     * 损耗
     */
    public void setLoss(BigDecimal loss) {
        this.loss = loss;
    }

    /**
     * 核价损耗
     */
    public BigDecimal getConfirmLoss() {
        return confirmLoss;
    }

    /**
     * 核价损耗
     */
    public void setConfirmLoss(BigDecimal confirmLoss) {
        this.confirmLoss = confirmLoss;
    }

    /**
     * 二次工艺总价，金额=单价*用量*损耗
     */
    public BigDecimal getSecondaryDesignCost() {
        return secondaryDesignCost;
    }

    /**
     * 二次工艺总价，金额=单价*用量*损耗
     */
    public void setSecondaryDesignCost(BigDecimal secondaryDesignCost) {
        this.secondaryDesignCost = secondaryDesignCost;
    }

    /**
     * 核价二次工艺总价
     */
    public BigDecimal getConfirmSecondaryDesignCost() {
        return confirmSecondaryDesignCost;
    }

    /**
     * 核价二次工艺总价
     */
    public void setConfirmSecondaryDesignCost(BigDecimal confirmSecondaryDesignCost) {
        this.confirmSecondaryDesignCost = confirmSecondaryDesignCost;
    }

    /**
     * 报价备注
     */
    public String getOfferingRemark() {
        return offeringRemark;
    }

    /**
     * 报价备注
     */
    public void setOfferingRemark(String offeringRemark) {
        this.offeringRemark = offeringRemark;
    }

    /**
     * 异议备注
     */
    public String getRejectRemark() {
        return rejectRemark;
    }

    /**
     * 异议备注
     */
    public void setRejectRemark(String rejectRemark) {
        this.rejectRemark = rejectRemark;
    }

    /**
     * 核价备注
     */
    public String getCheckRemark() {
        return checkRemark;
    }

    /**
     * 核价备注
     */
    public void setCheckRemark(String checkRemark) {
        this.checkRemark = checkRemark;
    }

    /**
     * 地区
     */
    public String getLocation() {
        return location;
    }

    /**
     * 地区
     */
    public void setLocation(String location) {
        this.location = location;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}