package com.nsy.scm.repository.dao.purchase.impl;

import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.scm.dto.domain.CheckResultResponse;
import com.nsy.api.scm.dto.domain.purchase.bulk.PurchasePlanActiveCheckLogDto;
import com.nsy.scm.constant.NumberConstant;
import com.nsy.scm.constant.StringConstant;
import com.nsy.scm.enumstable.price.adjustment.FlowTaskPriceAdjustmentApplyStatusEnum;
import com.nsy.scm.enumstable.price.editbom.FlowTaskEditBomStatusEnum;
import com.nsy.scm.repository.entity.develop.FlowTaskEditBomEntity;
import com.nsy.scm.repository.entity.develop.priceadjustment.FlowTaskPriceAdjustmentEntity;
import com.nsy.scm.repository.entity.purchase.bulk.PurchasePlanActiveLogEntity;
import com.nsy.scm.repository.entity.purchase.bulk.PurchasePlanEntity;
import com.nsy.scm.repository.sql.mapper.purchase.bulk.PurchasePlanActiveLogMapper;
import com.nsy.scm.repository.dao.purchase.PurchasePlanActiveLogDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.scm.utils.mp.TenantContext;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 针对表【大货采购下单日志表】的数据库操作Dao实现
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Service
public class PurchasePlanActiveLogDaoImpl extends ServiceImpl<PurchasePlanActiveLogMapper, PurchasePlanActiveLogEntity> implements PurchasePlanActiveLogDao {


    @Override
    public PurchasePlanActiveCheckLogDto buildEntityByEditBom(Integer logType, PurchasePlanEntity plan, FlowTaskEditBomEntity editBom, CheckResultResponse result, String content, boolean isCreateTask) {
        PurchasePlanActiveCheckLogDto dto = new PurchasePlanActiveCheckLogDto();
        dto.setPlanId(plan.getPlanId());
        dto.setSkc(plan.getSkc());
        dto.setOrderType(result.getOrderType());
        dto.setLogType(logType);
        dto.setPriceNoticeType(plan.getActivePriceNotice());
        dto.setTaskType(NumberConstant.ONE);
        if (Objects.isNull(editBom)) {
            dto.setContent(String.format("%s，%s", "不存在核价任务", content));
            dto.setSonType(NumberConstant.ONE);
            return dto;
        }
        if (FlowTaskEditBomStatusEnum.getUnCompletedStatusList().contains(editBom.getStatus())) {
            dto.setSonType(NumberConstant.TWO);
            dto.setContent(String.format("%s%s，%s", isCreateTask ? "触发" : "存在", "进行中核价任务", content));
        } else {
            dto.setSonType(NumberConstant.THREE);
            dto.setContent(String.format("%s%s，%s", isCreateTask ? "触发" : "存在", "已完成核价任务", content));
        }
        dto.setTaskId(editBom.getFlowTaskEditBomId());
        dto.setTaskStatus(editBom.getStatus());
        return dto;
    }

    @Override
    public PurchasePlanActiveCheckLogDto buildEntityByPurchasePrice(Integer logType, PurchasePlanEntity plan, FlowTaskPriceAdjustmentEntity priceAdjustment, CheckResultResponse result, String content) {
        PurchasePlanActiveCheckLogDto dto = new PurchasePlanActiveCheckLogDto();
        dto.setPlanId(plan.getPlanId());
        dto.setSkc(plan.getSkc());
        dto.setOrderType(result.getOrderType());
        dto.setLogType(logType);
        dto.setPriceNoticeType(plan.getActivePriceNotice());
        dto.setTaskType(NumberConstant.ONE);
        dto.setContent(content);
        if (Objects.isNull(priceAdjustment)) {
            dto.setSonType(NumberConstant.ONE);
            return dto;
        }
        if (!FlowTaskPriceAdjustmentApplyStatusEnum.endStatusList().contains(priceAdjustment.getApplyStatus())) {
            dto.setSonType(NumberConstant.TWO);
        } else {
            dto.setSonType(NumberConstant.THREE);
        }
        dto.setTaskId(priceAdjustment.getFlowTaskPriceAdjustmentId());
        dto.setTaskStatus(priceAdjustment.getApplyStatus());
        return dto;
    }

    @Override
    public void saveActiveLogEntity(List<PurchasePlanActiveCheckLogDto> logDtoList) {
        if (CollectionUtils.isEmpty(logDtoList)) {
            return;
        }
        // 同一批次
        String batchNo = UUID.randomUUID().toString().replace(StringConstant.HORIZONTAL_BAR_STR, StringConstant.EMPTY_STRING).toLowerCase(Locale.US);
        List<PurchasePlanActiveLogEntity> addList = logDtoList.stream().map(dto -> {
            PurchasePlanActiveLogEntity entity = new PurchasePlanActiveLogEntity();
            BeanUtilsEx.copyProperties(dto, entity);
            entity.setBatchNo(batchNo);
            entity.setLocation(TenantContext.getTenant());
            return entity;
        }).collect(Collectors.toList());
        saveBatch(addList);
    }
}
