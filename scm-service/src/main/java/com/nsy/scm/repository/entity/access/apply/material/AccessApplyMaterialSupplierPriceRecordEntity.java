package com.nsy.scm.repository.entity.access.apply.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <p>
 * 准入申请物料供应商信息价格记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Entity
@Table(name = "access_apply_material_supplier_price_record")
@TableName("access_apply_material_supplier_price_record")
public class AccessApplyMaterialSupplierPriceRecordEntity extends BaseMpEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer priceRecordId;

    /**
     * 准入申请物料供应商信息ID
     */
    private Integer materialSupplierInfoId;

    /**
     * 变动原因，数据字典
     */
    private String changeReason;

    /**
     * 变动记录
     */
    private String changeRecord;

    /**
     * 其他说明
     */
    private String otherExplain;


    public Integer getPriceRecordId() {
        return priceRecordId;
    }

    public void setPriceRecordId(Integer priceRecordId) {
        this.priceRecordId = priceRecordId;
    }

    public Integer getMaterialSupplierInfoId() {
        return materialSupplierInfoId;
    }

    public void setMaterialSupplierInfoId(Integer materialSupplierInfoId) {
        this.materialSupplierInfoId = materialSupplierInfoId;
    }

    public String getChangeReason() {
        return changeReason;
    }

    public void setChangeReason(String changeReason) {
        this.changeReason = changeReason;
    }

    public String getChangeRecord() {
        return changeRecord;
    }

    public void setChangeRecord(String changeRecord) {
        this.changeRecord = changeRecord;
    }

    public String getOtherExplain() {
        return otherExplain;
    }

    public void setOtherExplain(String otherExplain) {
        this.otherExplain = otherExplain;
    }


}
