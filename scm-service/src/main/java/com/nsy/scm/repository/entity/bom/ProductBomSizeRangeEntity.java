package com.nsy.scm.repository.entity.bom;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
 * BOM清单尺码段表 Entity
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@TableName("product_bom_size_range")
public class ProductBomSizeRangeEntity extends BaseMpEntity {


    /**
    * 尺码段id
    */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer sizeRangeId;

    /**
    * bom清单id
    */
    private Integer productBomId;

    /**
     * 商品id
     */
    private Integer productId;

    /**
    * 尺码段
    */
    private Integer sizeRange;

    /**
    * 尺码段名称
    */
    private String sizeRangeName;

    /**
     * 备注
     */
    private String remark;

    /**
    * 地区
    */
    private String location;


    public Integer getSizeRangeId() {
        return sizeRangeId;
    }

    public void setSizeRangeId(Integer sizeRangeId) {
        this.sizeRangeId = sizeRangeId;
    }

    public Integer getProductBomId() {
        return productBomId;
    }

    public void setProductBomId(Integer productBomId) {
        this.productBomId = productBomId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getSizeRange() {
        return sizeRange;
    }

    public void setSizeRange(Integer sizeRange) {
        this.sizeRange = sizeRange;
    }

    public String getSizeRangeName() {
        return sizeRangeName;
    }

    public void setSizeRangeName(String sizeRangeName) {
        this.sizeRangeName = sizeRangeName;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }


    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
