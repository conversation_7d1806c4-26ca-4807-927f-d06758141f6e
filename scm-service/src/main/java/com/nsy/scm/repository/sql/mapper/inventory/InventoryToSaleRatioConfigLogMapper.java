package com.nsy.scm.repository.sql.mapper.inventory;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.scm.repository.entity.inventory.InventoryToSaleRatioConfigLogEntity;
import com.nsy.scm.business.request.inventory.InventoryToSaleLogPageRequest;
import com.nsy.scm.business.response.inventory.InventoryToSaleLogResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
/**
 * <AUTHOR>
 * @date 2022-06-29
 */
@Mapper
public interface InventoryToSaleRatioConfigLogMapper extends BaseMapper<InventoryToSaleRatioConfigLogEntity> {

    IPage<InventoryToSaleLogResponse> pageSearch(IPage page, @Param("query") InventoryToSaleLogPageRequest request);

    IPage<InventoryToSaleLogResponse> logIdsSearch(IPage page, @Param("query") InventoryToSaleLogPageRequest request);
}