package com.nsy.scm.repository.sql.mapper.develop;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nsy.scm.repository.entity.develop.FlowTaskSkcOtherItemEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * @Entity generator.domain.FlowTaskSkcOtherItemEntity
 */
@Mapper
public interface FlowTaskSkcOtherItemMapper extends BaseMapper<FlowTaskSkcOtherItemEntity> {
    void updateRejectRemark(Integer id, String rejectRemark, String operatorName);
}




