package com.nsy.scm.repository.entity.material.detection;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.api.scm.dto.constant.StringConstant;
import com.nsy.api.scm.dto.domain.material.detection.MaterialDetectionTaskItemProjectDto;
import com.nsy.api.scm.dto.constant.TrueOrFalseConstant;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 面料检测项目表(三级表)
 *
 * <AUTHOR>
 * @date 2024/06/05 16:43
 */
@Entity
@Table(name = "material_detection_task_item_project")
@TableName("material_detection_task_item_project")
public class MaterialDetectionTaskItemProjectEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer projectId;

    /**
     * 检测任务主表ID
     */
    private Integer taskId;

    /**
     * material_detection_task_item主表ID
     */
    private Integer taskItemId;

    /**
     * 项目大类-key
     */
    private String projectGroupKey;

    /**
     * 项目大类-中文
     */
    private String projectGroupText;

    /**
     * 检测项目 -key
     */
    private String projectKey;

    /**
     * 检测项目 -中文
     */
    private String projectText;

    /**
     * 检测方法
     */
    private String projectMethod;

    /**
     * 检测单位
     */
    private String projectUnit;

    /**
     * 检测要求
     */
    private String projectRequirement;

    /**
     * 检测结果值
     */
    private String projectResult;

    /**
     * 检测备注
     */
    private String projectRemark;

    /**
     * 检测结果/是否合格
     */
    private String result;

    /**
     * 检测结果备注
     */
    private String resultRemark;

    /**
     * 是否被删除：0-未删除，1-删除
     */
    private Integer isDelete;


    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public Integer getTaskItemId() {
        return taskItemId;
    }

    public void setTaskItemId(Integer taskItemId) {
        this.taskItemId = taskItemId;
    }

    public String getProjectGroupKey() {
        return projectGroupKey;
    }

    public void setProjectGroupKey(String projectGroupKey) {
        this.projectGroupKey = projectGroupKey;
    }

    public String getProjectGroupText() {
        return projectGroupText;
    }

    public void setProjectGroupText(String projectGroupText) {
        this.projectGroupText = projectGroupText;
    }

    public String getProjectKey() {
        return projectKey;
    }

    public void setProjectKey(String projectKey) {
        this.projectKey = projectKey;
    }

    public String getProjectText() {
        return projectText;
    }

    public void setProjectText(String projectText) {
        this.projectText = projectText;
    }

    public String getProjectMethod() {
        return projectMethod;
    }

    public void setProjectMethod(String projectMethod) {
        this.projectMethod = projectMethod;
    }

    public String getProjectUnit() {
        return projectUnit;
    }

    public void setProjectUnit(String projectUnit) {
        this.projectUnit = projectUnit;
    }

    public String getProjectRequirement() {
        return projectRequirement;
    }

    public void setProjectRequirement(String projectRequirement) {
        this.projectRequirement = projectRequirement;
    }

    public String getProjectResult() {
        return projectResult;
    }

    public void setProjectResult(String projectResult) {
        this.projectResult = projectResult;
    }

    public String getProjectRemark() {
        return projectRemark;
    }

    public void setProjectRemark(String projectRemark) {
        this.projectRemark = projectRemark;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getResultRemark() {
        return resultRemark;
    }

    public void setResultRemark(String resultRemark) {
        this.resultRemark = resultRemark;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public void copyFrom(MaterialDetectionTaskItemProjectDto dto) {
        this.setProjectGroupKey(dto.getProjectGroupKey());
        this.setProjectGroupText(dto.getProjectGroupText());
        this.setProjectKey(dto.getProjectKey());
        this.setProjectText(dto.getProjectText());
        this.setProjectMethod(dto.getProjectMethod());
        this.setProjectUnit(dto.getProjectUnit());
        this.setProjectRequirement(dto.getProjectRequirement());
        this.setProjectRemark(dto.getProjectRemark());
        this.setIsDelete(TrueOrFalseConstant.NOT_DELETED);
    }


    /**
     * 返回唯一值，项目大类KEY + 检测项目KEY 确认唯一
     *
     * @return 唯一值
     */
    public String getUniqueKey() {
        return getProjectGroupKey() + StringConstant.HASH + getProjectKey();
    }
}
