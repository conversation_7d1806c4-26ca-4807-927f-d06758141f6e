package com.nsy.scm.repository.entity.lx;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 领星已完成采购单数量表
 * <AUTHOR>
 * @TableName lx_completed_purchase_order_quantity
 */
@Entity
@Table(name = "lx_completed_purchase_order_quantity")
@TableName("lx_completed_purchase_order_quantity")
public class LxCompletedPurchaseOrderQuantityEntity extends BaseMpEntity {
    /**
     *
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer orderQuantityId;

    /**
     * 商品编码
     */
    private String spu;

    /**
     * 商品Id
     */
    private Integer productId;

    /**
     * 供应商Id
     */
    private Integer supplierId;

    /**
     * 领星已完成采购单量
     */
    private Integer completedQty;

    /**
     * 区域
     */
    private String location;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    public Integer getOrderQuantityId() {
        return orderQuantityId;
    }

    /**
     *
     */
    public void setOrderQuantityId(Integer orderQuantityId) {
        this.orderQuantityId = orderQuantityId;
    }

    /**
     * 商品编码
     */
    public String getSpu() {
        return spu;
    }

    /**
     * 商品编码
     */
    public void setSpu(String spu) {
        this.spu = spu;
    }

    /**
     * 商品Id
     */
    public Integer getProductId() {
        return productId;
    }

    /**
     * 商品Id
     */
    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    /**
     * 供应商Id
     */
    public Integer getSupplierId() {
        return supplierId;
    }

    /**
     * 供应商Id
     */
    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    /**
     * 领星已完成采购单量
     */
    public Integer getCompletedQty() {
        return completedQty;
    }

    /**
     * 领星已完成采购单量
     */
    public void setCompletedQty(Integer completedQty) {
        this.completedQty = completedQty;
    }

    /**
     * 区域
     */
    public String getLocation() {
        return location;
    }

    /**
     * 区域
     */
    public void setLocation(String location) {
        this.location = location;
    }

}
