package com.nsy.scm.repository.dao.bd;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.scm.repository.entity.bd.BdReplenishmentConfigEntity;
import com.nsy.scm.repository.sql.mapper.bd.BdReplenishmentConfigMapper;

/**
* <AUTHOR>
* @description 针对表【replenishment_config(补货配置表)】的数据库操作Service
* @createDate 2022-12-01 11:12:29
*/
public interface BdReplenishmentConfigDao extends IService<BdReplenishmentConfigEntity> {


    @Override
    BdReplenishmentConfigMapper getBaseMapper();
}
