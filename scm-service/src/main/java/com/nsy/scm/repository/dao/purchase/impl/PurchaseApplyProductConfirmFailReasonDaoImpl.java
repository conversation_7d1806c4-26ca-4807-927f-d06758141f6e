package com.nsy.scm.repository.dao.purchase.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.scm.repository.dao.purchase.PurchaseApplyProductConfirmFailReasonDao;
import com.nsy.scm.repository.entity.purchase.PurchaseApplyProductConfirmFailReasonEntity;
import com.nsy.scm.repository.sql.mapper.purchase.PurchaseApplyProductConfirmFailReasonMapper;
import com.nsy.scm.utils.mp.TenantContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 针对表【申请单推送采购失败原因】的数据库操作Dao实现
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Service
public class PurchaseApplyProductConfirmFailReasonDaoImpl extends ServiceImpl<PurchaseApplyProductConfirmFailReasonMapper, PurchaseApplyProductConfirmFailReasonEntity> implements PurchaseApplyProductConfirmFailReasonDao {

    @Autowired
    private LoginInfoService loginInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void coverFailReason(String spu, String skc, List<String> reasonList) {
        if (StringUtils.isBlank(spu) || StringUtils.isBlank(skc) || CollectionUtil.isEmpty(reasonList)) {
            return;
        }
        // 删除旧数据
        List<PurchaseApplyProductConfirmFailReasonEntity> failReason = getFailReason(spu, skc);
        if (CollectionUtil.isNotEmpty(failReason)) {
            List<Integer> ids = failReason.stream().map(PurchaseApplyProductConfirmFailReasonEntity::getReasonId).collect(Collectors.toList());
            removeByIds(ids);
        }
        // 保存新数据
        List<PurchaseApplyProductConfirmFailReasonEntity> entityList = reasonList.stream().map(reason -> {
            PurchaseApplyProductConfirmFailReasonEntity entity = new PurchaseApplyProductConfirmFailReasonEntity();
            entity.setSpu(spu);
            entity.setSkc(skc);
            entity.setLocation(TenantContext.getTenant());
            entity.setCreateBy(loginInfoService.getName());
            entity.setContent(reason);
            return entity;
        }).collect(Collectors.toList());
        saveBatch(entityList);
    }

    @Override
    public List<PurchaseApplyProductConfirmFailReasonEntity> getFailReason(String spu, String skc) {
        return list(Wrappers.<PurchaseApplyProductConfirmFailReasonEntity>lambdaQuery()
                .eq(PurchaseApplyProductConfirmFailReasonEntity::getSpu, spu)
                .eq(PurchaseApplyProductConfirmFailReasonEntity::getSkc, skc));
    }

    @Override
    public void clearFailReason(List<String> spuList, List<String> skcList, List<PurchaseApplyProductConfirmFailReasonEntity> entityList) {
        if (CollectionUtil.isEmpty(spuList) || CollectionUtil.isEmpty(skcList)) {
            return;
        }
        Map<String, List<PurchaseApplyProductConfirmFailReasonEntity>> skcMapBySpuAndSkc = findSkcMapBySpuAndSkc(spuList, skcList);
        List<Integer> notDeleteIds = Lists.newArrayList();
        entityList.forEach(reason -> {
            List<PurchaseApplyProductConfirmFailReasonEntity> purchaseApplyProductConfirmFailReasonEntities = skcMapBySpuAndSkc.getOrDefault(reason.getSkc(), Collections.emptyList());
            purchaseApplyProductConfirmFailReasonEntities.stream()
                    .filter(f -> StringUtils.equals(f.getContent(), reason.getContent()))
                    .findFirst().ifPresent(entity -> {
                        reason.setReasonId(entity.getReasonId());
                        notDeleteIds.add(entity.getReasonId());
                    });
        });
        List<Integer> deleteIds = skcMapBySpuAndSkc.values().stream().flatMap(Collection::stream).filter(entity -> !notDeleteIds.contains(entity.getReasonId())).map(PurchaseApplyProductConfirmFailReasonEntity::getReasonId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(deleteIds)) {
            return;
        }
        removeByIds(deleteIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void clearFailAndSave(List<String> spuList, List<String> skcList, List<PurchaseApplyProductConfirmFailReasonEntity> entityList) {
        clearFailReason(spuList, skcList, entityList);
        if (CollectionUtil.isEmpty(entityList)) {
            return;
        }
        saveOrUpdateBatch(entityList, 200);
    }

    @Override
    public Map<String, List<PurchaseApplyProductConfirmFailReasonEntity>> mapBySpuAndSkc(List<String> spuList, List<String> skcList) {
        if (CollectionUtil.isEmpty(spuList) && CollectionUtil.isEmpty(skcList)) {
            return Collections.emptyMap();
        }
        List<PurchaseApplyProductConfirmFailReasonEntity> list = list(Wrappers.<PurchaseApplyProductConfirmFailReasonEntity>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(spuList), PurchaseApplyProductConfirmFailReasonEntity::getSpu, spuList)
                .in(CollectionUtil.isNotEmpty(skcList), PurchaseApplyProductConfirmFailReasonEntity::getSkc, skcList));
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.groupingBy(PurchaseApplyProductConfirmFailReasonEntity::getKey));
    }

    @Override
    public Map<String, List<PurchaseApplyProductConfirmFailReasonEntity>> findSkcMapBySpuAndSkc(List<String> spuList, List<String> skcList) {
        if (CollectionUtil.isEmpty(spuList) && CollectionUtil.isEmpty(skcList)) {
            return Collections.emptyMap();
        }
        List<PurchaseApplyProductConfirmFailReasonEntity> list = list(Wrappers.<PurchaseApplyProductConfirmFailReasonEntity>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(spuList), PurchaseApplyProductConfirmFailReasonEntity::getSpu, spuList)
                .in(CollectionUtil.isNotEmpty(skcList), PurchaseApplyProductConfirmFailReasonEntity::getSkc, skcList));
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.groupingBy(PurchaseApplyProductConfirmFailReasonEntity::getSkc));
    }
}
