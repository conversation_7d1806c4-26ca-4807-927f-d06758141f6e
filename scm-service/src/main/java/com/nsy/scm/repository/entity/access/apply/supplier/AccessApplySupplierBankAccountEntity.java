package com.nsy.scm.repository.entity.access.apply.supplier;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 供应商准入申请单银行账号信息表
 *
 * <AUTHOR>
 * @date 2023/10/20 09:36
 */
@Entity
@Table(name = "access_apply_supplier_bank_account")
@TableName("access_apply_supplier_bank_account")
public class AccessApplySupplierBankAccountEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer applyBankAccountId;

    /**
     * 准入供应商申请单Id
     */
    private Integer accessApplyId;

    /**
     * 结算账号归属
     */
    private String ascription;

    /**
     * 开户银行
     */
    private String bank;

    /**
     * 银行开户名
     */
    private String bankAccountName;

    /**
     * 银行开户账号
     */
    private String bankAccountNumber;

    /**
     * 收款人联系方式
     */
    private String payeeContactWay;

    /**
     * 收款人身份证号
     */
    private String payeeIdNumber;

    /**
     * 是否默认账号
     */
    private Integer isDefaultAccount;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    public Integer getApplyBankAccountId() {
        return applyBankAccountId;
    }

    public void setApplyBankAccountId(Integer applyBankAccountId) {
        this.applyBankAccountId = applyBankAccountId;
    }

    public Integer getAccessApplyId() {
        return accessApplyId;
    }

    public void setAccessApplyId(Integer accessApplyId) {
        this.accessApplyId = accessApplyId;
    }

    public String getAscription() {
        return ascription;
    }

    public void setAscription(String ascription) {
        this.ascription = ascription;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public String getBankAccountNumber() {
        return bankAccountNumber;
    }

    public void setBankAccountNumber(String bankAccountNumber) {
        this.bankAccountNumber = bankAccountNumber;
    }

    public String getPayeeContactWay() {
        return payeeContactWay;
    }

    public void setPayeeContactWay(String payeeContactWay) {
        this.payeeContactWay = payeeContactWay;
    }

    public String getPayeeIdNumber() {
        return payeeIdNumber;
    }

    public void setPayeeIdNumber(String payeeIdNumber) {
        this.payeeIdNumber = payeeIdNumber;
    }

    public Integer getIsDefaultAccount() {
        return isDefaultAccount;
    }

    public void setIsDefaultAccount(Integer isDefaultAccount) {
        this.isDefaultAccount = isDefaultAccount;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}
