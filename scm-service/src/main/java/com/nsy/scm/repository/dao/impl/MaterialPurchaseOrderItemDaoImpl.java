package com.nsy.scm.repository.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.scm.dto.domain.material.MaterialPurchaseOrderDeliverySmsNotifyDTO;
import com.nsy.api.scm.dto.domain.material.MaterialPurchaseOrderItemDTO;
import com.nsy.api.scm.dto.domain.material.MaterialPurchaseOrderItemListByPurchaseOrderItemForSupplierDTO;
import com.nsy.api.scm.dto.domain.material.MaterialPurchaseOrderItemReceivedPageDTO;
import com.nsy.api.scm.dto.domain.material.MaterialSupplierColorCardStockOrderDetailPageDTO;
import com.nsy.api.scm.dto.domain.material.QueryByPurchaseOrderNoAndSkcDTO;
import com.nsy.api.scm.dto.request.material.MaterialPurchaseOrderItemListByPurchaseOrderItemForSupplierRequest;
import com.nsy.api.scm.dto.request.material.MaterialPurchaseOrderItemListRequest;
import com.nsy.api.scm.dto.request.material.MaterialPurchaseOrderItemSupplierPageRequest;
import com.nsy.api.scm.dto.request.material.MaterialSupplierColorCardStockOrderDetialPageRequest;
import com.nsy.api.scm.dto.request.material.PurchaseOrderItemTabRequest;
import com.nsy.api.scm.dto.response.material.MaterialPurchaseOrderItemByOrderItemIdsRes;
import com.nsy.api.scm.dto.response.material.TabInfoRes;
import com.nsy.scm.constant.MybatisQueryConstant;
import com.nsy.scm.constant.NumberConstant;
import com.nsy.scm.constant.TrueOrFalseConstant;
import com.nsy.scm.repository.dao.MaterialPurchaseOrderItemDao;
import com.nsy.scm.repository.entity.material.MaterialPurchaseOrderItemEntity;
import com.nsy.scm.repository.sql.mapper.material.MaterialPurchaseOrderItemMapper;
import com.nsy.scm.utils.IntegerUtils;
import com.nsy.scm.utils.exception.DataOutOfDateException;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 面料集采单项dao实现
 *
 * <AUTHOR>
 * @since 2022-05-12 15:09
 */
@Service
public class MaterialPurchaseOrderItemDaoImpl extends ServiceImpl<MaterialPurchaseOrderItemMapper, MaterialPurchaseOrderItemEntity> implements MaterialPurchaseOrderItemDao {

    @Override
    public List<MaterialPurchaseOrderItemEntity> queryByPurchaseOrderId(Integer materialPurchaseOrderId) {
        LambdaQueryWrapper<MaterialPurchaseOrderItemEntity> queryWrapper = new LambdaQueryWrapper<MaterialPurchaseOrderItemEntity>()
                .eq(MaterialPurchaseOrderItemEntity::getPurchaseOrderId, materialPurchaseOrderId)
                .eq(MaterialPurchaseOrderItemEntity::getIsDelete, TrueOrFalseConstant.NOT_DELETED);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<MaterialPurchaseOrderItemEntity> queryByPurchaseOrderIds(Collection<Integer> materialPurchaseOrderIds) {
        if (CollectionUtils.isEmpty(materialPurchaseOrderIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaterialPurchaseOrderItemEntity> queryWrapper = new LambdaQueryWrapper<MaterialPurchaseOrderItemEntity>()
                .in(MaterialPurchaseOrderItemEntity::getPurchaseOrderId, materialPurchaseOrderIds)
                .eq(MaterialPurchaseOrderItemEntity::getIsDelete, TrueOrFalseConstant.NOT_DELETED);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<MaterialPurchaseOrderItemEntity> queryByPurchaseOrderItemIds(Collection<Integer> materialPurchaseOrderItemIds) {
        if (CollectionUtils.isEmpty(materialPurchaseOrderItemIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaterialPurchaseOrderItemEntity> queryWrapper = new LambdaQueryWrapper<MaterialPurchaseOrderItemEntity>()
                .in(MaterialPurchaseOrderItemEntity::getPurchaseOrderItemId, materialPurchaseOrderItemIds)
                .eq(MaterialPurchaseOrderItemEntity::getIsDelete, TrueOrFalseConstant.NOT_DELETED);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<MaterialPurchaseOrderItemEntity> queryByPurchaseOrderItemIdsAndStatus(Set<Integer> materialPurchaseOrderItemIds, List<Integer> status) {
        LambdaQueryWrapper<MaterialPurchaseOrderItemEntity> queryWrapper = new LambdaQueryWrapper<MaterialPurchaseOrderItemEntity>()
                .in(MaterialPurchaseOrderItemEntity::getPurchaseOrderItemId, materialPurchaseOrderItemIds)
                .eq(MaterialPurchaseOrderItemEntity::getIsDelete, TrueOrFalseConstant.NOT_DELETED);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<MaterialPurchaseOrderItemEntity> queryByPurchaseOrderItemIdsAndNotInStatus(Collection<Integer> materialPurchaseOrderItemIds, Collection<Integer> notInStatus) {
        LambdaQueryWrapper<MaterialPurchaseOrderItemEntity> queryWrapper = new LambdaQueryWrapper<MaterialPurchaseOrderItemEntity>()
                .in(MaterialPurchaseOrderItemEntity::getPurchaseOrderItemId, materialPurchaseOrderItemIds)
                .notIn(CollectionUtil.isNotEmpty(notInStatus), MaterialPurchaseOrderItemEntity::getStatus, notInStatus)
                .eq(MaterialPurchaseOrderItemEntity::getIsDelete, TrueOrFalseConstant.NOT_DELETED);
        return this.baseMapper.selectList(queryWrapper);
    }


    @Override
    public List<MaterialPurchaseOrderItemEntity> queryByItemIds(Collection<Integer> purchaseItemIds) {
        if (CollectionUtils.isEmpty(purchaseItemIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<MaterialPurchaseOrderItemEntity> queryWrapper = new LambdaQueryWrapper<MaterialPurchaseOrderItemEntity>()
                .in(MaterialPurchaseOrderItemEntity::getPurchaseOrderItemId, purchaseItemIds);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<MaterialPurchaseOrderItemEntity> queryMaterialPurchaseOrderItems(Set<Integer> purchaseOrderIds, Set<Integer> purchaseItemIds) {
        if (CollectionUtils.isEmpty(purchaseOrderIds) && CollectionUtils.isEmpty(purchaseItemIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaterialPurchaseOrderItemEntity> queryWrapper = new LambdaQueryWrapper<MaterialPurchaseOrderItemEntity>()
                .in(!CollectionUtils.isEmpty(purchaseOrderIds), MaterialPurchaseOrderItemEntity::getPurchaseOrderId, purchaseOrderIds)
                .in(!CollectionUtils.isEmpty(purchaseItemIds), MaterialPurchaseOrderItemEntity::getPurchaseOrderItemId, purchaseItemIds);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public void deleteByItemIds(Set<Integer> purchaseOrderItemIds) {
        List<MaterialPurchaseOrderItemEntity> entities = queryByItemIds(purchaseOrderItemIds);
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        for (MaterialPurchaseOrderItemEntity entity : entities) {
            entity.setIsDelete(TrueOrFalseConstant.DELETED);
        }
        this.saveOrUpdateBatch(entities);
    }

    @Override
    public void saveOrderItem(MaterialPurchaseOrderItemEntity orderItemEntity) {
        boolean saveSuccess = saveOrUpdate(orderItemEntity);
        if (!saveSuccess) {
            throw new DataOutOfDateException("当前集采单明细数据已过期，请刷新重试！orderItemId = " + orderItemEntity.getPurchaseOrderItemId());
        }
    }

    @Override
    public IPage<MaterialSupplierColorCardStockOrderDetailPageDTO> floralStockOrderDetailPage(Page page, MaterialSupplierColorCardStockOrderDetialPageRequest request) {
        return baseMapper.floralStockOrderDetailPage(page, request);
    }

    @Override
    public MaterialPurchaseOrderItemEntity queryByItemId(Integer itemId) {
        if (Objects.isNull(itemId)) {
            return null;
        }

        return baseMapper.selectOne(new LambdaQueryWrapper<MaterialPurchaseOrderItemEntity>()
                .eq(MaterialPurchaseOrderItemEntity::getPurchaseOrderItemId, itemId)
                .eq(MaterialPurchaseOrderItemEntity::getIsDelete, TrueOrFalseConstant.NOT_DELETED)
                .last(MybatisQueryConstant.QUERY_FIRST));
    }

    @Override
    public List<MaterialPurchaseOrderItemByOrderItemIdsRes> queryPurchaseOrderItemByPurchaseOrderItemIds(Set<Integer> purchaseOrderItemIds) {
        if (CollectionUtils.isEmpty(purchaseOrderItemIds)) {
            return new ArrayList<>();
        }
        return baseMapper.queryPurchaseOrderItemByPurchaseOrderItemIds(purchaseOrderItemIds);
    }

    @Override
    public List<MaterialPurchaseOrderItemReceivedPageDTO> receivedList(MaterialPurchaseOrderItemSupplierPageRequest request) {
        return baseMapper.receivedList(request);
    }


    @Override
    public List<MaterialPurchaseOrderItemListByPurchaseOrderItemForSupplierDTO> listByPurchaseOrderItemForSupplier(MaterialPurchaseOrderItemListByPurchaseOrderItemForSupplierRequest request) {
        return baseMapper.listByPurchaseOrderItemForSupplier(request);
    }

    @Override
    public List<MaterialPurchaseOrderItemListByPurchaseOrderItemForSupplierDTO> listByPurchaseOrderItemForPurchaseOrderAdjustmentRecordAdd(MaterialPurchaseOrderItemListByPurchaseOrderItemForSupplierRequest request) {
        return baseMapper.listByPurchaseOrderItemForPurchaseOrderAdjustmentRecordAdd(request);
    }

    @Override
    public List<MaterialPurchaseOrderItemEntity> queryByBulkOrderItemIds(Set<Integer> bulkOrderItemIds) {
        if (CollectionUtils.isEmpty(bulkOrderItemIds)) {
            return new ArrayList<>();
        }
        return baseMapper.queryByBulkOrderItemIds(bulkOrderItemIds);
    }

    @Override
    public List<TabInfoRes> queryPurchaseOrderItemReceiveAndAcceptanceCount(PurchaseOrderItemTabRequest request, Set<Integer> statusSet) {
        return baseMapper.queryPurchaseOrderItemReceiveAndAcceptanceCount(request, statusSet);
    }

    @Override
    public List<MaterialPurchaseOrderItemEntity> maxListByColorCardIdList(Collection<Integer> colorCardIds, Collection<Integer> notInStatus) {
        if (CollectionUtils.isEmpty(colorCardIds)) {
            return new ArrayList<>();
        }
        return baseMapper.maxListByColorCardIdList(colorCardIds, notInStatus);
    }

    @Override
    public List<String> existOrderByByMaterialSupplierInfoId(Collection<Integer> materialSupplierInfoIdList) {
        if (CollectionUtils.isEmpty(materialSupplierInfoIdList)) {
            return null;
        }
        return baseMapper.existOrderByByMaterialSupplierInfoId(materialSupplierInfoIdList);
    }

    @Override
    public List<QueryByPurchaseOrderNoAndSkcDTO> queryByPurchaseOrderNoAndSkc(Set<String> orderNos, Set<String> skcs) {
        if (CollectionUtil.isEmpty(orderNos) || CollectionUtil.isEmpty(skcs)) {
            return ListUtil.empty();
        }
        return baseMapper.queryByPurchaseOrderNoAndSkc(orderNos, skcs);
    }

    @Override
    public Integer countByMaterialSupplierIdAndStatus(Integer materialSupplierId, Integer status) {
        if (IntegerUtils.isNotValid(materialSupplierId) || Objects.isNull(status)) {
            return NumberConstant.ZERO;
        }
        return baseMapper.countByMaterialSupplierIdAndStatus(materialSupplierId, status);
    }

    @Override
    public List<MaterialPurchaseOrderDeliverySmsNotifyDTO> getDeliveredSmsNotifyOrderItemList(Collection<Integer> statuses) {
        return baseMapper.getDeliveredSmsNotifyOrderItemList(statuses);
    }

    @Override
    public List<MaterialPurchaseOrderItemDTO> listItem(MaterialPurchaseOrderItemListRequest request) {
        return baseMapper.listItem(request);
    }
}