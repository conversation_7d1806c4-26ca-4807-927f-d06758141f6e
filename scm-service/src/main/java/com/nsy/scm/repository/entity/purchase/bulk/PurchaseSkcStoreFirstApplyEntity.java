package com.nsy.scm.repository.entity.purchase.bulk;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.Date;

/**
 * 店铺skc申请首单信息表
 * @TableName purchase_skc_store_first_apply
 */
@TableName("purchase_skc_store_first_apply")
public class PurchaseSkcStoreFirstApplyEntity extends BaseMpEntity {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer skcStoreFirstApplyId;

    /**
     * 商品Id
     */
    private Integer productId;

    /**
     * 商品编码
     */
    private String spu;

    /**
     * 颜色编码
     */
    private String skc;

    /**
     * 店铺Id
     */
    private Integer storeId;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 店铺首次申请时间
     */
    private Date storeFirstApplyDate;

    /**
     * 店铺首次申请单号
     */
    private String storeFirstApplyNo;

    /**
     * 店铺首次申请单id
     */
    private Integer storeFirstApplyId;

    /**
     * 区域
     */
    private String location;

    /**
     * 主键
     */
    public Integer getSkcStoreFirstApplyId() {
        return skcStoreFirstApplyId;
    }

    /**
     * 主键
     */
    public void setSkcStoreFirstApplyId(Integer skcStoreFirstApplyId) {
        this.skcStoreFirstApplyId = skcStoreFirstApplyId;
    }

    /**
     * 商品Id
     */
    public Integer getProductId() {
        return productId;
    }

    /**
     * 商品Id
     */
    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    /**
     * 商品编码
     */
    public String getSpu() {
        return spu;
    }

    /**
     * 商品编码
     */
    public void setSpu(String spu) {
        this.spu = spu;
    }

    /**
     * 颜色编码
     */
    public String getSkc() {
        return skc;
    }

    /**
     * 颜色编码
     */
    public void setSkc(String skc) {
        this.skc = skc;
    }

    /**
     * 店铺Id
     */
    public Integer getStoreId() {
        return storeId;
    }

    /**
     * 店铺Id
     */
    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    /**
     * 店铺名称
     */
    public String getStoreName() {
        return storeName;
    }

    /**
     * 店铺名称
     */
    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    /**
     * 店铺首次申请时间
     */
    public Date getStoreFirstApplyDate() {
        return storeFirstApplyDate;
    }

    /**
     * 店铺首次申请时间
     */
    public void setStoreFirstApplyDate(Date storeFirstApplyDate) {
        this.storeFirstApplyDate = storeFirstApplyDate;
    }

    /**
     * 店铺首次申请单号
     */
    public String getStoreFirstApplyNo() {
        return storeFirstApplyNo;
    }

    /**
     * 店铺首次申请单号
     */
    public void setStoreFirstApplyNo(String storeFirstApplyNo) {
        this.storeFirstApplyNo = storeFirstApplyNo;
    }

    /**
     * 店铺首次申请单id
     */
    public Integer getStoreFirstApplyId() {
        return storeFirstApplyId;
    }

    /**
     * 店铺首次申请单id
     */
    public void setStoreFirstApplyId(Integer storeFirstApplyId) {
        this.storeFirstApplyId = storeFirstApplyId;
    }

    /**
     * 区域
     */
    public String getLocation() {
        return location;
    }

    /**
     * 区域
     */
    public void setLocation(String location) {
        this.location = location;
    }
}
