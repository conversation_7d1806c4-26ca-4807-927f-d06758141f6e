package com.nsy.scm.repository.dao.clothespart;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.api.scm.dto.domain.SelectModel;
import com.nsy.api.scm.dto.request.clothespart.PageQueryClothesPartTemplateRequest;
import com.nsy.api.scm.dto.response.clothespart.ClothesPartTemplateResponse;
import com.nsy.scm.repository.entity.clothespart.BdClothesPartTemplateInfoEntity;

import java.util.List;

/**
 * 服装部位模板信息表DAO
 *
 * <AUTHOR>
 * @since 2023/9/14 15:33
 */
public interface BdClothesPartTemplateInfoDao extends IService<BdClothesPartTemplateInfoEntity> {

    List<String> queryTemplateListByMeasureMethodIds(List<Integer> measureMethodIds);


    IPage<ClothesPartTemplateResponse> pageQueryClothesPartTemplate(PageQueryClothesPartTemplateRequest request);

    BdClothesPartTemplateInfoEntity queryByName(String templateName);

    List<SelectModel> queryTemplateOptions(List<Integer> categoryIdList);

    List<BdClothesPartTemplateInfoEntity> listByNames(List<String> templateNames);

    List<BdClothesPartTemplateInfoEntity> listByTemplateIds(List<Integer> templateIds);
}
