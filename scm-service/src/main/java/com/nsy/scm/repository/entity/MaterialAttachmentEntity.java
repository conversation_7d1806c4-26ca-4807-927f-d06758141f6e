package com.nsy.scm.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.nsy.api.scm.dto.constant.AttachmentTypeEnum;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <p>
 * 物料附件表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Entity
@Table(name = "material_attachment")
@TableName("material_attachment")
public class MaterialAttachmentEntity extends BaseMpEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer attachmentId;

    /**
     * 物料id
     */
    private Integer materialId;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 附件类型
     */
    private AttachmentTypeEnum attachmentType;

    /**
     * 附件名称
     */
    private String originName;

    /**
     * oss文件名称
     */
    private String attachmentName;

    /**
     * 附件url
     */
    private String attachmentUrl;

    /**
     * 是否被删除：0-未删除，1-删除
     */
    private int isDelete;


    public Integer getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(Integer attachmentId) {
        this.attachmentId = attachmentId;
    }

    public Integer getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Integer materialId) {
        this.materialId = materialId;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public AttachmentTypeEnum getAttachmentType() {
        return attachmentType;
    }

    public void setAttachmentType(AttachmentTypeEnum attachmentType) {
        this.attachmentType = attachmentType;
    }

    public String getOriginName() {
        return originName;
    }

    public void setOriginName(String originName) {
        this.originName = originName;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }


}
