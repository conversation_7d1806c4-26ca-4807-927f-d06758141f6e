package com.nsy.scm.repository.sql.mapper.material;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.scm.repository.entity.MaterialPurchaseOrderLogEntity;
import com.nsy.api.scm.dto.request.material.PageQueryMaterialPurchaseOrderLogRequest;

/**
 * 面料集采单操作日志Mapper
 *
 * <AUTHOR>
 * @since 2022-05-13 17:55
 */
public interface MaterialPurchaseOrderLogMapper extends BaseMapper<MaterialPurchaseOrderLogEntity> {

    IPage<MaterialPurchaseOrderLogEntity> pageQuery(IPage<MaterialPurchaseOrderLogEntity> page, PageQueryMaterialPurchaseOrderLogRequest request);

}
