package com.nsy.scm.repository.entity.material.sample;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import java.util.Date;

/**
 * 面料打样流程任务记录表
 *
 * <AUTHOR>
 * @date 2023/10/28 16:47
 */
@TableName("flow_task_material_sample_record")
public class FlowTaskMaterialSampleRecordEntity extends BaseMpEntity {

    @TableId(type = IdType.AUTO)
    private Integer recordId;

    /**
     * 任务流程ID
     */
    private Integer taskId;

    /**
     * 任务流程明细ID
     */
    private Integer taskItemId;

    /**
     * 记录类型：跟单(FOLLOW_UP)、审核(EXAMINE)
     */
    private String recordType;

    /**
     * 记录子类型：审核通过(EXAMINE_PASS)、审核驳回(EXAMINE_REJECT)
     */
    private String recordSubtype;

    /**
     * 审样类型
     */
    private String sampleType;

    /**
     * 送样类型
     */
    private String sendType;

    /**
     * 寄样时间
     */
    private Date sampleSendTime;

    /**
     * 当前版次
     */
    private Integer currentVersion;

    /**
     * 记录说明
     */
    private String recordDescribe;

    /**
     * 记录者
     */
    private String recordBy;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 地区
     */
    private String location;

    public Integer getRecordId() {
        return recordId;
    }

    public void setRecordId(Integer recordId) {
        this.recordId = recordId;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public Integer getTaskItemId() {
        return taskItemId;
    }

    public void setTaskItemId(Integer taskItemId) {
        this.taskItemId = taskItemId;
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public String getRecordSubtype() {
        return recordSubtype;
    }

    public void setRecordSubtype(String recordSubtype) {
        this.recordSubtype = recordSubtype;
    }

    public String getRecordDescribe() {
        return recordDescribe;
    }

    public void setRecordDescribe(String recordDescribe) {
        this.recordDescribe = recordDescribe;
    }

    public String getRecordBy() {
        return recordBy;
    }

    public void setRecordBy(String recordBy) {
        this.recordBy = recordBy;
    }

    public Date getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Date recordTime) {
        this.recordTime = recordTime;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSampleType() {
        return sampleType == null ? "" : sampleType;
    }

    public void setSampleType(String sampleType) {
        this.sampleType = sampleType;
    }

    public Integer getCurrentVersion() {
        return currentVersion;
    }

    public void setCurrentVersion(Integer currentVersion) {
        this.currentVersion = currentVersion;
    }

    public String getSendType() {
        return sendType;
    }

    public void setSendType(String sendType) {
        this.sendType = sendType;
    }

    public Date getSampleSendTime() {
        return sampleSendTime;
    }

    public void setSampleSendTime(Date sampleSendTime) {
        this.sampleSendTime = sampleSendTime;
    }
}
