package com.nsy.scm.repository.entity.replenishment;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

/**
 * 品类分组明细表
 *
 * <AUTHOR>
 * @since 2023/5/12 9:35
 */
@TableName("bd_category_group_item")
public class BdCategoryGroupItemEntity extends BaseMpEntity {

    @TableId(type = IdType.AUTO)
    private Integer groupItemId;

    private Integer groupId;

    private Integer categoryId;

    private String categoryName;

    @TableField("category_2_id")
    private Integer category2Id;

    @TableField("category_2_name")
    private String category2Name;

    @TableField("category_1_id")
    private Integer category1Id;

    @TableField("category_1_name")
    private String category1Name;

    private String location;

    public Integer getGroupItemId() {
        return groupItemId;
    }

    public void setGroupItemId(Integer groupItemId) {
        this.groupItemId = groupItemId;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Integer getCategory2Id() {
        return category2Id;
    }

    public void setCategory2Id(Integer category2Id) {
        this.category2Id = category2Id;
    }

    public String getCategory2Name() {
        return category2Name;
    }

    public void setCategory2Name(String category2Name) {
        this.category2Name = category2Name;
    }

    public Integer getCategory1Id() {
        return category1Id;
    }

    public void setCategory1Id(Integer category1Id) {
        this.category1Id = category1Id;
    }

    public String getCategory1Name() {
        return category1Name;
    }

    public void setCategory1Name(String category1Name) {
        this.category1Name = category1Name;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}
