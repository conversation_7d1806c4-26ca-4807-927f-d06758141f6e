package com.nsy.scm.repository.dao.purchase.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.scm.business.manage.fds.request.PurchaseOrderWorkmanshipSheetRequest;
import com.nsy.scm.constant.MybatisQueryConstant;
import com.nsy.scm.repository.dao.purchase.PurchaseOrderWorkmanshipAttachmentDao;
import com.nsy.scm.repository.entity.purchase.PurchaseOrderWorkmanshipAttachmentEntity;
import com.nsy.scm.repository.entity.purchase.bulk.PurchaseOrderItemEntity;
import com.nsy.scm.repository.sql.mapper.purchase.PurchaseOrderWorkmanshipAttachmentMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购单级别工艺单附件dao实现类
 * <AUTHOR>
 * @create 2024-11-07 10:55
 */
@Repository
public class PurchaseOrderWorkmanshipAttachmentDaoImpl extends ServiceImpl<PurchaseOrderWorkmanshipAttachmentMapper, PurchaseOrderWorkmanshipAttachmentEntity> implements PurchaseOrderWorkmanshipAttachmentDao {



    @Override
    public PurchaseOrderWorkmanshipAttachmentEntity queryBySheetRequest(PurchaseOrderWorkmanshipSheetRequest request) {
        return this.getOne(new LambdaQueryWrapper<PurchaseOrderWorkmanshipAttachmentEntity>()
                .eq(PurchaseOrderWorkmanshipAttachmentEntity::getOrderId, request.getOrderId())
                .eq(PurchaseOrderWorkmanshipAttachmentEntity::getProductId, request.getProductId())
                .eq(PurchaseOrderWorkmanshipAttachmentEntity::getVersionNo, request.getVersionNo())
                .eq(PurchaseOrderWorkmanshipAttachmentEntity::getBrandId, request.getBrandId())
                .eq(PurchaseOrderWorkmanshipAttachmentEntity::getPackageName, request.getPackageName())
                .orderByDesc(PurchaseOrderWorkmanshipAttachmentEntity::getLatestChildVersionId).last(MybatisQueryConstant.QUERY_FIRST));
    }

    @Override
    public List<PurchaseOrderWorkmanshipAttachmentEntity> queryByPurchaseOrderItems(List<PurchaseOrderItemEntity> purchaseOrderItemList) {
        if (CollectionUtils.isEmpty(purchaseOrderItemList)) {
            return Collections.emptyList();
        }
        List<PurchaseOrderWorkmanshipAttachmentEntity> resultList = new ArrayList<>();
        Lists.partition(purchaseOrderItemList, 200).forEach(list -> {
            List<Integer> orderIds = list.stream().map(PurchaseOrderItemEntity::getOrderId).distinct().collect(Collectors.toList());
            List<Integer> productIds = list.stream().map(PurchaseOrderItemEntity::getProductId).distinct().collect(Collectors.toList());
            List<String> versionNoList = list.stream().map(PurchaseOrderItemEntity::getWorkmanshipVersionNo).distinct().collect(Collectors.toList());
            List<Integer> brandIds = list.stream().map(PurchaseOrderItemEntity::getBrandId).distinct().collect(Collectors.toList());
            List<String> packageNames = list.stream().map(PurchaseOrderItemEntity::getPackageName).distinct().collect(Collectors.toList());
            resultList.addAll(this.list(new LambdaQueryWrapper<PurchaseOrderWorkmanshipAttachmentEntity>()
                    .in(PurchaseOrderWorkmanshipAttachmentEntity::getOrderId, orderIds)
                    .in(PurchaseOrderWorkmanshipAttachmentEntity::getProductId, productIds)
                    .in(PurchaseOrderWorkmanshipAttachmentEntity::getVersionNo, versionNoList)
                    .in(PurchaseOrderWorkmanshipAttachmentEntity::getBrandId, brandIds)
                    .in(PurchaseOrderWorkmanshipAttachmentEntity::getPackageName, packageNames)));
        });
        return resultList;
    }

}
