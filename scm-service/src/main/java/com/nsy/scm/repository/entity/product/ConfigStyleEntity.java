package com.nsy.scm.repository.entity.product;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.Date;

/**
 * 商品风格标签表 Entity
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@TableName("config_style")
public class ConfigStyleEntity extends BaseMpEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
    * 风格id
    */
    private Integer styleId;

    /**
    * 风格类型
    */
    private String styleType;

    /**
    * dtc主题分组
    */
    private String dtcStyleGroup;

    /**
    * 风格名称
    */
    private String styleName;

    /**
    * 主题
    */
    private String styleGroup;

    /**
    * 风格名称英文
    */
    private String styleNameEn;

    /**
    * 描述
    */
    private String description;

    /**
    * 是否系统风格
    */
    private Integer isSystem;

    /**
    * 是否删除
    */
    private Integer isDelete;

    /**
    * 排序
    */
    private Integer sort;

    /**
    * 风格class
    */
    private String styleClass;

    /**
    * 属性1
    */
    @TableField("properties_1")
    private String properties1;

    /**
    * 属性2
    */
    @TableField("properties_2")
    private String properties2;

    /**
    * 属性3
    */
    @TableField("properties_3")
    private String properties3;

    /**
    * 过期时间
    */
    private Date expiredDate;

    /**
    * 是否ip侵权
    */
    private Integer isInfringement;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getStyleId() {
        return styleId;
    }

    public void setStyleId(Integer styleId) {
        this.styleId = styleId;
    }

    public String getStyleType() {
        return styleType;
    }

    public void setStyleType(String styleType) {
        this.styleType = styleType;
    }

    public String getDtcStyleGroup() {
        return dtcStyleGroup;
    }

    public void setDtcStyleGroup(String dtcStyleGroup) {
        this.dtcStyleGroup = dtcStyleGroup;
    }

    public String getStyleName() {
        return styleName;
    }

    public void setStyleName(String styleName) {
        this.styleName = styleName;
    }

    public String getStyleGroup() {
        return styleGroup;
    }

    public void setStyleGroup(String styleGroup) {
        this.styleGroup = styleGroup;
    }

    public String getStyleNameEn() {
        return styleNameEn;
    }

    public void setStyleNameEn(String styleNameEn) {
        this.styleNameEn = styleNameEn;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Integer isSystem) {
        this.isSystem = isSystem;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getStyleClass() {
        return styleClass;
    }

    public void setStyleClass(String styleClass) {
        this.styleClass = styleClass;
    }

    public String getProperties1() {
        return properties1;
    }

    public void setProperties1(String properties1) {
        this.properties1 = properties1;
    }

    public String getProperties2() {
        return properties2;
    }

    public void setProperties2(String properties2) {
        this.properties2 = properties2;
    }

    public String getProperties3() {
        return properties3;
    }

    public void setProperties3(String properties3) {
        this.properties3 = properties3;
    }

    public Date getExpiredDate() {
        return expiredDate;
    }

    public void setExpiredDate(Date expiredDate) {
        this.expiredDate = expiredDate;
    }

    public Integer getIsInfringement() {
        return isInfringement;
    }

    public void setIsInfringement(Integer isInfringement) {
        this.isInfringement = isInfringement;
    }


}
