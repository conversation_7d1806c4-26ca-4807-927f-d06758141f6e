package com.nsy.scm.repository.entity.purchase.platform;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.scm.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 平台订单物流表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-15
 */
@Entity
@Table(name = "purchase_order_platform_order_logistics")
@TableName("purchase_order_platform_order_logistics")
public class PurchaseOrderPlatformOrderLogisticsEntity extends BaseMpEntity {


    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer logisticsId;

    /**
     * 采购单主键id
     */
    private Integer purchaseOrderId;

    /**
     * 采购单号
     */
    private String purchaseOrderPlanNo;

    /**
     *  平台订单表主键id
     */
    private Integer platformOrderId;
    /**
     * 平台订单号
     */
    private String platformOrderNo;

    /**
     * 物流类型-1:平台卖家发货物流，2:买家退货物流
     */
    private int logisticsType;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 平台物流信息ID
     */
    private String platformLogisticsId;

    /**
     * 配送的商品总数量
     */
    private BigDecimal goodsQuantity;

    /**
     * 总箱数
     */
    private Integer totalBox;

    /**
     * 物流状态
     */
    private int logisticsStatus;

    /**
     * 签收时间
     */
    private Date signTime;

    /**
     * 发货时间
     */
    private Date deliverTime;

    /**
     * 地区
     */
    private String location;


    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public Integer getTotalBox() {
        return totalBox;
    }

    public void setTotalBox(Integer totalBox) {
        this.totalBox = totalBox;
    }

    public Date getDeliverTime() {
        return deliverTime;
    }

    public void setDeliverTime(Date deliverTime) {
        this.deliverTime = deliverTime;
    }

    public Integer getPlatformOrderId() {
        return platformOrderId;
    }

    public void setPlatformOrderId(Integer platformOrderId) {
        this.platformOrderId = platformOrderId;
    }

    public Integer getLogisticsId() {
        return logisticsId;
    }

    public void setLogisticsId(Integer logisticsId) {
        this.logisticsId = logisticsId;
    }

    public Integer getPurchaseOrderId() {
        return purchaseOrderId;
    }

    public void setPurchaseOrderId(Integer purchaseOrderId) {
        this.purchaseOrderId = purchaseOrderId;
    }

    public String getPurchaseOrderPlanNo() {
        return purchaseOrderPlanNo;
    }

    public void setPurchaseOrderPlanNo(String purchaseOrderPlanNo) {
        this.purchaseOrderPlanNo = purchaseOrderPlanNo;
    }

    public String getPlatformOrderNo() {
        return platformOrderNo;
    }

    public void setPlatformOrderNo(String platformOrderNo) {
        this.platformOrderNo = platformOrderNo;
    }

    public int getLogisticsType() {
        return logisticsType;
    }

    public void setLogisticsType(int logisticsType) {
        this.logisticsType = logisticsType;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getPlatformLogisticsId() {
        return platformLogisticsId;
    }

    public void setPlatformLogisticsId(String platformLogisticsId) {
        this.platformLogisticsId = platformLogisticsId;
    }

    public BigDecimal getGoodsQuantity() {
        return Objects.isNull(goodsQuantity) ? BigDecimal.ZERO : goodsQuantity;
    }

    public void setGoodsQuantity(BigDecimal goodsQuantity) {
        this.goodsQuantity = goodsQuantity;
    }

    public int getLogisticsStatus() {
        return logisticsStatus;
    }

    public void setLogisticsStatus(int logisticsStatus) {
        this.logisticsStatus = logisticsStatus;
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }


}
