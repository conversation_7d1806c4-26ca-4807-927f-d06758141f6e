package com.nsy.scm.repository.sql.mapper.material;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nsy.scm.business.domain.material.MaterialPrepareSafetyStockProductRevisionDto;
import com.nsy.scm.repository.entity.material.MaterialPreparationOnlineImportEntity;
import com.nsy.scm.repository.entity.material.MaterialSafetyStockDetailEntity;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

/**
 * <h3>面料安全库存明细 Mapper 接口</h3>
 *
 * <AUTHOR>
 * @date 2023/11/24 11:00
 */
public interface MaterialSafetyStockDetailMapper extends BaseMapper<MaterialSafetyStockDetailEntity> {

    /**
     * 根据色卡ID查询安全库存，需要备料截止滚动日期大于等于当前日期
     * @param colorCardIds
     * @return
     */
    List<MaterialSafetyStockDetailEntity> listByColorCardIds(@Param("colorCardIds") Collection<Integer> colorCardIds);

    /**
     * 通过色卡ID、模式、部门和skc获取安全库存信息
     *
     * @param onlineImportList 线上备料导入集合
     * @return 安全库存信息
     */
    List<MaterialSafetyStockDetailEntity> listByColorCardIdAndModeAndBusinessTypeAndSkc(@Param("onlineImportList") List<MaterialPreparationOnlineImportEntity> onlineImportList);
    /**
     * 通过色卡ID和模式获取安全库存信息
     *
     * @param onlineImportList 线上备料导入集合
     * @return 安全库存信息
     */
    List<MaterialSafetyStockDetailEntity> listByColorCardIdAndMode(@Param("onlineImportList") List<MaterialPreparationOnlineImportEntity> onlineImportList);

    /**
     * 根据改版状态查询安全库存明细
     * @param taskStatus
     * @return
     */
    List<MaterialPrepareSafetyStockProductRevisionDto> listByProductRevisionTaskStatus(@Param("taskStatus") Collection<Integer> taskStatus);
}
