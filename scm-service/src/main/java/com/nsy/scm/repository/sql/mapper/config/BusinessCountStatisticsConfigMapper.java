package com.nsy.scm.repository.sql.mapper.config;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nsy.api.scm.dto.request.stat.BusinessCountStatisticsRequest;
import com.nsy.api.scm.dto.response.stat.BusinessCountStatisticsDTO;
import com.nsy.scm.repository.entity.config.BusinessCountStatisticsConfigEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <h3>业务计数统计配置表Mapper接口</h3>
 *
 * <AUTHOR>
 * @since 2024/03/01 16:14
 */
public interface BusinessCountStatisticsConfigMapper extends BaseMapper<BusinessCountStatisticsConfigEntity> {

    /**
     * 获取业务计数统计
     *
     * @param request 请求
     * @return 业务计数统计
     */
    List<BusinessCountStatisticsDTO> getBusinessCountStatisticsConfig(@Param("request") BusinessCountStatisticsRequest request);

    /**
     * 执行脚本，获取业务计数统计
     *
     * @param request 请求
     * @param dto 业务计数统计配置信息
     * @return 业务计数统计
     */
    List<BusinessCountStatisticsDTO> executeScript(@Param("request") BusinessCountStatisticsRequest request, @Param("dto") BusinessCountStatisticsDTO dto);

}
