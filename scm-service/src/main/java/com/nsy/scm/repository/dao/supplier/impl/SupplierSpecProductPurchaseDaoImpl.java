package com.nsy.scm.repository.dao.supplier.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.scm.constant.TrueOrFalseConstant;
import com.nsy.scm.repository.dao.supplier.SupplierSpecProductPurchaseDao;
import com.nsy.scm.repository.entity.SupplierSpecProductPurchaseEntity;
import com.nsy.scm.repository.sql.mapper.SupplierSpecProductPurchaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 供应商商品价格dao 实现类
 *
 * <AUTHOR>
 * @since 2022/8/16 17:50
 */
@Repository
public class SupplierSpecProductPurchaseDaoImpl extends ServiceImpl<SupplierSpecProductPurchaseMapper, SupplierSpecProductPurchaseEntity> implements SupplierSpecProductPurchaseDao {

    @Override
    public List<SupplierSpecProductPurchaseEntity> findBySupplierIdAndSpecIds(Integer supplierId, Collection<Integer> specIds) {
        LambdaQueryWrapper<SupplierSpecProductPurchaseEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierSpecProductPurchaseEntity::getIsDeleted, TrueOrFalseConstant.NOT_DELETED);
        queryWrapper.eq(SupplierSpecProductPurchaseEntity::getSupplierId, supplierId);
        if (CollectionUtils.isEmpty(specIds)) {
            queryWrapper.eq(SupplierSpecProductPurchaseEntity::getSpecId, -1);
        } else {
            queryWrapper.in(SupplierSpecProductPurchaseEntity::getSpecId, specIds);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public SupplierSpecProductPurchaseEntity getOneBySupplierIdAndSpecIdAndProductId(Integer supplierId, Integer specId, Integer productId) {
        LambdaQueryWrapper<SupplierSpecProductPurchaseEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierSpecProductPurchaseEntity::getIsDeleted, TrueOrFalseConstant.NOT_DELETED);
        queryWrapper.eq(SupplierSpecProductPurchaseEntity::getSupplierId, supplierId);
        queryWrapper.eq(SupplierSpecProductPurchaseEntity::getProductId, productId);
        queryWrapper.eq(SupplierSpecProductPurchaseEntity::getSpecId, specId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<SupplierSpecProductPurchaseEntity> findBySupplierIdInAndSpecIdIn(Collection<Integer> supplierIds, Collection<Integer> specIds) {
        if (CollectionUtils.isEmpty(supplierIds) || CollectionUtils.isEmpty(specIds)) {
            return Collections.emptyList();
        }
        return this.list(new LambdaQueryWrapper<SupplierSpecProductPurchaseEntity>()
                .in(SupplierSpecProductPurchaseEntity::getSupplierId, supplierIds)
                .in(SupplierSpecProductPurchaseEntity::getSpecId, specIds)
                .eq(SupplierSpecProductPurchaseEntity::getIsDeleted, TrueOrFalseConstant.NOT_DELETED));
    }

    @Override
    public List<SupplierSpecProductPurchaseEntity> findBySupplierIdInAndSkcIn(Collection<Integer> supplierIds, Collection<String> skcList) {
        if (CollectionUtils.isEmpty(supplierIds) || CollectionUtils.isEmpty(skcList)) {
            return Collections.emptyList();
        }
        return this.list(new LambdaQueryWrapper<SupplierSpecProductPurchaseEntity>()
                .in(SupplierSpecProductPurchaseEntity::getColorSku, skcList)
                .in(SupplierSpecProductPurchaseEntity::getSupplierId, supplierIds)
                .eq(SupplierSpecProductPurchaseEntity::getIsDeleted, TrueOrFalseConstant.NOT_DELETED));
    }
}
