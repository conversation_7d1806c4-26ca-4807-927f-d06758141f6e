package com.nsy.scm.repository.dao.material.detection.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.scm.dto.request.material.detection.MaterialDetectionTemplateDetailRequest;
import com.nsy.api.scm.dto.request.material.detection.MaterialDetectionTemplatePageRequest;
import com.nsy.api.scm.dto.response.material.detection.MaterialDetectionTemplateDto;
import com.nsy.api.scm.dto.response.material.detection.MaterialDetectionTemplateVailResponse;
import com.nsy.scm.repository.dao.material.detection.MaterialDetectionTemplateDao;
import com.nsy.scm.repository.entity.material.detection.MaterialDetectionTemplateEntity;
import com.nsy.scm.repository.sql.mapper.material.detection.MaterialDetectionTemplateMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 面料检测模板主表 DAO 实现类
 *
 * <AUTHOR>
 * @date 2024/06/05 18:04
 */
@Repository
public class MaterialDetectionTemplateDaoImpl extends ServiceImpl<MaterialDetectionTemplateMapper, MaterialDetectionTemplateEntity> implements MaterialDetectionTemplateDao {
    @Override
    public MaterialDetectionTemplateEntity getOneByRequest(MaterialDetectionTemplateDetailRequest request) {
        List<MaterialDetectionTemplateEntity> list = getListByRequest(request);
        if (CollectionUtil.isEmpty(list)) {
            return new MaterialDetectionTemplateEntity();
        }
        if (list.size() > 1) {
            throw new BusinessServiceException("数据超过1条");
        }
        return list.get(0);
    }

    @Override
    public List<MaterialDetectionTemplateEntity> getListByRequest(MaterialDetectionTemplateDetailRequest request) {
        return baseMapper.getByRequest(request);
    }

    @Override
    public List<MaterialDetectionTemplateVailResponse> uniqueVail(Set<Integer> templateIds, Integer isEnable) {
        if (isEnable == null || CollectionUtil.isEmpty(templateIds)) {
            return new ArrayList<>();
        }
        return baseMapper.uniqueVail(templateIds, isEnable);
    }

    @Override
    public List<MaterialDetectionTemplateVailResponse> uniqueTemplateNameVail(Set<Integer> templateIds, Integer isEnable) {
        if (isEnable == null || CollectionUtil.isEmpty(templateIds)) {
            return new ArrayList<>();
        }
        return baseMapper.uniqueTemplateNameVail(templateIds, isEnable);
    }

    @Override
    public String getLastOneMaxMaterialDetectionTemplateNo() {
        return baseMapper.getLastOneMaxMaterialDetectionTemplateNo();
    }

    @Override
    public IPage<MaterialDetectionTemplateEntity> iPage(Page<MaterialDetectionTemplatePageRequest> page, MaterialDetectionTemplatePageRequest request) {
        return baseMapper.iPage(page, request);
    }

    @Override
    public List<MaterialDetectionTemplateDto> queryList(MaterialDetectionTemplatePageRequest request) {
        return baseMapper.list(request);
    }
}
