package com.nsy.scm.repository.sql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nsy.api.scm.dto.response.product.SupplierSpecProductPurchaseDTO;
import com.nsy.permission.annatation.Permission;
import com.nsy.scm.business.response.supplier.SupplierProductMaxPriceUpdateDateDto;
import com.nsy.scm.common.annotation.IgnoreTenant;
import com.nsy.scm.repository.entity.SupplierSpecProductPurchaseEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SupplierSpecProductPurchaseMapper extends BaseMapper<SupplierSpecProductPurchaseEntity> {
    @IgnoreTenant
    List<SupplierSpecProductPurchaseDTO> listSkuMaxPriceByProductId(@Param("productId") Integer productId, @Param("notSupplilerId") Integer notSupplilerId);

    @IgnoreTenant
    List<SupplierSpecProductPurchaseDTO> listSkuMinPriceByProductId(@Param("productId") Integer productId, @Param("notSupplilerId") Integer notSupplilerId);
    @IgnoreTenant
    List<SupplierSpecProductPurchaseDTO> listMaxSkuPriceByProductId(@Param("productId") Integer product, @Param("notSupplilerId") Integer notSupplilerId, @Param("locations") List<String> locations);

    @IgnoreTenant
    List<SupplierSpecProductPurchaseDTO> listSkuPrice(@Param("productId") Integer productId, @Param("supplilerId") Integer supplilerId);
    @IgnoreTenant
    List<SupplierSpecProductPurchaseEntity> listSkcBySupplierProductIds(@Param("supplierProductIds") List<Integer> supplierProductIds);
    @IgnoreTenant
    List<SupplierSpecProductPurchaseEntity> listSkuBySupplierProductIds(@Param("supplierProductIds") List<Integer> supplierProductIds);
    @Permission
    List<SupplierSpecProductPurchaseEntity> findByProductIdAndSkuAndSkc(@Param("productId")Integer productId, @Param("skc") String skc, @Param("sku") String sku);
    /**
     * location通过参数传进去控制，普通用户查看需要过滤location，ka采购员特殊配置的用户不用过滤location
     */
    @IgnoreTenant
    @Permission
    List<SupplierSpecProductPurchaseEntity> findByProductIdWithPermission(@Param("productId") Integer productId, @Param("location") String location);
    /**
     * 财务看采购价数据，要忽略权限和区域限制，看所有工厂数据
     */
    @IgnoreTenant
    List<SupplierSpecProductPurchaseEntity> findByProductIdIgnoreTenant(@Param("productId") Integer productId);
    /**
     * 时颖报价tab：展示总部工厂 + 每个公司的虚拟工厂（目前只有广州99、厦门E01)
     */
    @IgnoreTenant
    List<SupplierSpecProductPurchaseEntity> getShiyingQuotationList(@Param("productId") Integer productId);

    List<SupplierProductMaxPriceUpdateDateDto> findMaxPriceUpdateDate(@Param("supplierIdList") List<Integer> supplierIdList, @Param("productIdList") List<Integer> productIdList);
}




