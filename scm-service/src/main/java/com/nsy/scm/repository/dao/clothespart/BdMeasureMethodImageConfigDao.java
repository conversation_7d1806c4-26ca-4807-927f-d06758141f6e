package com.nsy.scm.repository.dao.clothespart;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nsy.api.scm.dto.request.clothespart.PageQueryMeasureMethodImageConfigRequest;
import com.nsy.api.scm.dto.response.clothespart.BdMeasureMethodImageConfigResponse;
import com.nsy.scm.repository.entity.clothespart.BdMeasureMethodImageConfigEntity;

import java.util.List;

/**
 * 量法示意图配置DAO
 *
 * <AUTHOR>
 * @since 2024/7/11 15:33
 */
public interface BdMeasureMethodImageConfigDao extends IService<BdMeasureMethodImageConfigEntity> {

    IPage<BdMeasureMethodImageConfigResponse> pageQueryMeasureMethodImageConfig(PageQueryMeasureMethodImageConfigRequest request);

    BdMeasureMethodImageConfigResponse getMeasureMethodImageConfigResponse(Integer configId);

    BdMeasureMethodImageConfigEntity getById(Integer configId);

    void deleteBdMeasureMethodImageConfig(List<Integer> configIdList);
}
