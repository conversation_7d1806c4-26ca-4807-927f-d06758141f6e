package com.nsy.scm.repository.dao.clothespart.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.scm.dto.request.clothespart.PageQueryMeasureMethodImageConfigRequest;
import com.nsy.api.scm.dto.response.clothespart.BdMeasureMethodImageConfigResponse;
import com.nsy.scm.constant.TrueOrFalseConstant;
import com.nsy.scm.repository.dao.clothespart.BdMeasureMethodImageCategoryItemDao;
import com.nsy.scm.repository.dao.clothespart.BdMeasureMethodImageConfigDao;
import com.nsy.scm.repository.dao.clothespart.BdMeasureMethodImageItemDao;
import com.nsy.scm.repository.entity.clothespart.BdMeasureMethodImageCategoryItemEntity;
import com.nsy.scm.repository.entity.clothespart.BdMeasureMethodImageConfigEntity;
import com.nsy.scm.repository.entity.clothespart.BdMeasureMethodImageItemEntity;
import com.nsy.scm.repository.sql.mapper.clothespart.BdMeasureMethodImageConfigMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 服装部位信息表DAO
 *
 * <AUTHOR>
 * @since 2023/9/14 15:33
 */
@Service
public class BdMeasureMethodImageConfigDaoImpl extends ServiceImpl<BdMeasureMethodImageConfigMapper, BdMeasureMethodImageConfigEntity> implements BdMeasureMethodImageConfigDao {

    @Autowired
    private BdMeasureMethodImageItemDao bdMeasureMethodImageItemDao;

    @Autowired
    private BdMeasureMethodImageCategoryItemDao bdMeasureMethodImageCategoryItemDao;

    @Override
    public IPage<BdMeasureMethodImageConfigResponse> pageQueryMeasureMethodImageConfig(PageQueryMeasureMethodImageConfigRequest request) {
        if (request == null) {
            return null;
        }
        Page<BdMeasureMethodImageConfigResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<BdMeasureMethodImageConfigResponse> bdMeasureMethodImageConfigResponseIPage = baseMapper.pageQueryMeasureMethodImageConfig(page, request);
        List<BdMeasureMethodImageConfigResponse> records = bdMeasureMethodImageConfigResponseIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return bdMeasureMethodImageConfigResponseIPage;
        }
        List<Integer> configIds = records.stream().map(BdMeasureMethodImageConfigResponse::getConfigId).distinct().collect(Collectors.toList());
        List<BdMeasureMethodImageItemEntity> bdMeasureMethodImageItemEntities = bdMeasureMethodImageItemDao.listByConfigIds(configIds);
        if (CollectionUtils.isEmpty(bdMeasureMethodImageItemEntities)) {
            return bdMeasureMethodImageConfigResponseIPage;
        }
        records.forEach(record -> {
            List<BdMeasureMethodImageItemEntity> images = bdMeasureMethodImageItemEntities.stream().filter(f -> Objects.equals(f.getConfigId(), record.getConfigId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(images)) {
                return;
            }
            images.forEach(f -> {
                record.addImageList(BdMeasureMethodImageConfigResponse.buildBdMeasureMethodImage(f.getImageName(), f.getUrl()));
            });
        });
        return bdMeasureMethodImageConfigResponseIPage;
    }

    @Override
    public BdMeasureMethodImageConfigResponse getMeasureMethodImageConfigResponse(Integer configId) {
        if (configId == null || configId <= 0) {
            return null;
        }

        BdMeasureMethodImageConfigEntity configEntity = Optional.ofNullable(getById(configId)).orElseThrow(() -> new RuntimeException("未找到该量法示意图配置"));
        BdMeasureMethodImageConfigResponse response = new BdMeasureMethodImageConfigResponse();
        response.setConfigId(configId);
        List<BdMeasureMethodImageItemEntity> images = bdMeasureMethodImageItemDao.listByConfigIds(Lists.newArrayList(configEntity.getConfigId()));
        if (CollectionUtils.isNotEmpty(images)) {
            images.forEach(f -> {
                response.addImageList(BdMeasureMethodImageConfigResponse.buildBdMeasureMethodImage(f.getImageName(), f.getUrl()));
            });
        }
        List<BdMeasureMethodImageCategoryItemEntity> categoryItemEntities = bdMeasureMethodImageCategoryItemDao.listByConfigId(configId);
        if (CollectionUtils.isNotEmpty(categoryItemEntities)) {
            categoryItemEntities.forEach(f -> {
                response.addCategoryList(f.getCategoryId(), f.getCategoryName());
            });
        }

        return response;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteBdMeasureMethodImageConfig(List<Integer> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return;
        }
        this.baseMapper.deleteByIds(configIdList);
        bdMeasureMethodImageItemDao.deleteByConfigIds(configIdList);
        bdMeasureMethodImageCategoryItemDao.deleteByConfigIds(configIdList);
    }

    @Override
    public BdMeasureMethodImageConfigEntity getById(Integer configId) {
        if (configId == null || configId <= 0) {
            return null;
        }
        List<BdMeasureMethodImageConfigEntity> list = list(new LambdaQueryWrapper<BdMeasureMethodImageConfigEntity>()
                .in(BdMeasureMethodImageConfigEntity::getConfigId, configId)
                .eq(BdMeasureMethodImageConfigEntity::getIsDeleted, TrueOrFalseConstant.FALSE));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }
}
