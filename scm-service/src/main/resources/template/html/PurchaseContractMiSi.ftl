<!DOCTYPE html>
<html lang="en">
<head>
  <style type="text/css">
    body {
      height: 100%;
      font-family: "SimHei";
    }

    table {
      border-collapse: collapse;
    }

    th {
      border: 1px solid #000000;
      background: #dddddd;
      width: 10%;
      font-size: 11px;
    }

    td {
      border: 1px solid #000000;
      font-size: 10px;

    }

    #firstTable tr td {
      border: 0px;
      font-size: 17px;
    }
  </style>
</head>
<body>
<div>
  <table align="center" style="width: 100%;height: 100%;" id="firstTable">
    <tr style="height: 110px;">
      <td align="center" colspan="10" style="font-size: 30px;border: 0;">采购合同 ${orderNo!}</td>
    </tr>
    <tr>
      <td align="left" colspan="5">采购方: ${demanderName!}</td>
      <td align="left" colspan="5">供货方: ${supplierName!}</td>
    </tr>
    <tr>
      <td align="left" colspan="5">采购员: ${buyerName!}</td>
      <td align="left" colspan="5">订单日期: ${(activeDate?string("yyyy-MM-dd"))!''}</td>
    </tr>
    <tr>
      <td align="left" colspan="10">仓库地址: ${spaceAddress!}</td>
    </tr>
    <tr>
      <td align="left" colspan="10">单据备注: ${remark!}</td>
    </tr>
  </table>
  <div style="margin-top: 20px;"> 一、采购产品详情</div>
  <table id="secondTable" align="center" style="width: 100%;height: 100%;margin-top: 20px;">
    <tr>
      <th align="left">序号</th>
      <th align="left">图片</th>
      <th align="left">SPU</th>
      <th align="left">FNSKU</th>
      <th align="left">SKU</th>
      <th align="left">数量</th>
      <th align="left">单价(元/件)</th>
      <th align="left">金额(元)</th>
      <th align="left">预计到货日期</th>
      <th align="left">产品描述</th>
    </tr>
    <#list detail! as detail>
      <tr>
        <td align="left">${detail_index + 1}</td>
        <td align="left"><img style="width: 80px" src="${detail.imgUrl!}"></img></td>
        <td align="left">${detail.spu!}</td>
        <td align="left">${detail.fnSku!}</td>
        <td align="left">${detail.sku!}</td>
        <td align="left">${detail.purchaseQty!}</td>
        <td align="left">${detail.purchasePrice!}</td>
        <td align="left">${detail.amount!}</td>
        <td align="left">${(detail.expectDeliveryDate?string("yyyy-MM-dd"))!''}</td>
        <td align="left">${detail.packageName!}</td>
      </tr>
    </#list>
    <tr>
      <td align="left">合计:</td>
      <td align="left"></td>
      <td align="left"></td>
      <td align="left"></td>
      <td align="left"></td>
      <td align="left">${countNum!}</td>
      <td align="left"></td>
      <td align="left">${countAmount!}</td>
      <td align="left"></td>
      <td align="left"></td>
    </tr>
  </table>
  <div align="right" style="width: 100%;">
    ${countAmount!}     总金额(大写):    ${countAmountStr!}
  </div>
  <div style="width: 100%;">
    ${contractContent!}
  </div>
</div>
</body>
</html>