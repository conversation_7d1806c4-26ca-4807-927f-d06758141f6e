/*
* =============================================================================
* Designer: Administrator@XIEZHIJIE-PC
* Description: nsy_566
* Created: 2022/04/19 16:44:11
* ============================================================================= 
*/
-- 备份
create table if not exists nsy_backup.bd_technology_type_566 like nsy_scm.bd_technology_type;
INSERT INTO nsy_backup.bd_technology_type_566
select a.* from nsy_scm.bd_technology_type a left join
                nsy_backup.bd_technology_type_566 b on a.technology_type_id = b.technology_type_id
where b.technology_type_id is null;


update nsy_scm.bd_technology_type set technology_type_name = '工艺分类' where technology_type_id = 1;


-- 更新父节点名称
UPDATE nsy_scm.bd_technology_type a inner join nsy_scm.bd_technology_type b on a.parent_technology_type_id = b.technology_type_id
    SET a.parent_technology_type_name = b.technology_type_name
where a.parent_technology_type_id > 0;




