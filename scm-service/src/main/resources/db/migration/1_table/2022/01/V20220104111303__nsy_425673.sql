/*
* =============================================================================
* Designer: Administrator@XIEZHIJIE-PC
* Description: nsy_425673
* Created: 2022/01/04 11:13:03
* =============================================================================
*/

-- 准入物料数据备份
create table if not exists nsy_backup.access_apply_material_425673 (
    access_apply_id int(11) NOT NULL ,
    fabricate_way varchar(30) comment '制造方法');
delete from nsy_backup.access_apply_material_425673;
insert into nsy_backup.access_apply_material_425673(access_apply_id, fabricate_way)
select access_apply_id, fabricate_way from access_apply_material;


-- 物料数据备份
create table if not exists nsy_backup.material_425673 (
    material_id int(11) NOT NULL ,
    fabricate_way varchar(30) comment '制造方法');
delete from nsy_backup.material_425673;
insert into nsy_backup.material_425673(material_id, fabricate_way)
select material_id, fabricate_way from material;

-- 准入物料
-- 牛仔修改为梭织
update access_apply_material set fabricate_way = 'WOVEN' where fabricate_way = 'COWBOY';
-- 织造方法修改为梭织
update access_apply_material set fabricate_way = 'WOVEN' where fabricate_way = 'WEAVING_METHOD';
-- 毛衣改修成针织
update access_apply_material set fabricate_way = 'KNITTING' where fabricate_way = 'SWEATER';

-- 物料
-- 牛仔修改为梭织
update access_apply_material set fabricate_way = 'WOVEN' where fabricate_way = 'COWBOY';
-- 织造方法修改为梭织
update access_apply_material set fabricate_way = 'WOVEN' where fabricate_way = 'WEAVING_METHOD';
-- 毛衣改修成针织
update access_apply_material set fabricate_way = 'KNITTING' where fabricate_way = 'SWEATER';
