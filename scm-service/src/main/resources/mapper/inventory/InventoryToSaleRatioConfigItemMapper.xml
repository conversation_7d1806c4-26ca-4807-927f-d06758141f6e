<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.inventory.InventoryToSaleRatioConfigItemMapper">
  <resultMap id="BaseResultMap" type="com.nsy.scm.repository.entity.inventory.InventoryToSaleRatioConfigItemEntity">
    <id column="config_item_id" jdbcType="INTEGER" property="configItemId" />
    <result column="config_id" jdbcType="INTEGER" property="configId" />
    <result column="inventory_to_sale_ratio1" jdbcType="INTEGER" property="inventoryToSaleRatio1" />
    <result column="inventory_to_sale_ratio2" jdbcType="INTEGER" property="inventoryToSaleRatio2" />
    <result column="inventory_to_sale_ratio3" jdbcType="INTEGER" property="inventoryToSaleRatio3" />
    <result column="inventory_to_sale_ratio4" jdbcType="INTEGER" property="inventoryToSaleRatio4" />
    <result column="inventory_to_sale_ratio5" jdbcType="INTEGER" property="inventoryToSaleRatio5" />
    <result column="inventory_to_sale_ratio6" jdbcType="INTEGER" property="inventoryToSaleRatio6" />
    <result column="inventory_to_sale_ratio7" jdbcType="INTEGER" property="inventoryToSaleRatio7" />
    <result column="inventory_to_sale_ratio8" jdbcType="INTEGER" property="inventoryToSaleRatio8" />
    <result column="inventory_to_sale_ratio9" jdbcType="INTEGER" property="inventoryToSaleRatio9" />
    <result column="inventory_to_sale_ratio10" jdbcType="INTEGER" property="inventoryToSaleRatio10" />
    <result column="inventory_to_sale_ratio11" jdbcType="INTEGER" property="inventoryToSaleRatio11" />
    <result column="inventory_to_sale_ratio12" jdbcType="INTEGER" property="inventoryToSaleRatio12" />
    <result column="last_inventory_to_sale_ratio1" jdbcType="INTEGER" property="lastInventoryToSaleRatio1" />
    <result column="last_inventory_to_sale_ratio2" jdbcType="INTEGER" property="lastInventoryToSaleRatio2" />
    <result column="last_inventory_to_sale_ratio3" jdbcType="INTEGER" property="lastInventoryToSaleRatio3" />
    <result column="last_inventory_to_sale_ratio4" jdbcType="INTEGER" property="lastInventoryToSaleRatio4" />
    <result column="last_inventory_to_sale_ratio5" jdbcType="INTEGER" property="lastInventoryToSaleRatio5" />
    <result column="last_inventory_to_sale_ratio6" jdbcType="INTEGER" property="lastInventoryToSaleRatio6" />
    <result column="last_inventory_to_sale_ratio7" jdbcType="INTEGER" property="lastInventoryToSaleRatio7" />
    <result column="last_inventory_to_sale_ratio8" jdbcType="INTEGER" property="lastInventoryToSaleRatio8" />
    <result column="last_inventory_to_sale_ratio9" jdbcType="INTEGER" property="lastInventoryToSaleRatio9" />
    <result column="last_inventory_to_sale_ratio10" jdbcType="INTEGER" property="lastInventoryToSaleRatio10" />
    <result column="last_inventory_to_sale_ratio11" jdbcType="INTEGER" property="lastInventoryToSaleRatio11" />
    <result column="last_inventory_to_sale_ratio12" jdbcType="INTEGER" property="lastInventoryToSaleRatio12" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="VERSION" jdbcType="INTEGER" property="version" />
  </resultMap>


  <update id="deleteByConfigId">
    update nsy_scm.inventory_to_sale_ratio_config_item
    set is_delete = 1
    where config_id = #{configId}
  </update>

</mapper>