<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nsy.scm.repository.sql.mapper.ProcessBatchMapper">
    <select id="countByStatus" resultType="com.nsy.scm.business.response.process.ProcessBatchStatusResponse">
        SELECT
            status,count(*) as qty
        FROM process_batch
        where
            location = #{location}
        GROUP BY status
    </select>

    <sql id="selectWhere">
        <where>
            <if test="request.batchId!= null">
                AND pb.batch_id = #{request.batchId}
            </if>
            <if test="request.customTechnology!= null and request.customTechnology!= ''">
                AND pb.custom_technology = #{request.customTechnology}
            </if>
            <if test="request.createStartDate != null">
                and pb.create_date &gt;= #{request.createStartDate}
            </if>
            <if test="request.createEndDate != null">
                and pb.create_date &lt;= #{request.createEndDate}
            </if>
            <if test="(request.baseSku!= null and request.baseSku!='') or (request.customSku!= null and request.customSku!='')">
                and EXISTS (select * from process_batch_plan pbp left join process_plan_item ppi on pbp.plan_id
                = ppi.plan_id where pbp.batch_id = pb.batch_id
                <if test="request.baseSku!= null and request.baseSku!=''">
                    and ppi.base_sku like concat(#{request.baseSku}, '%')
                </if>
                <if test="request.customSku!= null and request.customSku!=''">
                    and ppi.custom_sku like concat(#{request.customSku}, '%')
                </if>
                )
            </if>
            <if test="request.customType!= null and request.customType!=''">
                AND pb.custom_type = #{request.customType}
            </if>
            <if test="request.status != null and request.status.size > 0 ">
                and pb.status in
                <foreach collection="request.status" item="status" separator="," open="(" close=")">
                    #{status}
                </foreach>
            </if>
            <if test="request.workspace!= null and request.workspace!=''">
                AND pb.workspace = #{request.workspace}
            </if>
            <if test="request.pickingType!= null and request.pickingType!=''">
                AND pb.picking_type = #{request.pickingType}
            </if>
            <if test="request.location!= null and request.location!=''">
                AND pb.location = #{request.location}
            </if>
            <if test="request.taskStatus!= null and request.taskStatus!=''">
                AND pt.status = #{request.taskStatus}
            </if>
        </where>
    </sql>

    <select id="findPage" resultType="com.nsy.scm.repository.entity.ProcessBatchEntity">
        SELECT
            pb.*
        FROM
            process_batch pb
        <include refid="selectWhere"></include>
        order by pb.create_date desc
    </select>

    <select id="sumTotalPlanQty" resultType="java.lang.Integer">
        SELECT
            ifnull(sum(ppi.plan_qty), 0) as plan_qty
        FROM
            process_batch pb
                LEFT JOIN process_batch_plan pbp ON pb.batch_id = pbp.batch_id
                LEFT JOIN process_plan_item ppi ON pbp.plan_id = ppi.plan_id
        <include refid="selectWhere"></include>
    </select>

    <select id="sumTotalCompleteQty" resultType="java.lang.Integer">
        SELECT
            ifnull(sum(pti.complete_qty), 0) as complete_qty
        FROM
            process_batch pb
                LEFT JOIN process_batch_plan pbp ON pb.batch_id = pbp.batch_id
                LEFT JOIN process_plan_item ppi ON pbp.plan_id = ppi.plan_id
                LEFT JOIN process_task_item pti ON ppi.plan_item_id = pti.plan_item_id
                left join process_task pt on pt.process_task_id = pti.process_task_id
        <include refid="selectWhere"></include>
    </select>

    <select id="sumTask" resultType="com.nsy.scm.business.domain.process.ProcessBatchSumTaskBo">
        SELECT
            pb.batch_id,
            ifnull( sum( pti.pick_qty ), 0 ) as pick_qty,
            ifnull( sum( pti.complete_qty ), 0 ) as complete_qty,
            ifnull( sum( pti.defective_qty ), 0 ) as defective_qty
        FROM
            process_batch pb
                LEFT JOIN process_task pt ON pb.batch_id = pt.batch_id
                LEFT JOIN process_task_item pti ON pt.process_task_id = pti.process_task_id
        WHERE
            pb.batch_id in
            <foreach collection="batchIdList" item="batchId" separator="," open="(" close=")">
                #{batchId}
            </foreach>
        GROUP BY
            pb.batch_id
    </select>

    <select id="sumPlan" resultType="com.nsy.scm.business.domain.process.ProcessBatchSumPlanBo">
        SELECT
            pb.batch_id,
            ifnull( sum( ppi.plan_qty ), 0 ) as plan_qty
        FROM
            process_batch pb
                LEFT JOIN process_batch_plan pbp ON pb.batch_id = pbp.batch_id
                LEFT JOIN process_plan_item ppi ON pbp.plan_id = ppi.plan_id
        WHERE
            pb.batch_id in
            <foreach collection="batchIdList" item="batchId" separator="," open="(" close=")">
                #{batchId}
            </foreach>
        GROUP BY
            pb.batch_id
    </select>
</mapper>
