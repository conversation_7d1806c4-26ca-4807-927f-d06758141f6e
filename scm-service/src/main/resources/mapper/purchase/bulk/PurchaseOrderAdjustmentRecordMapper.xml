<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.purchase.bulk.PurchaseOrderAdjustmentRecordMapper">
    <sql id="pageForMaterialPendingWithPermissionOfBulkPurchaseWhere">
        where 1 = 1
        <if test="query.isMaterialOrder != null">
            and po.is_material_order = #{query.isMaterialOrder}
        </if>
        <if test="query.orderAdjustmentRecordIds != null and query.orderAdjustmentRecordIds.size() > 0">
            and poar.order_adjustment_record_id in
            <foreach collection="query.orderAdjustmentRecordIds" item="orderAdjustmentRecordId" open="(" close=")" separator=",">
                #{orderAdjustmentRecordId}
            </foreach>
        </if>
        <if test="query.materialPurchaseOrderNo != null and query.materialPurchaseOrderNo != ''">
            and mpo.purchase_order_no like concat(#{query.materialPurchaseOrderNo}, '%')
        </if>
        <if test="query.materialPurchaseOrderStatus != null">
            and mpo.status = #{query.materialPurchaseOrderStatus}
        </if>
        <if test="query.materialSupplierId != null">
            and mpo.supplier_id = #{query.materialSupplierId}
        </if>
        <if test="query.fabricMerchandiserId != null">
            and mpo.fabric_merchandiser_id = #{query.fabricMerchandiserId}
        </if>
        <if test="query.estimateSubmitStartDate != null">
            and rece.estimate_submit_date >= #{query.estimateSubmitStartDate}
        </if>
        <if test="query.responsibleParty != null">
            and #{query.estimateSubmitStartDate} >= rece.estimate_submit_date
        </if>
        <if test="query.responsibleParty != null and query.responsibleParty != ''">
            and poar.responsible_party = #{query.responsibleParty}
        </if>
        <if test="query.orderNo != null and query.orderNo != ''">
            and po.order_no = #{query.orderNo}
        </if>
        <if test="query.supplierId != null and query.supplierId != 0">
            and po.supplier_id = #{query.supplierId}
        </if>
        <if test="query.supplierIds != null and query.supplierIds.size() > 0">
            and po.supplier_id in
            <foreach collection="query.supplierIds" item="supplierId" open="(" separator="," close=")">
                #{supplierId}
            </foreach>
        </if>
        <if test="query.buyerUserId != null and query.buyerUserId != 0">
            and po.buyer_user_id = #{query.buyerUserId}
        </if>
        <if test="query.spu != null and query.spu != ''">
            and (poar.spu like CONCAT(#{query.spu}, '%')
            <if test="query.spuAutoMatchList != null and query.spuAutoMatchList.size() > 0">
                OR
                <foreach collection="query.spuAutoMatchList" item="spu" open="(" close=")" separator="OR">
                    poar.spu like concat(#{spu}, '%')
                </foreach>
            </if>
            )
        </if>
        <if test="query.skc != null and query.skc != ''">
            and (poar.skc like CONCAT(#{query.skc}, '%')
            <if test="query.skcAutoMatchList != null and query.skcAutoMatchList.size() > 0">
                OR
                <foreach collection="query.skcAutoMatchList" item="skc" open="(" close=")" separator="OR">
                    poar.skc like concat(#{skc}, '%')
                </foreach>
            </if>
            )
        </if>
        <if test="query.sku != null and query.sku != ''">
            and (poar.sku like CONCAT(#{query.sku}, '%')
            <if test="query.skuAutoMatchList != null and query.skuAutoMatchList.size() > 0">
                OR
                <foreach collection="query.skuAutoMatchList" item="sku" open="(" close=")" separator="OR">
                    poar.sku like concat(#{sku}, '%')
                </foreach>
            </if>
            )
        </if>
        <if test="query.adjustmentType != null and query.adjustmentType != 0">
            and poar.adjustment_type = #{query.adjustmentType}
        </if>
        <if test="query.adjustmentTypeList != null and query.adjustmentTypeList.size() > 0">
            and poar.adjustment_type in
            <foreach collection="query.adjustmentTypeList" item="adjustmentType" open="(" separator="," close=")">
                #{adjustmentType}
            </foreach>
        </if>
        <if test="query.status != null and query.status != 0">
            and poar.status = #{query.status}
        </if>
        <if test="query.statusList != null and query.statusList.size() > 0">
            and poar.status in
            <foreach collection="query.statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="query.orderItemIds != null and query.orderItemIds.size() > 0">
            and poar.order_item_id in
            <foreach collection="query.orderItemIds" item="orderItemId" open="(" separator="," close=")">
                #{orderItemId}
            </foreach>
        </if>
        <if test="query.scmCategoryIdList != null and query.scmCategoryIdList.size() > 0">
            and poi.scm_category_id in
            <foreach collection="query.scmCategoryIdList" item="scmCategoryId" open="(" close=")" separator=",">
                #{scmCategoryId}
            </foreach>
        </if>
        <if test="query.brandId != null">
            and poi.brand_id = #{query.brandId}
        </if>
        <if test="query.orderLabel != null and query.orderLabel != ''">
            AND EXISTS (SELECT 1 FROM purchase_order_item_label oil WHERE oil.order_item_id = poi.order_item_id AND
            oil.label = #{query.orderLabel} and oil.type = 1)
        </if>
        <if test="query.expectDeliveryStartedDate != null">
            and poar.expect_delivery_date >= #{query.expectDeliveryStartedDate}
        </if>
        <if test="query.expectDeliveryEndedDate != null">
            and #{query.expectDeliveryEndedDate} > poar.expect_delivery_date
        </if>
        <if test="query.createStartedDate != null">
            and poar.create_date >= #{query.createStartedDate}
        </if>
        <if test="query.createEndedDate != null">
            and #{query.createEndedDate} > poar.create_date
        </if>
        <if test="query.selectIdList != null and query.selectIdList.size() > 0">
            and poar.order_adjustment_record_id in
            <foreach collection="query.selectIdList" item="ids" open="(" close=")" separator=",">
                #{ids}
            </foreach>
        </if>
        <if test="query.updateStartedDate != null">
            and poar.update_date >= #{query.updateStartedDate}
        </if>
        <if test="query.updateEndedDate != null">
            and #{query.updateEndedDate} > poar.update_date
        </if>
        <if test="query.auditStartedDate != null">
            and poar.audit_time >= #{query.auditStartedDate}
        </if>
        <if test="query.auditEndedDate != null">
            and #{query.auditEndedDate} > poar.audit_time
        </if>
        <if test="query.applyNo != null and query.applyNo != ''">
            AND poar.order_item_id IN ( SELECT pp.order_item_id FROM purchase_plan pp WHERE pp.apply_no =
            #{query.applyNo})
        </if>
        <if test="query.affiliateDeptId != null">
            AND s.affiliate_dept_id = #{query.affiliateDeptId}
        </if>
        <if test="query.contactPurchaserEmpId != null">
            AND s.buyer_user_id = #{query.contactPurchaserEmpId}
        </if>
    </sql>
    <select id="pageWithPermissionOfBulkPurchase"
            resultType="com.nsy.api.scm.dto.domain.purchase.bulk.PurchaseOrderAdjustmentRecordDto">
        select po.active_date, poar.remark, poar.responsible_party,ps.image_url as imageUrl, po.order_no as orderNo,
        po.supplier_id as supplierId, po.buyer_user_id as buyerUserId, po.buyer_name as buyerName,
        poar.skc as skc, poar.sku as sku, poar.status as status, poar.previous_status as previousStatus,
        poi.scm_category_id as scmCategoryId,
        poi.scm_category_name as scmCategoryName, poi.purchase_qty as purchaseQty, poar.expect_delivery_date as
        expectDeliveryDate,
        poar.adjustment_type as adjustmentType, poar.delay_time as delayTime,poar.delay_day as delayDay, poar.audit_delay_time as auditDelayTime,
        poar.apply_cancel_qty as applyCancelQty, sspd.cancelled_quantity as cancelQty,
        poar.apply_reason as applyReason, poar.create_by as createBy, poar.create_date as createDate, poar.update_by as
        updateBy,poar.audit_delay_day,
        poar.update_date as updateDate, poar.audit_time as auditTime, poar.handle_results as handleResults,
        poar.order_adjustment_record_id as orderAdjustmentRecordId,
        poar.order_id as orderId, poar.order_item_id as orderItemId, poar.apply_qty, s.supplier_code,
        poar.receive_order_expect_delivery_date,
        po.is_material_order,
        poar.material_audit_responsible_party,
        poar.material_audit_delay_day,
        poar.material_audit_by,
        poar.material_audit_remark,
        poar.material_audit_time,
        poar.audit_by,
        poar.audit_remark,
        poi.brand_name,
        s.affiliate_dept_name,
        s.buyer_user_id as contactPurchaserEmpName
    from purchase_order_adjustment_record poar
    inner join purchase_order_item poi on poi.order_item_id = poar.order_item_id
    inner join purchase_order po on po.order_id = poar.order_id
    inner join product_spec ps on ps.spec_id = poi.spec_id
    inner join supplier s on po.supplier_id = s.supplier_id
    <if test="query.reasonList != null and query.reasonList.size() > 0">
        inner join purchase_order_adjustment_record_reason r
        on poar.order_adjustment_record_id = r.order_adjustment_record_id and  r.reason_id in
        <foreach collection="query.reasonList" item="reason" open="(" separator="," close=")">
            #{reason}
        </foreach>
    </if>
    left join sync_supplier_produce_data sspd on poi.order_item_id = sspd.order_item_id
    where 1 = 1
    <if test="query.isMaterialOrder != null">
        and po.is_material_order = #{query.isMaterialOrder}
    </if>
    <if test="query.responsibleParty != null and query.responsibleParty != ''">
        and poar.responsible_party = #{query.responsibleParty}
    </if>
    <if test="query.orderNo != null and query.orderNo != ''">
        and po.order_no = #{query.orderNo}
    </if>
    <if test="query.supplierId != null and query.supplierId != 0">
        and po.supplier_id = #{query.supplierId}
    </if>
    <if test="query.supplierIds != null and query.supplierIds.size() > 0">
        and po.supplier_id in
        <foreach collection="query.supplierIds" item="supplierId" open="(" separator="," close=")">
            #{supplierId}
        </foreach>
    </if>
    <if test="query.buyerUserId != null and query.buyerUserId != 0">
        and po.buyer_user_id = #{query.buyerUserId}
    </if>
    <if test="query.spu != null and query.spu != ''">
        and (poar.spu like CONCAT(#{query.spu}, '%')
        <if test="query.spuAutoMatchList != null and query.spuAutoMatchList.size() > 0">
            OR <foreach collection="query.spuAutoMatchList" item="spu" open="(" close=")" separator="OR">
            poar.spu like concat(#{spu}, '%')
        </foreach>
        </if>)
    </if>
    <if test="query.skc != null and query.skc != ''">
        and (poar.skc like CONCAT(#{query.skc}, '%')
        <if test="query.skcAutoMatchList != null and query.skcAutoMatchList.size() > 0">
            OR <foreach collection="query.skcAutoMatchList" item="skc" open="(" close=")" separator="OR">
            poar.skc like concat(#{skc}, '%')
        </foreach>
        </if>)
    </if>
    <if test="query.sku != null and query.sku != ''">
        and (poar.sku like CONCAT(#{query.sku}, '%')
        <if test="query.skuAutoMatchList != null and query.skuAutoMatchList.size() > 0">
            OR <foreach collection="query.skuAutoMatchList" item="sku" open="(" close=")" separator="OR">
            poar.sku like concat(#{sku}, '%')
        </foreach>
        </if>)
    </if>
    <if test="query.adjustmentType != null and query.adjustmentType != 0">
        and poar.adjustment_type = #{query.adjustmentType}
    </if>
    <if test="query.adjustmentTypeList != null and query.adjustmentTypeList.size() > 0">
        and poar.adjustment_type in
        <foreach collection="query.adjustmentTypeList" item="adjustmentType" open="(" separator="," close=")">
            #{adjustmentType}
        </foreach>
    </if>
    <if test="query.status != null and query.status != 0">
        and poar.status = #{query.status}
    </if>
    <if test="query.statusList != null and query.statusList.size() > 0">
        and poar.status in
        <foreach collection="query.statusList" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
    </if>
    <if test="query.orderItemIds != null and query.orderItemIds.size() > 0">
        and poar.order_item_id in
        <foreach collection="query.orderItemIds" item="orderItemId" open="(" separator="," close=")">
            #{orderItemId}
        </foreach>
    </if>
    <if test="query.scmCategoryIdList != null and query.scmCategoryIdList.size() > 0">
        and poi.scm_category_id in
        <foreach collection="query.scmCategoryIdList" item="scmCategoryId" open="(" close=")" separator=",">
            #{scmCategoryId}
        </foreach>
    </if>
    <if test="query.brandId != null">
        and poi.brand_id = #{query.brandId}
    </if>
    <if test="query.orderLabel != null and query.orderLabel != ''">
        AND EXISTS (SELECT 1 FROM purchase_order_item_label oil WHERE oil.order_item_id = poi.order_item_id AND oil.label = #{query.orderLabel} and oil.type = 1)
    </if>
    <if test="query.expectDeliveryStartedDate != null">
        and poar.expect_delivery_date >= #{query.expectDeliveryStartedDate}
    </if>
    <if test="query.expectDeliveryEndedDate != null">
        and #{query.expectDeliveryEndedDate} > poar.expect_delivery_date
    </if>
    <if test="query.createStartedDate != null">
        and poar.create_date >= #{query.createStartedDate}
    </if>
    <if test="query.createEndedDate != null">
        and #{query.createEndedDate} > poar.create_date
    </if>
    <if test="query.selectIdList != null and query.selectIdList.size() > 0">
        and poar.order_adjustment_record_id in
        <foreach collection="query.selectIdList" item="ids" open="(" close=")" separator=",">
            #{ids}
        </foreach>
    </if>
    <if test="query.updateStartedDate != null">
        and poar.update_date >= #{query.updateStartedDate}
    </if>
    <if test="query.updateEndedDate != null">
        and #{query.updateEndedDate} > poar.update_date
    </if>
    <if test="query.auditStartedDate != null">
        and poar.audit_time >= #{query.auditStartedDate}
    </if>
    <if test="query.auditEndedDate != null">
        and #{query.auditEndedDate} > poar.audit_time
    </if>
    <if test="query.applyNo != null and query.applyNo != ''">
    AND poar.order_item_id IN ( SELECT pp.order_item_id FROM purchase_plan pp WHERE pp.apply_no = #{query.applyNo})
    </if>
    <if test="query.affiliateDeptId != null">
        AND s.affiliate_dept_id = #{query.affiliateDeptId}
    </if>
    <if test="query.contactPurchaserEmpId != null">
        AND s.buyer_user_id = #{query.contactPurchaserEmpId}
    </if>
    <if test="query.delayDay != null">
        AND poar.delay_day >= #{query.delayDay}
    </if>
    group by poar.order_adjustment_record_id
    ORDER BY poar.order_adjustment_record_id desc
    </select>

    <select id="downloadForMaterialPendingWithPermissionOfBulkPurchase"
            resultType="com.nsy.api.scm.dto.domain.purchase.bulk.PurchaseOrderAdjustmentRecordDownLoadDto">
        SELECT
            po.order_no,
            s.supplier_code,
            s.buyer_name,
            poar.skc,
            poar.sku,
            poi.purchase_qty,
            poar.adjustment_type,
            poi.brand_name,
            poar.status as adjustment_record_status,
            poar.receive_order_expect_delivery_date,
            poar.delay_time,
            poar.create_date,
            poar.apply_reason,
            poar.remark,
            poar.material_audit_responsible_party,
            poar.material_audit_by,
            poar.material_audit_time,
            poar.responsible_party,
            poar.responsible_party,
            poar.material_audit_delay_day,
            poar.material_audit_remark,
            poar.audit_by,
            poar.audit_time,
            poar.audit_remark,
            materialSupplier.supplier_short_name,
            mpoi.purchase_order_item_id,
            mpoi.supplier_info_id,
            mpoi.supplier_material_name,
            mpoi.color_number,
            mpoi.purchase_quantity,
            rece.estimate_deliver_date,
            rece.estimate_submit_date,
            mpoi.status as material_purchase_order_item_status
        FROM
            purchase_order_adjustment_record poar
                INNER JOIN purchase_order_item poi ON poi.order_item_id = poar.order_item_id
                INNER JOIN purchase_order po ON po.order_id = poar.order_id
                INNER JOIN supplier s ON po.supplier_id = s.supplier_id
                LEFT JOIN material_purchase_order_item_bulk_order_detail bod ON poar.order_item_id = bod.bulk_order_item_id
                LEFT JOIN material_purchase_order_item mpoi ON bod.material_order_item_id = mpoi.purchase_order_item_id AND mpoi.is_delete = 0
                LEFT JOIN material_purchase_order mpo ON mpoi.purchase_order_id = mpo.purchase_order_id AND mpo.is_delete = 0
                LEFT JOIN material_receive_info rece ON mpoi.purchase_order_item_id = rece.purchase_order_item_id AND rece.is_delete = 0
                left join supplier materialSupplier on mpo.supplier_id = materialSupplier.supplier_id
        <include refid="pageForMaterialPendingWithPermissionOfBulkPurchaseWhere"></include>
    </select>
    <select id="selectByOrderItemIdInAndAdjustmentTypeInAndStatus"
            resultType="com.nsy.scm.repository.entity.purchase.bulk.PurchaseOrderAdjustmentRecordEntity">
        select poar.* from purchase_order_adjustment_record poar
        <where>
            <if test="orderItemIds != null and orderItemIds.size > 0">
                poar.order_item_id in
                <foreach collection="orderItemIds" item="orderItemId" open="(" separator="," close=")">
                    #{orderItemId}
                </foreach>
            </if>
            <if test="adjustmentTypes != null and adjustmentTypes.size > 0">
                and poar.adjustment_type in
                <foreach collection="adjustmentTypes" item="adjustmentType" open="(" separator="," close=")">
                    #{adjustmentType}
                </foreach>
            </if>
            <if test="status != null and status != ''">
                and poar.status = #{status}
            </if>
        </where>
    </select>

    <select id="getTabs" resultType="com.nsy.scm.business.response.base.StatusTabResponse">
        SELECT poar.`status` as status,
               count(1) num
        FROM purchase_order_adjustment_record poar FORCE INDEX(idx_status)
        inner join purchase_order_item poi on poi.order_item_id = poar.order_item_id
        inner join purchase_order po on po.order_id = poar.order_id
        inner join supplier s on po.supplier_id = s.supplier_id
        where poar.status = 1
        GROUP BY poar.`status`
    </select>

    <select id="queryLastedAdjustmentRecordByOrderNoAndSkc"
            resultType="com.nsy.api.scm.dto.domain.purchase.bulk.LastedAuditAdjustmentRecordDto">
        SELECT po.order_no as orderNo, poar.skc as skc, SUBSTRING_INDEX(GROUP_CONCAT(DISTINCT poar.order_adjustment_record_id ORDER BY poar.`audit_time` DESC, poar.order_adjustment_record_id DESC), ',', 1) AS idStr
        from purchase_order_adjustment_record poar
        left JOIN purchase_order po on poar.order_id = po.order_id
        where
        <if test="query.orderNoList != null and query.orderNoList.size() > 0">
            po.order_no in
            <foreach collection="query.orderNoList" item="orderNo" open="(" separator="," close=")">
                #{orderNo}
            </foreach>
        </if>
        <if test="query.skcList != null and query.skcList.size() > 0">
            and poar.skc in
            <foreach collection="query.skcList" item="skc" open="(" separator="," close=")">
                #{skc}
            </foreach>
        </if>
        <if test="query.adjustmentType != null and query.adjustmentType != 0">
            and poar.adjustment_type = #{query.adjustmentType}
        </if>
        group by po.order_no, poar.skc
    </select>

    <select id="existsByOrderItemIdInAndAdjustmentTypeInAndStatusInAndNotEqualId" resultType="boolean">
        select count(poar.order_adjustment_record_id) > 0
        from purchase_order_adjustment_record poar
        where poar.order_item_id in
        <foreach collection="orderItemIds" item="orderItemId" open="(" separator="," close=")">
            #{orderItemId}
        </foreach>
        and poar.adjustment_type in
        <foreach collection="adjustmentTypes" item="adjustmentType" open="(" separator="," close=")">
            #{adjustmentType}
        </foreach>
        and poar.order_adjustment_record_id != #{orderAdjustmentRecordId}
        and poar.status in
        <foreach collection="statusList" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
    </select>
    <select id="queryBySendAuditDingDingMsg"
            resultType="com.nsy.api.scm.dto.domain.purchase.bulk.QueryBySendAuditDingDingMsgDto">
        select
        poar.order_adjustment_record_id,
        poar.order_id,
        poar.order_item_id,
        po.order_no,
        po.supplier_id,
        s.supplier_name,
        po.buyer_user_id as buyerUserId, po.buyer_name as buyerName,
        poar.skc as skc, poar.sku as sku,
        poi.purchase_qty,poar.apply_reason,poar.handle_results, poar.remark, poar.responsible_party, poar.apply_qty
        from purchase_order_adjustment_record poar
        inner join purchase_order_item poi on poi.order_item_id = poar.order_item_id
        inner join purchase_order po on po.order_id = poar.order_id
        inner join product_spec ps on ps.spec_id = poi.spec_id
        inner join supplier s on po.supplier_id = s.supplier_id
        where poar.order_adjustment_record_id = #{orderAdjustmentRecordId}
    </select>
    <select id="pageForMaterialPendingWithPermissionOfBulkPurchase" resultType="java.lang.String">
        select
        group_concat( distinct poar.order_adjustment_record_id)
        from purchase_order_adjustment_record poar
        inner join purchase_order_item poi on poi.order_item_id = poar.order_item_id
        inner join purchase_order po on po.order_id = poar.order_id
        inner join product_spec ps on ps.spec_id = poi.spec_id
        inner join supplier s on po.supplier_id = s.supplier_id
        left join material_purchase_order_item_bulk_order_detail bod on poar.order_item_id = bod.bulk_order_item_id
        left join material_purchase_order_item mpoi on bod.material_order_item_id = mpoi.purchase_order_item_id and mpoi.is_delete = 0
        left join material_purchase_order mpo on mpoi.purchase_order_id = mpo.purchase_order_id and mpo.is_delete = 0
        left join material_receive_info rece on mpoi.purchase_order_item_id = rece.purchase_order_item_id and rece.is_delete = 0
        <include refid="pageForMaterialPendingWithPermissionOfBulkPurchaseWhere"></include>
        group by po.order_no,poar.skc
        order by poar.create_date asc
    </select>
    <select id="listForMaterialPendingWithPermissionOfBulkPurchase"
            resultType="com.nsy.api.scm.dto.domain.purchase.bulk.PurchaseOrderAdjustmentRecordDto">
        select po.active_date, poar.remark, poar.responsible_party,ps.image_url as imageUrl, po.order_no as orderNo, po.supplier_id as supplierId, po.buyer_user_id as buyerUserId, po.buyer_name as buyerName,
        poar.skc as skc, poar.sku as sku, poar.status as status, poar.previous_status as previousStatus, poi.scm_category_id as scmCategoryId,
        poi.scm_category_name as scmCategoryName, poi.purchase_qty as purchaseQty, poar.expect_delivery_date as expectDeliveryDate,
        poar.adjustment_type as adjustmentType, poar.delay_time as delayTime, poar.audit_delay_time as auditDelayTime, poar.apply_cancel_qty as applyCancelQty, poar.cancel_qty as cancelQty,
        poar.apply_reason as applyReason, poar.create_by as createBy, poar.create_date as createDate, poar.update_by as updateBy,
        poar.update_date as updateDate, poar.audit_time as auditTime, poar.handle_results as handleResults, poar.order_adjustment_record_id as orderAdjustmentRecordId,
        poar.order_id as orderId, poar.order_item_id as orderItemId, poar.apply_qty, s.supplier_code, s.affiliate_dept_name, s.buyer_name as contactPurchaserEmpName, poar.receive_order_expect_delivery_date,
        mpoi.purchase_quantity,
        mpoi.purchase_quantity_unit,
        mpo.purchase_order_id as material_purchase_order_id,
        mpo.purchase_order_no as material_purchase_order_no,
        mpo.fabric_merchandiser_name,
        mpoi.color_card_id,
        mpo.status as material_purchase_order_status,
        mpoi.status as material_purchase_order_item_status,
        rece.estimate_submit_date,
        mpoi.purchase_order_item_id as material_purchase_order_item_id,
        po.is_material_order,
        poar.material_audit_responsible_party,
        poar.material_audit_delay_day,
        poar.material_audit_by,
        poar.material_audit_remark,
        poar.material_audit_time,
        poar.audit_by,
        poar.audit_remark,
        poi.brand_name
        from purchase_order_adjustment_record poar
        inner join purchase_order_item poi on poi.order_item_id = poar.order_item_id
        inner join purchase_order po on po.order_id = poar.order_id
        inner join product_spec ps on ps.spec_id = poi.spec_id
        inner join supplier s on po.supplier_id = s.supplier_id
        left join material_purchase_order_item_bulk_order_detail bod on poar.order_item_id = bod.bulk_order_item_id
        left join material_purchase_order_item mpoi on bod.material_order_item_id = mpoi.purchase_order_item_id and mpoi.is_delete = 0
        left join material_purchase_order mpo on mpoi.purchase_order_id = mpo.purchase_order_id and mpo.is_delete = 0
        left join material_receive_info rece on mpoi.purchase_order_item_id = rece.purchase_order_item_id and rece.is_delete = 0
        <include refid="pageForMaterialPendingWithPermissionOfBulkPurchaseWhere"></include>
        order by po.order_no,poar.adjustment_type, poar.skc, mpo.purchase_order_no, mpoi.color_card_id
    </select>
    <select id="queryByOrderItemIds"
            resultType="com.nsy.api.scm.dto.domain.purchase.bulk.PurchaseOrderAdjustmentRecordDto">
        select po.active_date, poar.remark, poar.responsible_party,po.order_no as orderNo,
        po.supplier_id as supplierId, po.buyer_user_id as buyerUserId, po.buyer_name as buyerName,
        poar.skc as skc, poar.sku as sku, poar.status as status, poar.previous_status as previousStatus,
        poi.scm_category_id as scmCategoryId,
        poi.scm_category_name as scmCategoryName, poi.purchase_qty as purchaseQty, poar.expect_delivery_date as expectDeliveryDate,
        poar.adjustment_type as adjustmentType, poar.delay_time as delayTime, poar.audit_delay_time as auditDelayTime,
        poar.apply_cancel_qty as applyCancelQty, poar.cancel_qty as cancelQty,
        poar.apply_reason as applyReason, poar.create_by as createBy, poar.create_date as createDate, poar.update_by as updateBy,
        poar.update_date as updateDate, poar.audit_time as auditTime, poar.handle_results as handleResults,
        poar.order_adjustment_record_id as orderAdjustmentRecordId,
        poar.order_id as orderId, poar.order_item_id as orderItemId, poar.apply_qty,
        poar.receive_order_expect_delivery_date
        from purchase_order_adjustment_record poar
        inner join purchase_order_item poi on poi.order_item_id = poar.order_item_id
        inner join purchase_order po on po.order_id = poar.order_id
        <where>
            <if test="query.orderItemIds != null and query.orderItemIds.size() > 0">
                and poar.order_item_id in
                <foreach collection="query.orderItemIds" item="orderItemId" open="(" separator="," close=")">
                    #{orderItemId}
                </foreach>
            </if>
            <if test="query.statusList != null and query.statusList.size() > 0">
                and poar.status in
                <foreach collection="query.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="query.adjustmentTypeList != null and query.adjustmentTypeList.size() > 0">
                and poar.adjustment_type in
                <foreach collection="query.adjustmentTypeList" item="adjustmentType" open="(" separator="," close=")">
                    #{adjustmentType}
                </foreach>
            </if>
        </where>
    </select>
    <select id="queryForNotify"
            resultType="com.nsy.api.scm.dto.domain.purchase.bulk.PurchaseOrderAdjustmentRecordDto">
        select po.active_date, poar.remark, poar.responsible_party,po.order_no as orderNo,
        po.supplier_id as supplierId, po.buyer_user_id as buyerUserId, po.buyer_name as buyerName,
        poar.skc as skc, poar.sku as sku, poar.status as status, poar.previous_status as previousStatus,
        poi.scm_category_id as scmCategoryId,
        poi.scm_category_name as scmCategoryName, poi.purchase_qty as purchaseQty, poar.expect_delivery_date as expectDeliveryDate,
        poar.adjustment_type as adjustmentType, poar.delay_time as delayTime, poar.audit_delay_time as auditDelayTime,
        poar.apply_cancel_qty as applyCancelQty, poar.cancel_qty as cancelQty,
        poar.apply_reason as applyReason, poar.create_by as createBy, poar.create_date as createDate, poar.update_by as updateBy,
        poar.update_date as updateDate, poar.audit_time as auditTime, poar.handle_results as handleResults,
        poar.order_adjustment_record_id as orderAdjustmentRecordId,
        poar.order_id as orderId, poar.order_item_id as orderItemId, poar.apply_qty,
        poar.receive_order_expect_delivery_date
        from purchase_order_adjustment_record poar
        inner join purchase_order_item poi on poi.order_item_id = poar.order_item_id
        inner join purchase_order po on po.order_id = poar.order_id
        <where>
            <if test="query.supplierId != null and query.supplierId != 0">
                and po.supplier_id = #{query.supplierId}
            </if>
            <if test="query.createStartedDate != null">
                and poar.create_date >= #{query.createStartedDate}
            </if>
            <if test="query.createEndedDate != null">
                and #{query.createEndedDate} > poar.create_date
            </if>
            <if test="query.statusList != null and query.statusList.size() > 0">
                and poar.status in
                <foreach collection="query.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="query.adjustmentTypeList != null and query.adjustmentTypeList.size() > 0">
                and poar.adjustment_type in
                <foreach collection="query.adjustmentTypeList" item="adjustmentType" open="(" separator="," close=")">
                    #{adjustmentType}
                </foreach>
            </if>
        </where>
    </select>
    <select id="querySumDelayCountList" resultType="com.nsy.scm.business.domain.purchase.bulk.OrderAndSkcDetailDto">
        SELECT order_id, skc, COUNT(DISTINCT create_date) AS sumDelayCount
        FROM purchase_order_adjustment_record
        WHERE (order_id, skc) IN
        <foreach collection="dtos" open="(" close=")" separator="," item="dto">
            (#{dto.orderId}, #{dto.skc})
        </foreach>
        AND status = 2
        AND adjustment_type IN (1, 2, 5, 6)
        GROUP BY order_id, skc
    </select>
    <select id="pageWithPermissionOfBulkPurchaseV2"
            resultType="com.nsy.api.scm.dto.domain.purchase.bulk.PurchaseOrderAdjustmentRecordV2Dto">
        select poar.skc,poar.spu,po.order_no,ps.image_url as imageUrl,po.active_date,po.space_name,poi.brand_name,poi.package_name,poar.order_adjustment_record_id,
        sum(poar.apply_qty) as activeQty,poi.expect_delivery_date,poar.apply_reason,poar.remark,poar.create_date,poar.update_date,poar.update_by,poar.audit_remark,
        poar.status,poar.responsible_party,poar.delay_time,poar.audit_delay_time,poar.order_id,poar.expect_delivery_date as deliveryDate,poar.create_by,poar.audit_by
        from purchase_order_adjustment_record poar
        inner join purchase_order_item poi on poi.order_item_id = poar.order_item_id
        inner join purchase_order po on po.order_id = poar.order_id
        inner join product_spec ps on ps.spec_id = poi.spec_id
        where 1 = 1
        <if test="query.responsibleParty != null and query.responsibleParty != ''">
            and poar.responsible_party = #{query.responsibleParty}
        </if>
        <if test="query.orderNo != null and query.orderNo != ''">
            and po.order_no = #{query.orderNo}
        </if>
        <if test="query.supplierIds != null and query.supplierIds.size() > 0">
            and po.supplier_id in
            <foreach collection="query.supplierIds" item="supplierId" open="(" separator="," close=")">
                #{supplierId}
            </foreach>
        </if>
        <if test="query.spuList != null and query.spuList.size() > 0">
            AND (<foreach collection="query.spuList" item="spu" separator=" OR ">
            poar.spu LIKE CONCAT(#{spu}, '%')
        </foreach>)
        </if>
        <if test="query.skcList != null and query.skcList.size() > 0">
            AND (<foreach collection="query.skcList" item="skc" separator=" OR ">
            poar.skc LIKE CONCAT(#{skc}, '%')
        </foreach>)
        </if>
        <if test="query.status != null and query.status != 0">
            and poar.status = #{query.status}
        </if>
        <if test="query.createStartedDate != null">
            and poar.create_date >= #{query.createStartedDate}
        </if>
        <if test="query.createEndedDate != null">
            and #{query.createEndedDate} > poar.create_date
        </if>
        <if test="query.auditStartedDate != null">
            and poar.audit_time >= #{query.auditStartedDate}
        </if>
        <if test="query.auditEndedDate != null">
            and #{query.auditEndedDate} > poar.audit_time
        </if>
        <if test="query.adjustmentTypeList != null and query.adjustmentTypeList.size() > 0">
            and poar.adjustment_type in
            <foreach collection="query.adjustmentTypeList" item="adjustmentType" open="(" separator="," close=")">
                #{adjustmentType}
            </foreach>
        </if>
        group by poar.skc,po.order_no,poar.create_date
        order by poar.create_date desc
    </select>
    <select id="getPurchaseOrderAdjustmentRecordV2DtoList"
            resultType="com.nsy.api.scm.dto.domain.purchase.bulk.PurchaseOrderAdjustmentRecordV2Dto">
         select poar.order_adjustment_record_id,poar.order_id,poar.skc,poar.create_date from purchase_order_adjustment_record poar
         <where>
             poar.order_id in
             <foreach collection="orderIdList" item="item" open="(" separator="," close=")">
                 #{item}
             </foreach>
             and poar.skc in
             <foreach collection="skcList" item="item" open="(" separator="," close=")">
                 #{item}
             </foreach>
             and poar.create_date in
             <foreach collection="createDateList" item="item" open="(" separator="," close=")">
                 #{item}
             </foreach>
         </where>
    </select>
</mapper>
