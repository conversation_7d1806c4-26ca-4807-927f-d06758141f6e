<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.purchase.bulk.PurchaseSpuDepartmentLastOrderVersionMapper">


    <select id="lastOrderInfoBySpuAndDepartment" resultType="com.nsy.scm.business.domain.purchase.bulk.LastOrderInfoBySpuAndDepartmentDto">
        SELECT
            poi.product_id,
            poi.spu,
            pp.apply_business_type,
            max( po.order_id ) AS lastOrderId
        FROM
        purchase_order po
            INNER JOIN purchase_order_item poi ON poi.order_id = po.order_id
            INNER JOIN purchase_plan pp ON pp.order_item_id = poi.order_item_id
            INNER JOIN product ON product.product_id = poi.product_id
        WHERE
            product.`status` NOT IN ( 'pending', 'delete' )
            AND po.`status` != 5
            AND poi.`status` != 5
            AND po.production_type = 1
            AND poi.workmanship_version_no != ''
        <if test="spuList != null and spuList.size() > 0">
            and poi.spu IN
            <foreach collection="spuList" item="spu" open="(" close=")" separator=",">
                #{spu}
            </foreach>
        </if>
        <if test="businessType != null and businessType != ''">
            and pp.apply_business_type = #{businessType}
        </if>
        <if test="location != null and location != ''">
            and poi.location = #{location}
        </if>
        GROUP BY poi.product_id, poi.spu, pp.apply_business_type
    </select>

</mapper>
