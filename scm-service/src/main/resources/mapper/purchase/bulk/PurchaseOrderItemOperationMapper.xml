<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.purchase.bulk.PurchaseOrderItemOperationMapper">

<select id="selectByOrderItemIdAndOperationType"
            resultType="com.nsy.scm.repository.entity.purchase.bulk.PurchaseOrderItemOperationEntity">
    select poio.* from purchase_order_item_operation poio
    where poio.order_item_id = #{orderItemId} and poio.operation_type = #{operationType}
    </select>
</mapper>
