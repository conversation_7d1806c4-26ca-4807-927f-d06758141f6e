<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.MaterialSupplierInfoMapper">

    <resultMap id="materialPage" type="com.nsy.scm.repository.projection.MaterialSupplierInfoPageProjection">
        <result property="infoId" column="info_id"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="materialId" column="material_id"/>
    </resultMap>

    <select id="pageIds" resultMap="materialPage">
        SELECT distinct info.supplier_info_id info_id,
        info.supplier_id supplier_id,
        m.material_id material_id,
        info.product_sale_volume,
        info.product_sale_volume_last_year productSaleVolumeLastYear,
        info.bind_product_count,
        info.branch_company_sale_volume,
        info.main_company_sale_volume
        FROM material m
            INNER JOIN material_supplier_info info on m.material_id = info.material_id  and info.is_delete = 0
            inner join material_supplier_info_permission on info.supplier_info_id = material_supplier_info_permission.material_supplier_info_id
        <choose>
            <when test="query.materialLabels != null and query.materialLabels.size() > 0">
                inner join material_supplier_attribute_mapping msam on info.supplier_info_id = msam.material_supplier_info_id and info.material_id = msam.material_id
                and msam.attribute_mapping_key = 'scm_develop_pattern_labels_material'
                and msam.attribute_mapping_value in
                <foreach collection="query.materialLabels" item="materialLabel" separator=","
                         open="(" close=")">
                    #{materialLabel}
                </foreach>
            </when>
            <otherwise>
                LEFT JOIN material_supplier_attribute_mapping msam on info.supplier_info_id = msam.material_supplier_info_id and info.material_id = msam.material_id
                and msam.attribute_mapping_key = 'scm_develop_pattern_labels_material'
            </otherwise>
        </choose>

        <if test="query.seasons != null and query.seasons.size() > 0">
            INNER JOIN material_attribute_mapping season on m.material_id = season.material_id
        </if>
        <if test="query.suitableCategoryIds != null and query.suitableCategoryIds.size() > 0">
            INNER JOIN material_attribute_mapping category on m.material_id = category.material_id
        </if>
        <if test="query.applicableCompanies != null and query.applicableCompanies.size() > 0">
            INNER JOIN material_attribute_mapping company on m.material_id = company.material_id
        </if>
        <if test="query.sellingPoints != null and query.sellingPoints.size() > 0">
            INNER JOIN material_attribute_mapping sellingPoint on m.material_id = sellingPoint.material_id
        </if>
        <if test="query.floralStyle != null and query.floralStyle !=''">
            inner JOIN material_supplier_color_card colorCard on info.supplier_info_id = colorCard.material_supplier_info_id and colorCard.is_delete = 0
        </if>
        <if test="(query.supplierShortName != null and query.supplierShortName !='') or (query.isFilterSignOutSupplier != null and query.isFilterSignOutSupplier)">
            inner JOIN supplier s on info.supplier_id = s.supplier_id
        </if>
        <where>
            <if test="query.materialId != null">
                and m.material_id = #{query.materialId}
            </if>
            <if test="query.materialCode != null and query.materialCode != ''">
                and m.material_code = #{query.materialCode}
            </if>
            <if test="query.isLightCustom != null">
                and m.is_light_custom = #{query.isLightCustom}
            </if>
            <if test="query.technology != null and query.technology != ''">
                and m.technology = #{query.technology}
            </if>
            <if test="query.clothStyle != null and query.clothStyle != ''">
                and m.cloth_style = #{query.clothStyle}
            </if>
            <if test="query.internalFabricName != null and query.internalFabricName != ''">
                and m.internal_fabric_name like concat('%', #{query.internalFabricName}, '%')
            </if>
            <if test="query.materialCategory != null and query.materialCategory != ''">
                and m.material_category = #{query.materialCategory}
            </if>
            <if test="query.materialApplyNo != null and query.materialApplyNo != ''">
                and m.material_apply_no like concat('%', #{query.materialApplyNo}, '%')
            </if>
            <if test="query.enableStatus != null and query.enableStatus != ''">
                and info.enable_status = #{query.enableStatus}
            </if>
            <if test="query.horizontalElastic != null and query.horizontalElastic != ''">
                and m.horizontal_elastic = #{query.horizontalElastic}
            </if>
            <if test="query.fabricateWay != null and query.fabricateWay != ''">
                and m.fabricate_way = #{query.fabricateWay}
            </if>
            <if test="query.isDetected != null">
                and info.is_detected = #{query.isDetected}
            </if>
            <if test="query.isQualified != null">
                and info.is_qualified = #{query.isQualified}
            </if>
            <if test="query.ingredientId != null">
                and info.ingredient_id = #{query.ingredientId}
            </if>
            <if test="query.supplierMaterialName != null and query.supplierMaterialName != ''">
                and info.supplier_material_name like concat('%', #{query.supplierMaterialName}, '%')
            </if>
            <if test="query.developEmpCode != null and query.developEmpCode != ''">
                and info.develop_emp_code = #{query.developEmpCode}
            </if>
            <if test="query.developEmpId != null">
                and info.develop_emp_id = #{query.developEmpId}
            </if>
            <if test="query.materialName != null and query.materialName != ''">
                and m.material_name like concat('%', #{query.materialName}, '%')
            </if>
            <if test="query.collectSource != null and query.collectSource != ''">
                and m.collect_source = #{query.collectSource}
            </if>
            <if test="query.largeCargoElevator != null and query.largeCargoElevator != ''">
                and info.large_cargo_elevator = #{query.largeCargoElevator}
            </if>
            <if test="query.materialSupplierInfoId != null">
                and info.supplier_info_id = #{query.materialSupplierInfoId}
            </if>
            <if test="query.supplierShortName != null and query.supplierShortName !=''">
                and s.supplier_short_name like concat(#{query.supplierShortName}, '%')
            </if>
            <if test="query.isFilterSignOutSupplier != null and query.isFilterSignOutSupplier">
                and s.cooperate_status != 'SIGN_OUT'
            </if>
            <if test="query.selectIdList != null and query.selectIdList.size() > 0">
                and info.supplier_info_id in
                <foreach collection="query.selectIdList" item="supplierInfoId" separator=","
                         open="(" close=")">
                    #{supplierInfoId}
                </foreach>
            </if>

            <if test="query.supplierIds !=null and query.supplierIds.size() > 0">
                and info.supplier_id IN
                <foreach collection="query.supplierIds" item="supplierId" separator=","
                         open="(" close=")">
                    #{supplierId}
                </foreach>
            </if>

            <if test="query.materialIds !=null and query.materialIds.size() > 0">
                and m.material_id IN
                <foreach collection="query.materialIds" item="materialId" separator=","
                         open="(" close=")">
                    #{materialId}
                </foreach>
            </if>

            <if test="query.supplierId != null">
                and info.supplier_id = #{query.supplierId}
            </if>
            <if test="query.grammage != null">
                and info.grammage = #{query.grammage}
            </if>
            <if test="query.supplierMaterialCode != null and query.supplierMaterialCode != ''">
                and info.supplier_material_code like concat('%', #{query.supplierMaterialCode}, '%')
            </if>
            <if test="query.createStartDate != null">
                and info.create_date >= DATE_FORMAT(#{query.createStartDate}, "%Y-%m-%d %H:%i:%S")
            </if>
            <if test="query.saleDepartmentList != null and query.saleDepartmentList.size() > 0">
                and
                <foreach collection="query.saleDepartmentList" item="saleDepartmentColumn" separator="or" open="(" close=")">
                    info.${saleDepartmentColumn} > 0
                </foreach>
            </if>
            <if test="query.materialSupplierInfoIdsByProductSku != null and query.materialSupplierInfoIdsByProductSku.size() > 0">
                and info.supplier_info_id in
                <foreach collection="query.materialSupplierInfoIdsByProductSku" item="supplierInfoId" separator=","
                         open="(" close=")">
                    #{supplierInfoId}
                </foreach>
            </if>
            <if test="query.createEndDate != null">
                and DATE_ADD(DATE_FORMAT(#{query.createEndDate}, "%Y-%m-%d %H:%i:%S"), INTERVAL 1 DAY) >= info.create_date
            </if>
            <if test="query.materialTypeIds != null and query.materialTypeIds.size() > 0">
                and m.material_type_id in
                <foreach collection="query.materialTypeIds" item="materialTypeId" separator=","
                         open="(" close=")">
                    #{materialTypeId}
                </foreach>
            </if>
            <if test="query.seasons != null and query.seasons.size() > 0">
                and season.attribute_mapping_key = #{query.seasonDictionaryQuery.dictionaryName}
                and season.attribute_mapping_value in
                <foreach collection="query.seasonDictionaryQuery.dictionaryValues" item="dictionaryValue" separator=","
                         open="(" close=")">
                    #{dictionaryValue}
                </foreach>
            </if>
            <if test="query.suitableCategoryIds != null and query.suitableCategoryIds.size() > 0">
                and category.attribute_mapping_key = #{query.categoryDictionaryQuery.dictionaryName}
                and category.attribute_mapping_value in
                <foreach collection="query.categoryDictionaryQuery.dictionaryValues" item="dictionaryValue"
                         separator="," open="(" close=")">
                    #{dictionaryValue}
                </foreach>
            </if>
            <if test="query.floralStyle != null and query.floralStyle !=''">
                and colorCard.floral_style = #{query.floralStyle}
                and colorCard.color_card_id is not null
            </if>
            <if test="query.applicableCompanies != null and query.applicableCompanies.size() > 0">
                and company.attribute_mapping_key = #{query.companiesDictionaryQuery.dictionaryName}
                and company.attribute_mapping_value in
                <foreach collection="query.companiesDictionaryQuery.dictionaryValues" item="dictionaryValue" separator="," open="(" close=")">
                    #{dictionaryValue}
                </foreach>
            </if>
            <if test="query.sellingPoints != null and query.sellingPoints.size() > 0">
                and sellingPoint.attribute_mapping_key = #{query.sellingPointDictionaryQuery.dictionaryName}
                and sellingPoint.attribute_mapping_value in
                <foreach collection="query.sellingPointDictionaryQuery.dictionaryValues" item="dictionaryValue" separator="," open="(" close=")">
                    #{dictionaryValue}
                </foreach>
            </if>
            <if test="query.createByCompanyList != null and query.createByCompanyList.size() > 0">
                and info.create_by_company in
                <foreach collection="query.createByCompanyList" item="createByCompany" separator="," open="(" close=")">
                    #{createByCompany}
                </foreach>
            </if>
            <if test="query.identification != null and query.identification !=''">
                and info.identification = #{query.identification}
            </if>
            <if test="query.minWholePrice != null">
                and info.whole_price >= #{query.minWholePrice}
            </if>
            <if test="query.maxWholePrice != null">
                and #{query.maxWholePrice} >= info.whole_price
            </if>
        </where>
    </select>

    <sql id="unionPageItem">
        SELECT distinct info.supplier_info_id info_id, info.product_sale_volume
        FROM material material
        inner join material_supplier_info info on material.material_id = info.material_id  and info.is_delete = 0
        inner join material_supplier_info_permission on info.supplier_info_id = material_supplier_info_permission.material_supplier_info_id
        inner join supplier s on s.supplier_id = info.supplier_id and s.cooperate_status != 'SIGN_OUT'
        <if test="query.materialLabels != null and query.materialLabels.size() > 0">
            inner join material_supplier_attribute_mapping msam on info.supplier_info_id = msam.material_supplier_info_id and info.material_id = msam.material_id
            and  msam.attribute_mapping_key = 'scm_develop_pattern_labels_material'
            and msam.attribute_mapping_value in
            <foreach collection="query.materialLabels" item="materialLabel" separator=","
                     open="(" close=")">
                #{materialLabel}
            </foreach>
        </if>
            <if test="query.seasons != null and query.seasons.size() > 0">
                INNER JOIN material_attribute_mapping season on material.material_id = season.material_id
            </if>
            <if test="query.suitableCategoryIds != null and query.suitableCategoryIds.size() > 0">
                INNER JOIN material_attribute_mapping category on material.material_id = category.material_id
            </if>
            <if test="query.applicableCompanies != null and query.applicableCompanies.size() > 0">
                INNER JOIN material_attribute_mapping company on material.material_id = company.material_id
            </if>
            inner JOIN material_supplier_color_card colorCard on info.supplier_info_id = colorCard.material_supplier_info_id and colorCard.is_delete = 0 and colorCard.is_enable = 1
        <where>
            <if test="query.enableStatus != null and query.enableStatus != ''">
                and info.enable_status = #{query.enableStatus}
            </if>
            <if test="query.materialCategory != null and query.materialCategory != ''">
                and material.material_category = #{query.materialCategory}
            </if>
            <if test="query.materialId != null and query.materialId > 0">
                and material.material_id = #{query.materialId}
            </if>
            <if test="query.materialName != null and query.materialName != ''">
                and material.material_name like concat(#{query.materialName}, '%')
            </if>
            <if test="query.supplierMaterialName != null and query.supplierMaterialName != ''">
                and info.supplier_material_name like concat('%', #{query.supplierMaterialName}, '%')
            </if>
            <if test="query.supplierId != null">
                and info.supplier_id = #{query.supplierId}
            </if>
            <if test="query.materialSupplierInfoIdsByProductSku != null and query.materialSupplierInfoIdsByProductSku.size() > 0">
                and info.supplier_info_id in
                <foreach collection="query.materialSupplierInfoIdsByProductSku" item="supplierInfoId" separator=","
                         open="(" close=")">
                    #{supplierInfoId}
                </foreach>
            </if>
            <if test="query.createStartDate != null">
                and info.create_date >= DATE_FORMAT(#{query.createStartDate}, "%Y-%m-%d %H:%i:%S")
            </if>
            <if test="query.createEndDate != null">
                and DATE_ADD(DATE_FORMAT(#{query.createEndDate}, "%Y-%m-%d %H:%i:%S"), INTERVAL 1 DAY) >= info.create_date
            </if>
            <if test="query.grammage != null">
                and info.grammage = #{query.grammage}
            </if>
            <if test="query.saleDepartmentList != null and query.saleDepartmentList.size() > 0">
                and
                <foreach collection="query.saleDepartmentList" item="saleDepartmentColumn" separator="or" open="(" close=")">
                    info.${saleDepartmentColumn} > 0
                </foreach>
            </if>
            <if test="query.seasons != null and query.seasons.size() > 0">
                and season.attribute_mapping_key = #{query.seasonDictionaryQuery.dictionaryName}
                and season.attribute_mapping_value in
                <foreach collection="query.seasonDictionaryQuery.dictionaryValues" item="dictionaryValue" separator=","
                         open="(" close=")">
                    #{dictionaryValue}
                </foreach>
            </if>
            <if test="query.suitableCategoryIds != null and query.suitableCategoryIds.size() > 0">
                and category.attribute_mapping_key = #{query.categoryDictionaryQuery.dictionaryName}
                and category.attribute_mapping_value in
                <foreach collection="query.categoryDictionaryQuery.dictionaryValues" item="dictionaryValue"
                         separator="," open="(" close=")">
                    #{dictionaryValue}
                </foreach>
            </if>
            <if test="query.applicableCompanies != null and query.applicableCompanies.size() > 0">
                and company.attribute_mapping_key = #{query.companiesDictionaryQuery.dictionaryName}
                and company.attribute_mapping_value in
                <foreach collection="query.companiesDictionaryQuery.dictionaryValues" item="dictionaryValue"
                         separator="," open="(" close=")">
                    #{dictionaryValue}
                </foreach>
            </if>
            <if test="query.materialTypeIds != null and query.materialTypeIds.size() > 0">
                and material.material_type_id in
                <foreach collection="query.materialTypeIds" item="materialTypeId" separator=","
                         open="(" close=")">
                    #{materialTypeId}
                </foreach>
            </if>
        </where>
    </sql>

    <select id="unionPageIds" resultType="int" parameterType="com.nsy.api.scm.dto.request.material.MaterialSupplierInfoProductPageRequest">
        select distinct info_id, product_sale_volume
        from (
        <if test="query.recommendFloralTypeList != null and query.recommendFloralTypeList.size > 0">
            <foreach collection="query.recommendFloralTypeList" separator="union"
                     item="recommendFloralType">
                    <include refid="unionPageItem"></include>
                    and colorCard.floral_style = #{recommendFloralType.floralStyle} and colorCard.color_dictionary_item_value = #{recommendFloralType.color}
            </foreach>
             UNION
        </if>
            <include refid="unionPageItem"></include>
        ) a
        order by product_sale_volume desc
    </select>


    <select id="page" resultType="com.nsy.scm.repository.entity.MaterialEntity">
        SELECT distinct m.*
            , sum(ifnull(msi.product_sale_volume,0)) product_sale_volume
        FROM material m
        left JOIN material_supplier_info msi ON m.material_id = msi.material_id
        <if test="query.productSku != null and query.productSku != ''">
            INNER JOIN material_product_bind_mapping mpbm on mpbm.material_id = m.material_id
        </if>
        <if test="query.seasons != null and query.seasons.size() > 0">
            INNER JOIN material_attribute_mapping season on m.material_id = season.material_id
        </if>
        <if test="query.suitableCategoryIds != null and query.suitableCategoryIds.size() > 0">
            INNER JOIN material_attribute_mapping category on m.material_id = category.material_id
        </if>
        <if test="query.applicableCompanies != null and query.applicableCompanies.size() > 0">
            INNER JOIN material_attribute_mapping company on m.material_id = company.material_id
        </if>
        <if test="query.sellingPoints != null and query.sellingPoints.size() > 0">
            INNER JOIN material_attribute_mapping sellingPoint on m.material_id = sellingPoint.material_id
        </if>
        <!-- 面料标签 -->
        <choose>
            <when test="query.materialLabels != null and query.materialLabels.size() > 0">
                INNER JOIN material_attribute_mapping materialLabel on m.material_id = materialLabel.material_id
            </when>
            <otherwise>
                LEFT JOIN material_attribute_mapping materialLabel on m.material_id = materialLabel.material_id
                AND materialLabel.attribute_mapping_key = 'scm_develop_pattern_labels_material'
            </otherwise>
        </choose>
        <where>
            <if test="query.materialId != null">
                and m.material_id = #{query.materialId}
            </if>
            <if test="query.selectIdList != null and query.selectIdList.size() > 0">
                and m.material_id in
                <foreach collection="query.selectIdList" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query.materialCode != null and query.materialCode != ''">
                and m.material_code = #{query.materialCode}
            </if>
            <if test="query.enableStatus != null and query.enableStatus != ''">
                and m.enable_status = #{query.enableStatus}
            </if>
            <if test="query.technology != null and query.technology != ''">
                and m.technology = #{query.technology}
            </if>
            <if test="query.isLightCustom != null">
                and m.is_light_custom = #{query.isLightCustom}
            </if>
            <if test="query.clothStyle != null and query.clothStyle != ''">
                and m.cloth_style = #{query.clothStyle}
            </if>
            <if test="query.internalFabricName != null and query.internalFabricName != ''">
                and m.internal_fabric_name like concat('%', #{query.internalFabricName}, '%')
            </if>
            <if test="query.materialCategory != null and query.materialCategory != ''">
                and m.material_category = #{query.materialCategory}
            </if>
            <if test="query.productSku != null and query.productSku != ''">
                and mpbm.product_sku = #{query.productSku}
            </if>
            <if test="query.horizontalElastic != null and query.horizontalElastic != ''">
                and m.horizontal_elastic = #{query.horizontalElastic}
            </if>
            <if test="query.fabricateWay != null and query.fabricateWay != ''">
                and m.fabricate_way = #{query.fabricateWay}
            </if>
            <if test="query.materialName != null and query.materialName != ''">
                and m.material_name like concat('%', #{query.materialName}, '%')
            </if>
            <if test="query.collectSource != null and query.collectSource != ''">
                and m.collect_source = #{query.collectSource}
            </if>
            <if test="query.materialTypeId != null">
                and m.material_type_id = #{query.materialTypeId}
            </if>
            <if test="query.materialTypeIds != null and query.materialTypeIds.size() > 0">
                and m.material_type_id in
                <foreach collection="query.materialTypeIds" item="materialTypeId" separator=","
                         open="(" close=")">
                    #{materialTypeId}
                </foreach>
            </if>
            <if test="query.seasons != null and query.seasons.size() > 0">
                and season.attribute_mapping_key = #{query.seasonDictionaryQuery.dictionaryName}
                and season.attribute_mapping_value in
                <foreach collection="query.seasonDictionaryQuery.dictionaryValues" item="dictionaryValue" separator=","
                         open="(" close=")">
                    #{dictionaryValue}
                </foreach>
            </if>
            <if test="query.suitableCategoryIds != null and query.suitableCategoryIds.size() > 0">
                and category.attribute_mapping_key = #{query.categoryDictionaryQuery.dictionaryName}
                and category.attribute_mapping_value in
                <foreach collection="query.categoryDictionaryQuery.dictionaryValues" item="dictionaryValue"
                         separator="," open="(" close=")">
                    #{dictionaryValue}
                </foreach>
            </if>
            <if test="query.applicableCompanies != null and query.applicableCompanies.size() > 0">
                and company.attribute_mapping_key = #{query.companiesDictionaryQuery.dictionaryName}
                and company.attribute_mapping_value in
                <foreach collection="query.companiesDictionaryQuery.dictionaryValues" item="dictionaryValue"
                         separator="," open="(" close=")">
                    #{dictionaryValue}
                </foreach>
            </if>
            <if test="query.materialLabels != null and query.materialLabels.size() > 0">
                and materialLabel.attribute_mapping_key = #{query.materialLabelDictionaryQuery.dictionaryName}
                and materialLabel.attribute_mapping_value in
                <foreach collection="query.materialLabelDictionaryQuery.dictionaryValues" item="dictionaryValue"
                         separator="," open="(" close=")">
                    #{dictionaryValue}
                </foreach>
            </if>
            <if test="query.sellingPoints != null and query.sellingPoints.size() > 0">
                and sellingPoint.attribute_mapping_key = #{query.sellingPointDictionaryQuery.dictionaryName}
                and sellingPoint.attribute_mapping_value in
                <foreach collection="query.sellingPointDictionaryQuery.dictionaryValues" item="dictionaryValue" separator="," open="(" close=")">
                    #{dictionaryValue}
                </foreach>
            </if>
            <if test="query.createByCompanyList != null and query.createByCompanyList.size() > 0">
                and m.create_by_company in
                <foreach collection="query.createByCompanyList" item="createByCompany" separator="," open="(" close=")">
                    #{createByCompany}
                </foreach>
            </if>
        </where>
        GROUP BY m.material_id
        <if test="query.orderBys == null or query.orderBys.size == 0 or query.sortField == null or query.sortField == '' ">
            order by materialLabel.attribute_mapping_value desc, materialLabel.effective_start_date desc, product_sale_volume desc, m.material_id
        </if>
        <if test="query.orderBys !=null and query.orderBys.size >0">
            order by
            <foreach collection="query.orderBys " item="item" separator=",">
                ${item}
            </foreach>
        </if>
    </select>


    <select id="getAllTabCountMap" resultType="java.util.HashMap">
        SELECT m.material_category,count(*) count FROM material m
            INNER JOIN material_supplier_info info on m.material_id = info.material_id  and info.is_delete = 0
            inner join material_supplier_info_permission on info.supplier_info_id = material_supplier_info_permission.material_supplier_info_id
        group by material_category
    </select>

    <select id="materialSupplierInfoDropDown" resultType="com.nsy.api.scm.dto.response.material.MaterialSupplierInfoListRes" parameterType="com.nsy.api.scm.dto.request.material.MaterialSupplierInfoListRequest">
        SELECT info.supplier_info_id materialSupplierInfoId,
               info.material_id materialId,
               info.supplier_material_name supplierMaterialName
        FROM material material
            INNER JOIN material_supplier_info info on material.material_id = info.material_id  and info.is_delete = 0
            <if test="query.materialTypeNameList != null and query.materialTypeNameList.size() > 0">
                LEFT JOIN material_type mType on mType.material_type_id = material.material_type_id
            </if>
        <where>
            <if test="query.materialCategory != null and query.materialCategory != ''">
                and material.material_category = #{query.materialCategory}
            </if>
            <if test="query.enableStatus != null and query.enableStatus != ''">
                and info.enable_status = #{query.enableStatus}
            </if>
            <if test="query.supplierId != null">
                and info.supplier_id = #{query.supplierId}
            </if>
            <if test="query.materialTypeNameList != null and query.materialTypeNameList.size() > 0">
                and mType.material_type_name in
                <foreach collection="query.materialTypeNameList" item="materialTypeName" separator=","
                         open="(" close=")">
                    #{materialTypeName}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getByMaterialIdWithPermission" resultType="com.nsy.scm.repository.entity.MaterialSupplierInfoEntity">
        SELECT
        info.*,
        s.supplier_name,
        msav.material_url
        FROM
        material_supplier_info info
        LEFT JOIN supplier s
        ON s.supplier_id = info.supplier_id
        INNER JOIN material_supplier_info_permission
        ON info.supplier_info_id = material_supplier_info_permission.material_supplier_info_id
        LEFT JOIN
        (SELECT
        material_supplier_info_id supplier_info_id,
        attachment_url material_url
        FROM
        material_supplier_attachment m
        WHERE m.attachment_id IN
        (SELECT
        MIN(msa.attachment_id) attachment_id
        FROM
        material_supplier_attachment msa
        WHERE msa.attachment_type = 'FABRIC_SUPPLIER_REFERENCE_IMG'
        AND msa.attachment_url IS NOT NULL
        AND msa.is_delete = 0
        GROUP BY msa.material_supplier_info_id)) msav
        ON msav.supplier_info_id = info.`supplier_info_id`
        <where>
            <!--  1=1 占位符，权限框架，会把1=1 替换为目标sql -->
            1=1
            <if test="request.materialIds !=null and request.materialIds.size() > 0">
                and info.material_id IN
                <foreach collection="request.materialIds" item="materialId" separator="," open="(" close=")">
                    #{materialId}
                </foreach>
            </if>
            <if test="request.materialSupplierInfoIds !=null and request.materialSupplierInfoIds.size() > 0">
                and info.supplier_info_id IN
                <foreach collection="request.materialSupplierInfoIds" item="materialSupplierInfoId" separator="," open="(" close=")">
                    #{materialSupplierInfoId}
                </foreach>
            </if>
            <if test="request.supplierId !=null">
                and info.supplier_id = #{request.supplierId}
            </if>
            <if test="request.isDeleted !=null">
                and info.is_delete = #{request.isDeleted}
            </if>
            <if test="request.enableStatus != null and request.enableStatus != ''">
                and info.enable_status = #{request.enableStatus}
            </if>
        </where>
    </select>

    <select id="getBySupplierIdWithPermission" resultType="com.nsy.scm.repository.entity.MaterialSupplierInfoEntity">
        SELECT
        info.*,
        s.supplier_name,
        msav.material_url
        FROM
        material_supplier_info info
        LEFT JOIN supplier s
        ON s.supplier_id = info.supplier_id
        INNER JOIN material_supplier_info_permission
        ON info.supplier_info_id = material_supplier_info_permission.material_supplier_info_id
        LEFT JOIN
        (SELECT
        material_supplier_info_id supplier_info_id,
        attachment_url material_url
        FROM
        material_supplier_attachment m
        WHERE m.attachment_id IN
        (SELECT
        MIN(msa.attachment_id) attachment_id
        FROM
        material_supplier_attachment msa
        WHERE msa.attachment_type = 'FABRIC_SUPPLIER_REFERENCE_IMG'
        AND msa.attachment_url IS NOT NULL
        AND msa.is_delete = 0
        GROUP BY msa.material_supplier_info_id)) msav
        ON msav.supplier_info_id = info.`supplier_info_id`
        <where>
            <if test="supplierIds !=null and supplierIds.size() > 0">
                and info.supplier_id IN
                <foreach collection="supplierIds" item="supplierIdLs" separator=","
                         open="(" close=")">
                    #{supplierIdLs}
                </foreach>
            </if>
            <if test="supplierMaterialName != null and supplierMaterialName != ''">
                and info.supplier_material_name like concat('%', #{supplierMaterialName}, '%')
            </if>
        </where>
        and info.is_delete = #{isDeleted}
        and info.enable_status = 'ENABLE'
    </select>

    <select id="queryMaterialWithoutPermission" resultType="com.nsy.scm.repository.entity.MaterialSupplierInfoEntity">
        SELECT info.*, s.supplier_name from material_supplier_info info
        left join supplier s on s.supplier_id = info.supplier_id
        where info.material_id in
        <foreach collection="materialIds" item="materialId" separator=","
                 open="(" close=")">
            #{materialId}
        </foreach>
        and info.is_delete = 0
    </select>


    <select id="pageQuery" resultType="com.nsy.api.scm.dto.response.material.MaterialSupplierInfoRes">
        SELECT
        info.*,
        s.supplier_name,
        sa.material_url,
        ingredient.text ingredient
        FROM
        nsy_scm.material_supplier_info info
        LEFT JOIN nsy_scm.supplier s
        ON s.supplier_id = info.supplier_id
        LEFT JOIN
        (SELECT
        material_supplier_info_id,
        attachment_url material_url
        FROM
        material_supplier_attachment m
        WHERE m.attachment_id IN
        (SELECT
        MIN(msa.attachment_id) attachment_id
        FROM
        material_supplier_attachment msa
        WHERE msa.attachment_type = 'FABRIC_SUPPLIER_REFERENCE_IMG'
        AND msa.attachment_url IS NOT NULL
        AND msa.is_delete = 0
        GROUP BY msa.material_supplier_info_id)) sa
        ON sa.material_supplier_info_id = info.supplier_info_id
        INNER JOIN nsy_scm.material_supplier_info_permission
        ON info.supplier_info_id = material_supplier_info_permission.material_supplier_info_id
        LEFT JOIN 	nsy_scm.`bd_ingredient` ingredient on info.ingredient_id = ingredient.id
        where  info.is_delete = 0 and info.enable_status = 'ENABLE'
            <if test="query.supplierId != null">
                and info.supplier_id = #{query.supplierId}
            </if>
            <if test="query.supplierIds != null and query.supplierIds.size() > 0">
                and info.supplier_id in
                <foreach collection="query.supplierIds" item="supplierIds" separator=","
                         open="(" close=")">
                    #{supplierIds}
                </foreach>
            </if>
            <if test="query.supplierInfoId != null">
                and info.supplier_info_id = #{query.supplierInfoId}
            </if>
            <if test="query.supplierMaterialName != null">
                and info.supplier_material_name like concat('%', #{query.supplierMaterialName}, '%')
            </if>
            <if test="query.supplierMaterialCode != null and query.supplierMaterialCode != ''">
                and info.supplier_material_code like concat(#{query.supplierMaterialCode}, '%')
            </if>
            <if test="query.updateTimeStart != null">
                and info.update_date &gt;= #{query.updateTimeStart}
            </if>
            <if test="query.updateTimeEnd != null">
                and info.update_date &lt;= #{query.updateTimeEnd}
            </if>
    </select>

    <update id="setNotDefault">
        update material_supplier_info set is_default = 0
        where material_id = #{materialId} and supplier_info_id != #{supplierInfoId}
    </update>

    <select id="queryMaterialAndSupplierInfoByIds"
            resultType="com.nsy.scm.business.domain.print.LargeCargoElevator">
        SELECT
            m.material_name,
            m.material_id,
            m.fabricate_way,
            m.internal_fabric_name,
            m.cloth_style,
            m.floral_style,
            info.supplier_info_id,
            info.supplier_material_name,
            info.storage_area,
            info.fabric_style,
            info.ingredient_id,
            info.price_unit,
            TRUNCATE(info.net_breadth,0) as net_breadth,
            TRUNCATE(info.gross_breadth,0) as gross_breadth,
            TRUNCATE(info.grammage,0) as grammage,
            sr.supplier_short_name,
            sr.supplier_code,
            bi.text as ingredient_desc,
            TRUNCATE(info.whole_price,2) as whole_price,
            TRUNCATE(info.unit_price,2) as unit_price
        from material_supplier_info info
        inner join material m on info.material_id = m.material_id
        inner join supplier sr on info.supplier_id = sr.supplier_id
        left join bd_ingredient bi on info.ingredient_id = bi.id
        where info.supplier_info_id in
        <foreach collection="supplierInfoIds" item="id" separator=","
                 open="(" close=")">
            #{id}
        </foreach>
    </select>
    <select id="iPage" resultType="com.nsy.api.scm.dto.domain.material.MaterialSupplierInfoForDemandOrderDTO">
        select
            a.supplier_id,
            a.supplier_info_id,
            a.supplier_material_name,
            a.grammage,
            a.net_breadth,
            a.whole_price as unit_price,
            a.ingredient_id,
            b.text as ingredient_name
        from material_supplier_info a
                 left join bd_ingredient b on a.ingredient_id = b.id
        where a.enable_status = 'ENABLE' and a.is_delete = 0
        <if test="request.excludeSupplierInfoIds != null and request.excludeSupplierInfoIds.size() > 0">
            and a.supplier_info_id not in
            <foreach collection="request.excludeSupplierInfoIds" item="id" separator=","
                     open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="request.supplierId != null">
            and a.supplier_id = #{request.supplierId}
        </if>
        <if test="request.supplierInfoId != null">
            and a.supplier_info_id = #{request.supplierInfoId}
        </if>
        <if test="request.supplierMaterialName != null and request.supplierMaterialName != ''">
            and a.supplier_material_name like concat(#{request.supplierMaterialName}, '%')
        </if>
    </select>

    <select id="querySupplierInfoColors" resultType="com.nsy.api.scm.dto.response.material.MaterialColorOptionRes">
        SELECT
            info.supplier_info_id,
            mscc.color_dictionary_item_value VALUE
        FROM nsy_scm.material_supplier_info info
            LEFT JOIN
            nsy_scm.material_supplier_color_card mscc ON mscc.material_supplier_info_id = info.supplier_info_id
        WHERE info.supplier_info_id = #{supplierInfoId}
            AND mscc.is_enable = 1
        AND mscc.color_dictionary_item_value IS NOT NULL
    </select>

    <select id="getMaxMaterialSupplierInfoId" resultType="java.lang.Integer">
        select max(supplier_info_id) from material_supplier_info;
    </select>

    <select id="materialDetailInfoPage"
            resultType="com.nsy.scm.repository.entity.MaterialSupplierInfoEntity">
        SELECT
        info.*
        FROM
        nsy_scm.material_supplier_info info
            inner join material on material.material_id = info.material_id
        where  info.is_delete = 0
        <if test="query.materialId != null">
            and material.material_id = #{query.materialId}
        </if>
        <if test="query.supplierId != null">
            and info.supplier_id = #{query.supplierId}
        </if>
        <if test="query.supplierIds != null and query.supplierIds.size() > 0">
            and info.supplier_id in
            <foreach collection="query.supplierIds" item="supplierIds" separator=","
                     open="(" close=")">
                #{supplierIds}
            </foreach>
        </if>
        <if test="query.supplierInfoId != null">
            and info.supplier_info_id = #{query.supplierInfoId}
        </if>
        <if test="query.supplierMaterialName != null">
            and info.supplier_material_name like concat(#{query.supplierMaterialName}, '%')
        </if>
        <if test="query.updateTimeStart != null">
            and info.update_date &gt;= #{query.updateTimeStart}
        </if>
        <if test="query.updateTimeEnd != null">
            and info.update_date &lt;= #{query.updateTimeEnd}
        </if>
    </select>

    <select id="supplierColorCardPage" resultType="java.lang.Integer">
        SELECT info.supplier_info_id from material_supplier_info info
        <if test="request.materialSupplierId != null">
            inner join supplier s on info.supplier_id = s.supplier_id
            and s.supplier_id = #{request.materialSupplierId}
        </if>
        where info.supplier_info_id = #{materialSupplierInfoId}
        <if test="request.supplierMaterialName != null and request.supplierMaterialName != ''">
            and info.supplier_material_name = #{request.supplierMaterialName}
        </if>
        and info.is_delete = 0 and info.enable_status = 'ENABLE'

        UNION ALL
        SELECT info.supplier_info_id from material_supplier_info info
        <if test="request.materialSupplierId != null">
            inner join supplier s on info.supplier_id = s.supplier_id
            and s.supplier_id = #{request.materialSupplierId}
        </if>
        where info.material_id = #{materialId}
        and info.supplier_info_id &lt;&gt; #{materialSupplierInfoId}
        <if test="request.supplierMaterialName != null and request.supplierMaterialName != ''">
            and info.supplier_material_name = #{request.supplierMaterialName}
        </if>
        and info.is_delete = 0 and info.enable_status = 'ENABLE'

    </select>
    <select id="pageMaterialWithoutPriceChange"
            resultType="com.nsy.scm.repository.entity.MaterialSupplierInfoEntity">
        SELECT msi.supplier_id, msi.material_id, msi.supplier_material_name, msi.unit_price, msi.whole_price, mspr.update_date
        FROM material_supplier_info msi
        INNER JOIN material_supplier_price_record mspr ON mspr.material_supplier_info_id = msi.supplier_info_id
        <where>
            msi.enable_status = 'ENABLE' and msi.is_delete = 0
            <if test="developEmpIds != null and developEmpIds.size() > 0">
                and msi.develop_emp_id in
                <foreach collection="developEmpIds" item="developEmpId" separator="," open="(" close=")">
                    #{developEmpId}
                </foreach>
            </if>
            <if test="createByCompany != null">
                and msi.create_by_company = #{createByCompany}
            </if>
        </where>
        GROUP BY mspr.material_supplier_info_id
        <if test="date != null">
            HAVING MAX(mspr.update_date) &lt;= #{date}
        </if>
        ORDER by msi.supplier_info_id, msi.material_id, msi.supplier_id
    </select>

    <select id="pageMaterialForApplet"
            resultType="com.nsy.api.scm.dto.domain.material.MaterialPageForAppletDTO">
        SELECT
            info.supplier_info_id,
            info.supplier_id,
            m.material_id
        FROM
            material m
        INNER JOIN
            material_supplier_info info ON m.material_id = info.material_id
        <where>
            AND info.is_delete = 0 AND info.enable_status = 'ENABLE'
            <if test="request.materialOrSupShortName != null and request.materialOrSupShortName != ''">
                and info.supplier_material_name like concat('%', #{request.materialOrSupShortName}, '%')
                <if test="request.supplierIds != null and request.supplierIds.size() > 0">
                    or info.supplier_id in
                    <foreach collection="request.supplierIds" item="supplierId" separator="," open="(" close=")">
                        #{supplierId}
                    </foreach>
                </if>
            </if>
        </where>
        ORDER BY info.supplier_info_id
    </select>
    <select id="getInPurchaseByIds" resultType="com.nsy.api.scm.dto.domain.material.MaterialSupplierInfoDto">
        select
            info.supplier_info_id
        from material_supplier_info info
        inner join material_supplier_color_card color on info.supplier_info_id = color.material_supplier_info_id
        where info.enable_status = 'ENABLE' and info.is_delete = 0
        and color.is_enable = 1 and color.is_delete = 0 and color.identification = 'BULK'
        and info.supplier_info_id in
        <foreach collection="materialSupplierInfoIds" item="materialSupplierInfoId" separator="," open="(" close=")">
            #{materialSupplierInfoId}
        </foreach>
    </select>
    <select id="getInPurchaseByMaterialIds"
            resultType="com.nsy.api.scm.dto.domain.material.MaterialSupplierInfoDto">
        select
            m.material_id
        from material m
        inner join material_supplier_info info on info.material_id = m.material_id
        inner join material_supplier_color_card color on info.supplier_info_id = color.material_supplier_info_id
        where m.enable_status = 'ENABLE'
        and info.enable_status = 'ENABLE' and info.is_delete = 0
        and color.is_enable = 1 and color.is_delete = 0 and color.identification = 'BULK'
        and m.material_id in
        <foreach collection="materialIds" item="materialId" separator="," open="(" close=")">
            #{materialId}
        </foreach>
    </select>

    <select id="queryWholePriceLessThanBomPriceSupplierInfoIdList"
            resultType="com.nsy.api.scm.dto.domain.material.ColorCardPriceDiffDto">
        select distinct material_supplier_info_id, color_card_id, material_price whole_price from (
            select
            cc.material_supplier_info_id ,
            cc.color_card_id ,
            cc.whole_price,
            cc.unit_price ,
            case when m.material_category = 'FABRIC' then cc.whole_price
            when m.material_category = 'ACCESSORIES' then   cc.unit_price
            else cc.whole_price
            end material_price,
            psbmo.price,
            m.material_category,
            case when m.material_category = 'FABRIC' then psbmo.price - cc.whole_price
            when m.material_category = 'ACCESSORIES' then psbmo.price - cc.unit_price
            else psbmo.price - cc.whole_price
            end price_diff
            from nsy_scm.material_supplier_color_card cc
            inner join nsy_scm.material_supplier_info msi on cc.material_supplier_info_id  = msi.supplier_info_id
            inner join nsy_scm.material m on cc.material_id  = m.material_id
            inner join nsy_scm.product_skc_bom_material_offering psbmo  on cc.color_card_id  = psbmo.color_card_id
            inner join nsy_scm.product_bom pb on psbmo.product_bom_id = pb.product_bom_id
            inner join nsy_scm.product p on p.product_id = pb.product_id and p.status in ('develop','complete')
            inner join nsy_scm.supplier s on s.supplier_id = msi.supplier_id and s.cooperate_status = 'IN_COOPERATION'
            left join nsy_scm.flow_task_edit_bom teb on teb.product_id = pb.product_id and teb.status not in ('COMPLETE','CANCEL')
            where
            msi.is_delete  = 0 and msi.enable_status = 'ENABLE'
            and teb.flow_task_edit_bom_id is null
            and cc.is_enable = 1 and cc.is_delete = 0
            order by material_supplier_info_id, color_card_id
        ) a where a.price_diff > #{lessLimit}
    </select>


    <select id="getMaterialSupplierByMaterialCategory" resultType="com.nsy.api.scm.dto.domain.material.MaterialSupplierInfoDto">
        select supplier_info_id,supplier_material_name from  material_supplier_info  ms
         inner join  material  m  on  ms.material_id =  m.material_id
        where ms.is_delete = 0 and  m.material_category  =  #{materialCategory}
    </select>

    <select id="priceNoChangeIPage"
            resultType="com.nsy.api.scm.dto.response.material.MaterialSupplierPriceNoChangePageRes">
        select
            msi.supplier_info_id materialSupplierInfoId,
            msi.supplier_material_name,
            msi.develop_emp_name,
            msi.create_by_company,
            m.material_name,
            m.material_category,
            s.supplier_short_name,
            MAX(mspr.update_date) updateDate,
            mspr.update_by,
            sum(ifnull(t.should_consumption_before_sixty, 0)) as shouldConsumptionBeforeSixty
        from material_supplier_info msi
        inner join material m on msi.material_id = m.material_id
        inner join supplier s on msi.supplier_id = s.supplier_id
        inner join (
            select material_supplier_info_id,MAX(update_date) update_date, update_by
            from material_supplier_price_record mspr
            group by material_supplier_info_id
            <if test="request.date != null">
                HAVING MAX(update_date) &lt;= #{request.date}
            </if>
        ) mspr on mspr.material_supplier_info_id = msi.supplier_info_id
        inner join material_supplier_color_card card on card.material_supplier_info_id = msi.supplier_info_id
        left join material_supplier_color_card_statistics t on card.color_card_id = t.color_card_id
        <where>
            msi.enable_status = 'ENABLE' and msi.is_delete = 0
            <if test="request.materialName != null and request.materialName != ''">
                and m.material_name like concat(#{request.materialName}, '%')
            </if>
            <if test="request.supplierMaterialName != null and request.supplierMaterialName != ''">
                and msi.supplier_material_name like concat(#{request.supplierMaterialName}, '%')
            </if>
            <if test="request.materialSupplierId != null">
                and msi.supplier_id = #{request.materialSupplierId}
            </if>
            <if test="request.materialCategory != null and request.materialCategory != ''">
                and m.material_category = #{request.materialCategory}
            </if>
            <if test="request.developEmpId != null">
                and msi.develop_emp_id = #{request.developEmpId}
            </if>
            <if test="request.updateBy != null and request.updateBy != ''">
                and mspr.update_by = #{request.updateBy}
            </if>
            <if test="request.createByCompany != null">
                and msi.create_by_company = #{request.createByCompany}
            </if>
            <if test="request.updateDateStart != null">
                and mspr.update_date >= #{request.updateDateStart}
            </if>
            <if test="request.updateDateEnd != null">
                AND #{request.updateDateEnd} >= mspr.update_date
            </if>
            <if test="request.selectIdList != null and request.selectIdList.size() != 0">
                and msi.supplier_info_id in
                <foreach collection="request.selectIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        GROUP BY mspr.material_supplier_info_id
        <choose>
            <when test="request.sortField != null and request.sortField != '' and request.sortOrder != null and request.sortOrder !='' ">
                order by ${request.sortField} ${request.sortOrder}, msi.supplier_info_id, msi.material_id, msi.supplier_id
            </when>
            <otherwise>
                order by shouldConsumptionBeforeSixty desc, msi.supplier_info_id, msi.material_id, msi.supplier_id
            </otherwise>
        </choose>
    </select>
</mapper>
