<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.supplier.BdProductProductionCycleLogMapper">
	<!-- 分页查询商品生产周期日志 -->
	<select id="pageLog" resultType="com.nsy.scm.business.response.supplier.BdProductProductionCycleLogPageResponse">
		SELECT log.log_id,
		       log.event,
		       log.spu,
		       log.skc,
		       log.content,
		       log.operate_user_id,
		       log.operate_user_name,
		       log.create_date
		FROM bd_product_production_cycle_log log
		<where>
			<!-- 根据SPU筛选 -->
			<if test="request.spu != null and request.spu != ''">
				AND log.spu = #{request.spu}
			</if>
			<!-- 根据SKC筛选 -->
			<if test="request.skc != null and request.skc != ''">
				AND log.skc = #{request.skc}
			</if>
			<!-- 根据SPU集合筛选 -->
			<if test="request.spuList != null and request.spuList.size() > 0">
				AND log.spu IN
				<foreach collection="request.spuList" item="spu" open="(" separator="," close=")">
					#{spu}
				</foreach>
			</if>
			<!-- 根据SKC集合筛选 -->
			<if test="request.skcList != null and request.skcList.size() > 0">
				AND log.skc IN
				<foreach collection="request.skcList" item="skc" open="(" separator="," close=")">
					#{skc}
				</foreach>
			</if>
			<!-- 根据操作开始时间筛选 -->
			<if test="request.startTime != null">
				AND log.create_date >= #{request.startTime}
			</if>
			<!-- 根据操作结束时间筛选 -->
			<if test="request.endTime != null">
				AND log.create_date &lt;= #{request.endTime}
			</if>
		</where>
		ORDER BY log.create_date DESC
	</select>
</mapper>
