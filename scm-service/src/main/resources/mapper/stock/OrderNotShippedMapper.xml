<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.stock.OrderNotShippedMapper">
    <select id="findFirstUnApplySpotLackSpecOrder" resultType="java.lang.Integer">
        select 1
        from order_not_shipped ons
                     inner join product_spec ps on ons.erp_spec_id = ps.erp_spec_id
                     inner join product p on ps.product_id = p.product_id
        where ons.num > ons.prematch_num
          and ps.market_supply_status in ('PendingCheck', 'CanPurchase')
          and p.supply_status in ('market_supply_buy', 'market_supply_pending')
        limit 1
    </select>


    <select id="getAllBusinessType" resultType="java.lang.String">
        select distinct ons.business_type
        from order_not_shipped ons
        <where>
            <if test="location != null and location != ''">
                and ons.location = #{location}
            </if>
        </where>
    </select>

    <select id="findSpotLackSpecOrderLack" resultType="java.lang.Integer">
        select ons.not_shop_order_id
        from order_not_shipped ons
                     inner join product_spec ps on ons.erp_spec_id = ps.erp_spec_id
                     inner join product p on ps.product_id = p.product_id
        where ons.business_type = #{businessType}
          and ons.num > ons.prematch_num
          and ps.market_supply_status in ('PendingCheck', 'CanPurchase')
          and p.supply_status in ('market_supply_buy', 'market_supply_pending')
          and ons.not_shop_order_id > #{minId}
        order by ons.not_shop_order_id
    </select>

    <select id="countAllSpotLack" resultType="java.lang.Integer">
        select sum(ons.num - ons.prematch_num)
        from order_not_shipped ons
                     inner join product_spec ps on ons.erp_spec_id = ps.erp_spec_id
                     inner join product p on ps.product_id = p.product_id
        where ons.erp_spec_id = #{erpSpecId}
          and ons.num > ons.prematch_num
          and ps.market_supply_status in ('PendingCheck', 'CanPurchase')
          and p.supply_status in ('market_supply_buy', 'market_supply_pending')
    </select>

    <select id="getFirstOrderLackDate" resultType="com.nsy.scm.repository.projection.purchase.ErpFirstOrderLackDate">
        select o.erp_spec_id as erpSpecId, min(o.pay_time) as firstOrderLackDate from order_not_shipped o
        where o.erp_spec_id in
        <foreach collection="erpSpecIds" open="(" close=")" separator="," item="erpSpecId">
            #{erpSpecId}
        </foreach>
        and o.pay_time is not null
        group by o.erp_spec_id
    </select>

    <select id="findLackErpSpecIds" resultType="java.lang.Integer">
        select ons.erp_spec_id
        from order_not_shipped ons
                     inner join product_spec ps on ons.erp_spec_id = ps.erp_spec_id
                     inner join product p on ps.product_id = p.product_id
        where ons.num > ons.prematch_num
          and ps.market_supply_status in ('PendingCheck', 'CanPurchase')
          and p.supply_status in ('market_supply_buy', 'market_supply_pending')
          and ons.erp_spec_id > #{minErpSpecId}
        order by ons.erp_spec_id
    </select>

    <select id="batchCountAllSpotLack" resultType="com.nsy.scm.repository.projection.purchase.SpotSpecLackProjection">
        select ons.erp_spec_id as erpSpecId, sum(ons.num - ons.prematch_num) as lackQty
        from order_not_shipped ons
        where ons.erp_spec_id in
        <foreach collection="erpSpecIds" open="(" close=")" separator="," item="erpSpecId">
            #{erpSpecId}
        </foreach>
          and ons.num > ons.prematch_num
        group by ons.erp_spec_id
    </select>
</mapper>
