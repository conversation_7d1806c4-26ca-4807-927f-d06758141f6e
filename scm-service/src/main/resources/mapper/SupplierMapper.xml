<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.SupplierMapper">

    <select id="iPage" resultType="com.nsy.scm.business.response.supplier.SupplierPageResponse">
        select distinct
        supplier.supplier_id,supplier.supplier_code,supplier.supply_type,supplier.supplier_no,supplier.supplier_short_name,
               supplier.business_specialist_name,
        supplier.supplier_name,supplier.supplier_level,supplier.supplier_type,
        supplier.model_merchandiser_emp_name,supplier.buyer_name as contact_purchaser_emp_name,supplier.model_merchandiser_emp_id,
        supplier.contact_qc_emp_code, supplier.contact_qc_emp_id, supplier.contact_qc_emp_name,
        supplier_address.contacts_name,supplier_address.contacts_phone,
        supplier.cooperate_status,supplier.affiliate_dept_code,supplier.affiliate_dept_id,supplier.buyer_user_code as contact_purchaser_emp_code,supplier.buyer_user_id as contact_purchaser_emp_id,
        supplier_address.country,supplier_address.province,supplier_address.city,supplier_address.district,supplier_address.address,supplier.cooperate_date,supplier.access_date,
        supplier.access_apply_date,supplier.update_date,supplier.update_by,supplier.cooperation_model
        ,supplier.create_date,supplier.create_by
        ,supplier.system_account,supplier.fabric_merchandiser_id,supplier.fabric_merchandiser_name,supplier.currency,supplier.identification
        <if test="request.orderBys !=null and request.orderBys.size >0">
            , case supplier.identification
                when 'BULK' then 1
                when 'SPOT' then 2
                when 'OTHER' then 3
                else 9 end identification_sort
        </if>
        from supplier supplier
        inner join supplier_permission on supplier.supplier_id = supplier_permission.supplier_id
        left join supplier_address supplier_address on supplier.supplier_id = supplier_address.supplier_id and
        address_type = 1
            <include refid="SupplierPageRequestJoin"></include>
            <include refid="SupplierPageRequestWhere"></include>
            <if test="request.orderBys !=null and request.orderBys.size >0">
                order by
                <foreach collection="request.orderBys " item="item" separator=",">
                    ${item}
                </foreach>
            </if>
    </select>

    <sql id="SupplierPageRequestJoin">
        <if test="request.selfDelivery != null">
            inner join supplier_production_capacity as capacity
            on supplier.supplier_id = capacity.supplier_id
        </if>
        <if test="request.canInvoice != null or request.isFinancialRisk != null">
            inner join supplier_settlement as settlement
            on supplier.supplier_id = settlement.supplier_id
        </if>
        <if test="request.signOutDateStart != null and request.signOutDateEnd != null">
            inner join supplier_log as log
            on supplier.supplier_id = log.supplier_id
            and log.create_date between #{request.signOutDateStart} and #{request.signOutDateEnd}
            and log.operate_type = #{request.logOperateType}
        </if>
        <if test="request.isIncludeCourierFees != null">
            inner join supplier_tax as tax
            on supplier.supplier_id = tax.supplier_id
            and tax.is_deleted = 0
            and tax.is_include_courier_fees = #{request.isIncludeCourierFees}
        </if>
    </sql>

    <sql id="SupplierPageRequestWhere" >
        where supplier.is_delete = 0
        <if test="request.isContract != null">
	        and supplier.is_contract = #{request.isContract}
        </if>
        <if test="request.selfDelivery != null">
	        and capacity.self_delivery = #{request.selfDelivery}
        </if>
        <if test="request.canInvoice != null">
            and settlement.can_invoice = #{request.canInvoice}
        </if>
        <if test="request.isFinancialRisk != null">
            and settlement.is_financial_risk = #{request.isFinancialRisk}
        </if>


        <if test="request.supplierNameList != null and request.supplierNameList.size() != 0">
            and supplier.supplier_name in
            <foreach collection="request.supplierNameList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="request.supplierShortNameList != null and request.supplierShortNameList.size() != 0">
            and supplier.supplier_short_name in
            <foreach collection="request.supplierShortNameList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="request.supplierNo !=null and request.supplierNo !=''">
            and supplier.supplier_no like concat('%', #{request.supplierNo}, '%')
        </if>
        <if test="request.supplierIds != null and request.supplierIds.size() > 0">
            and supplier.supplier_id in
            <foreach collection="request.supplierIds " item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.selectIdList != null and request.selectIdList.size() != 0">
	        and supplier.supplier_id in
            <foreach collection="request.selectIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="request.supplierNos != null and request.supplierNos.size() > 0">
            and supplier.supplier_no in
            <foreach collection="request.supplierNos " item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.supplierCode !=null and request.supplierCode !=''">
            and supplier.supplier_code = #{request.supplierCode}
        </if>
        <if test="request.businessSpecialistId !=null">
            and supplier.business_specialist_id = #{request.businessSpecialistId}
        </if>

        <if test="request.supplierType !=null and request.supplierType.size >0 ">
            and supplier.supplier_type in
            <foreach collection="request.supplierType " item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.extensionTypeValues !=null and request.extensionTypeValues.size >0 ">
            and exists
            (
            select 1 from supplier_extension as s
            <where>
                supplier.supplier_id = s.supplier_id and s.extension_type_key = #{request.productCategory}
                and s.extension_type_value in
                <foreach collection="request.extensionTypeValues " item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </where>
            )
        </if>
        <if test="request.fabricTypeValues !=null and request.fabricTypeValues.size >0">
            and exists
            (
            select 1 from supplier_extension as s
            <where>
                supplier.supplier_id = s.supplier_id and s.extension_type_key = #{request.scmFabricType}
                and s.extension_type_value in
                <foreach collection="request.fabricTypeValues " item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </where>
            )
        </if>
        <if test="request.productionTypeValues !=null and request.productionTypeValues.size >0">
            and exists
            (
            select 1 from supplier_extension as s
            <where>
                supplier.supplier_id = s.supplier_id and s.extension_type_key = #{request.scmProductionType}
                and s.extension_type_value in
                <foreach collection="request.productionTypeValues " item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </where>
            )
        </if>

        <if test="request.supplierLevel !=null and request.supplierLevel !=''">
            and supplier.supplier_level = #{request.supplierLevel}
        </if>
        <if test="request.supplyType !=null and request.supplyType !=''">
            and supplier.supply_type = #{request.supplyType}
        </if>
        <if test="request.cooperateStatus !=null and request.cooperateStatus !=''">
            and supplier.cooperate_status = #{request.cooperateStatus}
        </if>
        <if test="request.cooperateStatusList !=null and request.cooperateStatusList.size >0">
            and supplier.cooperate_status in
            <foreach collection="request.cooperateStatusList " item="cooperateStatus" separator="," open="(" close=")">
                #{cooperateStatus}
            </foreach>
        </if>
        <if test="request.affiliateDeptCodeList !=null and request.affiliateDeptCodeList.size >0">
            and supplier.affiliate_dept_code in
            <foreach collection="request.affiliateDeptCodeList " item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.affiliateDeptIds !=null and request.affiliateDeptIds.size >0">
            and supplier.affiliate_dept_id in
            <foreach collection="request.affiliateDeptIds " item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.contactPurchaserEmpCode !=null and request.contactPurchaserEmpCode.size >0">
            and supplier.buyer_user_code in
            <foreach collection="request.contactPurchaserEmpCode " item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.contactPurchaserEmpIds !=null and request.contactPurchaserEmpIds.size >0">
            and supplier.buyer_user_id in
            <foreach collection="request.contactPurchaserEmpIds " item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.modelMerchandiserEmpIds !=null and request.modelMerchandiserEmpIds.size >0">
            and supplier.model_merchandiser_emp_id in
            <foreach collection="request.modelMerchandiserEmpIds " item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.fabricMerchandiserIds !=null and request.fabricMerchandiserIds.size >0">
            and supplier.fabric_merchandiser_id in
            <foreach collection="request.fabricMerchandiserIds " item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.country !=null and request.country !=''">
            and supplier_address.country = #{request.country}
        </if>
        <if test="request.province !=null and request.province !=''">
            and supplier_address.province = #{request.province}
        </if>
        <if test="request.city !=null and request.city !=''">
            and supplier_address.city = #{request.city}
        </if>
        <if test="request.district !=null and request.district !=''">
            and supplier_address.district = #{request.district}
        </if>
        <if test="request.cooperateDate !=null">
            and supplier.cooperate_date = DATE_FORMAT(#{request.cooperateDate},'%Y-%m-%d')
        </if>
        <if test="request.cooperateDateStart !=null">
            and supplier.cooperate_date >= DATE_FORMAT(#{request.cooperateDateStart},'%Y-%m-%d')
        </if>
        <if test="request.cooperateDateEnd !=null">
            and DATE_FORMAT(#{request.cooperateDateEnd},'%Y-%m-%d') >= supplier.cooperate_date
        </if>
        <if test="request.cooperationMode !=null and request.cooperationMode !=''">
            and supplier.cooperation_model = #{request.cooperationMode}
        </if>
        <if test="request.inPurchase !=null">
            <if test="request.inPurchase == 1">and supplier.identification = 'BULK'</if>
            <if test="request.inPurchase == 0">and supplier.identification != 'BULK'</if>
        </if>
    </sql>

    <select id="pageIds" resultType="Integer">
        select supplier.supplier_id
        <if test="request.orderBys !=null and request.orderBys.size >0">
            , case supplier.identification
            when 'BULK' then 1
            when 'SPOT' then 2
            when 'OTHER' then 3
            else 9 end identification_sort
        </if>
        from supplier supplier
        inner join supplier_permission on supplier.supplier_id = supplier_permission.supplier_id
        left join supplier_address supplier_address on supplier.supplier_id = supplier_address.supplier_id and
        address_type = 1

        <include refid="SupplierPageRequestJoin"></include>
        <include refid="SupplierPageRequestWhere"></include>
            <if test="request.orderBys !=null and request.orderBys.size >0">
                order by
                <foreach collection="request.orderBys " item="item" separator=",">
                    ${item}
                </foreach>
            </if>
    </select>

    <select id="cooperationSupplierList" resultType="com.nsy.scm.business.response.supplier.CooperationSupplierResponse">
        SELECT
        supplier_id AS supplierId,
        supplier_name AS supplierName
        FROM
        supplier
        <where>
            <if test="status != null and status !=''">
                cooperate_status = #{status}
            </if>
            <if test="supplierType != null and supplierType !=''">
                and supplier_type != #{supplierType}
            </if>
        </where>
    </select>

    <select id="getAllTabCountMap" resultType="java.util.HashMap">
        SELECT cooperate_status,count(*) count FROM supplier
        inner join supplier_permission on supplier.supplier_id = supplier_permission.supplier_id
        <where>
            is_delete = 0
            <if test="supplierType != null and supplierType !=''">
                and supplier_type = #{supplierType}
            </if>
        </where>
        group by cooperate_status
    </select>

    <select id="getSupplierNoNumList" resultType="java.lang.Integer">
        SELECT DISTINCT supplier_no_num
        FROM
        supplier
        <where>
            is_delete = 0
            <if test="supplierType != null">
                and supplier_type = #{supplierType}
            </if>
            <if test="minSupplierNoNum != null">
                and supplier_no_num > #{minSupplierNoNum}
            </if>
        </where>
        order by supplier_no_num asc
    </select>

    <select id="getSupplierDropDownList" resultType="com.nsy.scm.repository.entity.SupplierEntity">
        SELECT s.*,
        s.buyer_user_id as contactPurchaserEmpId,
        s.buyer_name as contactPurchaserEmpName
        FROM
        supplier s
        inner join supplier_permission sp on s.supplier_id = sp.supplier_id
        <where>
            s.is_delete = 0
            <if test="cooperateStatus != null and cooperateStatus !=''">
                and s.cooperate_status = #{cooperateStatus}
            </if>
            <if test="supplierTypes != null and supplierTypes.size > 0">
                and s.supplier_type in
                <foreach collection="supplierTypes" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="supplyType != null and supplyType !=''">
                and s.supply_type = #{supplyType}
            </if>
        </where>
    </select>

    <select id="querySupplierSelector" resultType="com.nsy.scm.repository.entity.SupplierEntity">
        SELECT s.*,
        s.buyer_user_id as contactPurchaserEmpId,
        s.buyer_name as contactPurchaserEmpName
        FROM
        supplier s
        inner join supplier_permission on s.supplier_id = supplier_permission.supplier_id
        <where>
            s.is_delete = 0
            <if test="request.cooperateStatusSet != null and request.cooperateStatusSet.size() > 0">
                and s.cooperate_status in
                <foreach collection="request.cooperateStatusSet " item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.supplierType != null and request.supplierType !=''">
                and s.supplier_type = #{request.supplierType}
            </if>
            <if test="request.supplierTypes != null and request.supplierTypes.size > 0">
                and s.supplier_type in
                <foreach collection="request.supplierTypes" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.sycSupplierName != null and request.sycSupplierName !=''">
                and s.supplier_name like concat('%', #{request.sycSupplierName}, '%')
            </if>
            <if test="request.supplierShortName != null and request.supplierShortName !=''">
                and s.supplier_short_name = #{request.supplierShortName}
            </if>
        </where>
    </select>

    <select id="getSupplierDropDown" resultType="com.nsy.scm.business.domain.supplier.SupplierDropDownDto">
        <include refid="getSupplierDropDown"></include>
    </select>

    <sql id="getSupplierDropDown">
        SELECT s.supplier_id as supplierId,
        s.supplier_code as supplierCode,
        s.supplier_name as supplierName,
        s.supplier_short_name as supplierShortName,
        s.supplier_no as supplierNo,
        s.supplier_type as supplierType,
        s.buyer_user_id as buyerUserId,
        s.buyer_name as buyerName,
        s.cooperate_status as cooperateStatus
        FROM
        supplier s
        inner join supplier_permission sp on s.supplier_id = sp.supplier_id
        <if test="request.spuAutoMatchList != null and request.spuAutoMatchList.size() > 0">
            inner join
            (
            select DISTINCT spd.supplier_id from supplier_product spd where spd.spu in
            <foreach collection="request.spuAutoMatchList" open="(" close=")" separator="," item="spu">
                #{spu}
            </foreach> and spd.is_deleted = 0
            ) tmp on tmp.supplier_id = s.supplier_id
        </if>
        <where>
            <!--  1=1 占位符，权限框架，会把1=1 替换为目标sql -->
            1=1 and s.is_delete = 0
            <if test="request.cooperateStatus != null and request.cooperateStatus !=''">
                and s.cooperate_status = #{request.cooperateStatus}
            </if>
            <if test="request.supplierTypeList !=null and request.supplierTypeList.size >0 ">
                and s.supplier_type in
                <foreach collection="request.supplierTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="request.cooperateStatusList !=null and request.cooperateStatusList.size >0 ">
                and s.cooperate_status in
                <foreach collection="request.cooperateStatusList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>

    <select id="getIncludeQuanZhouSupplierDropDown" resultType="com.nsy.scm.business.domain.supplier.SupplierDropDownDto">
        SELECT s.supplier_id as supplierId,
        s.supplier_code as supplierCode,
        s.supplier_name as supplierName,
        s.supplier_short_name as supplierShortName,
        s.supplier_no as supplierNo,
        s.supplier_type as supplierType,
        s.buyer_user_id as buyerUserId,
        s.buyer_name as buyerName
        FROM
        supplier s
        inner join supplier_permission sp on s.supplier_id = sp.supplier_id
        <if test="request.spuAutoMatchList != null and request.spuAutoMatchList.size() > 0">
            inner join
            (
            select DISTINCT spd.supplier_id from supplier_product spd where spd.spu in
            <foreach collection="request.spuAutoMatchList" open="(" close=")" separator="," item="spu">
                #{spu}
            </foreach> and spd.is_deleted = 0
            ) tmp on tmp.supplier_id = s.supplier_id
        </if>
        <where>
            <!--  1=1 占位符，权限框架，会把1=1 替换为目标sql -->
            1=1 and s.is_delete = 0
            <if test="location != null and location !=''">
                and s.location in ('QUANZHOU', #{location})
            </if>
            <if test="request.cooperateStatus != null and request.cooperateStatus !=''">
                and s.cooperate_status = #{request.cooperateStatus}
            </if>
            <if test="request.supplierTypeList !=null and request.supplierTypeList.size >0 ">
                and s.supplier_type in
                <foreach collection="request.supplierTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="querySupplierPage" resultType="com.nsy.scm.repository.entity.SupplierEntity">
        SELECT s.*,
        s.buyer_user_id as contactPurchaserEmpId,
        s.buyer_name as contactPurchaserEmpName
        FROM
        supplier s
        inner join supplier_permission on s.supplier_id = supplier_permission.supplier_id
        <where>
            <if test="request.cooperateStatus != null and request.cooperateStatus !=''">
                and s.cooperate_status = #{request.cooperateStatus}
            </if>
            <if test="request.supplierType != null and request.supplierType !=''">
                and s.supplier_type = #{request.supplierType}
            </if>
            <if test="request.sycSupplierName != null and request.sycSupplierName !=''">
                and s.supplier_name = #{request.sycSupplierName}
            </if>
            <if test="request.supplierShortName != null and request.supplierShortName !=''">
                and s.supplier_short_name = #{request.supplierShortName}
            </if>
        </where>
    </select>

    <select id="getNotSystemSupplierAndInCooperationList"
            resultType="com.nsy.scm.repository.entity.SupplierEntity">
        select supplier_id, supplier_code, supplier_no, system_account, supplier_type
        from supplier
        where
            ifnull(supplier_code, '') != ''
            and supplier_type = 'FABRIC_SUPPLIER'
            and cooperate_status = 'IN_COOPERATION'
            and is_delete = 0
            and ifnull(system_account, '') = ''
    </select>
    <select id="getSupplierDropDownWithoutPermission"
            resultType="com.nsy.scm.business.domain.supplier.SupplierDropDownDto">
        <include refid="getSupplierDropDown"></include>
    </select>

    <update id="addOrderCount">
        UPDATE supplier set order_count = order_count + #{orderCount} where supplier_id = #{supplierId};
    </update>
</mapper>
