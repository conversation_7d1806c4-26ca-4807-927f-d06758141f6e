<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nsy.scm.repository.sql.mapper.ProcessPlanLogMapper">
    <select id="showStatusLog" resultType="com.nsy.scm.business.domain.process.PlanStatusLog">
        SELECT
            process_plan_log.plan_status AS STATUS,
            min(process_plan_log.create_date) AS operateDate
        FROM
            process_plan_log process_plan_log
        WHERE
            process_plan_log.process_plan_id = #{planId}
        GROUP BY
            process_plan_log.plan_status
        ORDER BY operateDate
    </select>
</mapper>
