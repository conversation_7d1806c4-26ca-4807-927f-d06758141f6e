<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.recommendmaterial.FlowTaskRecommendMaterialSkcGroupMapper">
    <select id="getTaskItemStatus" resultType="com.nsy.scm.repository.entity.recommendmaterial.FlowTaskRecommendMaterialSkcGroupEntity">
        select
        ftrmsg.*
        from flow_task_recommend_material ftrm
        inner join flow_task_recommend_material_skc_group ftrmsg on ftrmsg.task_id = ftrm.task_id
        <where>
            ftrm.task_status not in (7) and (ftrm.task_status = 6 and ftrmsg.task_item_status = 6 or ftrm.task_status != 6)
            <if test="productId != null">
                and ftrm.product_id = #{productId}
            </if>
            <if test="skcList != null and skcList.size() != 0">
                and ftrmsg.skc in
                <foreach collection="skcList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY ftrm.task_id,ftrm.product_id,ftrmsg.skc
    </select>
</mapper>
