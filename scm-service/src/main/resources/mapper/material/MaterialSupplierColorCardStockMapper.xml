<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.material.MaterialSupplierColorCardStockMapper">

    <select id="queryUnCreateColorCardIds" resultType="java.lang.Integer">
        select a.color_card_id
        from nsy_scm.material_supplier_color_card a
        left join nsy_scm.material_supplier_color_card_stock b on a.color_card_id = b.color_card_id
        where b.material_color_card_stock_id is null
        and a.color_card_id in
        <foreach collection="colorCardIds" item="colorCardId" separator="," open="(" close=")">
            #{colorCardId}
        </foreach>
    </select>
</mapper>
