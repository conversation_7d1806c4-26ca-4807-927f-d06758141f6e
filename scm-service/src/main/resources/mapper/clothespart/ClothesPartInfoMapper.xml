<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.clothespart.ClothesPartInfoMapper">

    <select id="pageQueryMeasureMethod"
            resultType="com.nsy.api.scm.dto.response.clothespart.ClothesPartMeasureMethodResponse">
        select * from (
            select
            cpi.part_id,
            mmc.config_id,
            cpi.part_name_chinese,
            cpi.part_name_english,
            mmc.measure_method,
            cpi.is_sales_display,
            mmc.create_by,
            mmc.update_by,
            case when mmc.dimension_type = '' then 'NUM'
            else mmc.dimension_type end as dimension_type,
            case when mmc.dimension_type = 'CHAR' then '字母码'
            else '数字码' end as dimension_type_desc,
            mmc.update_date
            from nsy_scm.clothes_part_info cpi
            left join nsy_scm.bd_clothes_part_measure_method_config mmc on mmc.part_id = cpi.part_id and mmc.is_deleted = 0
            <where>
                <if test="request.partNameChinese != null and request.partNameChinese != ''">
                    and cpi.part_name_chinese = #{request.partNameChinese}
                </if>
                <if test="request.partNameEnglish != null and request.partNameEnglish != ''">
                    and cpi.part_name_english = #{request.partNameEnglish}
                </if>
                <if test="request.measureMethod != null and request.measureMethod != ''">
                    and mmc.measure_method like concat(#{request.measureMethod}, '%')
                </if>
                <if test="request.isSalesDisplay != null ">
                    and cpi.is_sales_display = #{request.isSalesDisplay}
                </if>
                and cpi.is_deleted = 0
            </where>
            order by cpi.update_date desc, cpi.part_name_chinese
        ) cpiv

    </select>
    <select id="getAllPartChineseOptions" resultType="java.lang.String">
        select
        distinct cpi.part_name_chinese
        from nsy_scm.clothes_part_info cpi
        left join nsy_scm.bd_clothes_part_measure_method_config mmc on cpi.part_id = mmc.part_id and mmc.is_deleted = 0
        where cpi.is_deleted = 0 and cpi.part_name_chinese is not null and cpi.part_name_chinese != ''
    </select>

    <select id="getAllPartEnglishOptions" resultType="java.lang.String">
        select
        distinct cpi.part_name_english
        from nsy_scm.clothes_part_info cpi
        inner join nsy_scm.bd_clothes_part_measure_method_config mmc on cpi.part_id = mmc.part_id and mmc.is_deleted = 0
        where cpi.is_deleted = 0 and cpi.part_name_english is not null and cpi.part_name_english != ''
    </select>
    
    
    <select id="pageQueryClothesPartTable" resultType="com.nsy.api.scm.dto.response.clothespart.ClothesPartMeasureMethodTableResponse">
	SELECT cpi.part_id, cpi.part_name_chinese, cpi.part_name_english, cpi.is_sales_display, cptmmi.sort
	FROM clothes_part_info cpi
	INNER JOIN bd_clothes_part_measure_method_config bcpmmc ON cpi.part_id = bcpmmc.part_id and bcpmmc.is_deleted = 0
	INNER JOIN clothes_part_template_measure_method_item cptmmi ON cptmmi.measure_method_config_id = bcpmmc.config_id and cptmmi.is_deleted = 0
	where
	 cpi.is_deleted = 0
        <if test="request.templateId != null">
            and cptmmi.template_id = #{request.templateId}
        </if>
        <if test="request.partId != null">
            and cpi.part_id = #{request.partId}
        </if>
        <if test="request.partIds != null and request.partIds.size() != 0">
            and cpi.part_id in
            <foreach collection="request.partIds" open="(" close=")" separator="," item="partId">
                #{partId}
            </foreach>
        </if>
        group by cpi.part_id
        order by cptmmi.sort
    </select>
    <select id="getPartOptionByTemplateId" resultType="com.nsy.api.scm.dto.domain.SelectModel">
        SELECT cpi.part_id `value`, cpi.part_name_chinese label
        from nsy_scm.clothes_part_info cpi
                 inner join nsy_scm.bd_clothes_part_measure_method_config bcpmmc
                            on cpi.part_id = bcpmmc.part_id and bcpmmc.is_deleted = 0
                 INNER JOIN clothes_part_template_measure_method_item cptmmi
                            ON cptmmi.measure_method_config_id = bcpmmc.config_id and cptmmi.is_deleted = 0
        where cpi.is_deleted = 0
          and cpi.part_name_chinese is not null
          and cpi.part_name_chinese != ''
        <if test="templateId != null">
            and cptmmi.template_id = #{templateId}
        </if>
        group by cpi.part_id
    </select>
</mapper>
