<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.ProductScmCategoryLogMapper">
    <select id="logList" resultType="com.nsy.scm.business.response.base.LogPageResponse">
        SELECT
            l.scm_category_log_type type,
            l.content,
            l.create_by,
            l.create_date
        FROM
            product_scm_category_log l
        <where>
            <if test="request.type != null and request.type != '' ">
                and l.scm_category_log_type = #{request.type}
            </if>
            <if test="request.content != null and request.content != '' ">
                and l.content like concat('%',#{request.content},'%')
            </if>
            <if test="request.operator != null and request.operator !=''">
                and l.create_by like concat('%',#{request.operator},'%')
            </if>
            <if test="request.startDate != null">
                and l.create_date &gt;= #{request.startDate}
            </if>
            <if test="request.endDate != null">
                and l.create_date &lt;= #{request.endDate}
            </if>
        </where>
        order by l.scm_category_log_id desc
    </select>
</mapper>
