<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.product.ProductWorkmanshipChildVersionMapper">


    <select id="queryProductWorkmanshipChildVersion"
            resultType="com.nsy.scm.business.response.product.ProductWorkmanshipHistoryChildVersionDto">
                 SELECT v.product_id, cv.edit_content,cv.source,cv.`child_version_id` AS version_id,CONCAT(v.`version_no`,'-',cv.`child_version_no`)AS VERSION,cv.`update_date`,cv.`update_by`,v.revision_flow_task_id,t.edit_reason,t.submit_edit_reason,t.submit_edit_content,cv.workmanship_flow_task_id,t.task_type FROM `product_workmanship_version` v
        INNER JOIN `product_workmanship_child_version` cv ON cv.`product_version_id` = v.`version_id`
        LEFT JOIN `flow_task_product_workmanship` t ON t.flow_task_product_workmanship_id = cv.workmanship_flow_task_id
        WHERE 1=1
        <if test="request.productId != null and request.productId > 0 ">
           and v.product_id = #{request.productId}
        </if>
        <if test="request.productIds != null and request.productIds.size() > 0 ">
            and v.product_id in
            <foreach item="item" collection="request.productIds" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.versionNo != null and request.versionNo != ''">
            and v.version_no = #{request.versionNo}
        </if>
        order by cv.`child_version_id` desc
    </select>
</mapper>
