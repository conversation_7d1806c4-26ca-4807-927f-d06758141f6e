<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.preparematerial.PrepareMaterialMapper">


    <select id="pageList" resultType="com.nsy.scm.business.response.reparematerial.PrepareMaterialPageResponse">
        SELECT max(p.prepare_material_id) as prepareMaterialId,p.skc
        FROM material_prepare_material p
        LEFT JOIN material_prepare_material_item pmi ON p.skc = pmi.skc
        and p.syc_supplier_id = pmi.syc_supplier_id
        <where>
            <include refid="whereByPrepareMaterialRequest"></include>
        </where>
        GROUP BY p.skc,p.syc_supplier_id

    </select>
    <sql id="whereByPrepareMaterialRequest">
        <if test="query.supplierIds != null and query.supplierIds.size() > 0">
            and p.syc_supplier_id in
            <foreach collection="query.supplierIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.spuList != null and query.spuList.size() > 0">
            and p.spu in
            <foreach collection="query.spuList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="query.skcList != null and query.skcList.size() > 0">
            and p.skc in
            <foreach collection="query.skcList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.createDateBegin != null">
            and p.create_date >= #{query.createDateBegin}
        </if>
        <if test="query.createDateEnd != null">
            and #{query.createDateEnd} > p.create_date
        </if>
        <if test="query.updateDateBegin != null">
            and p.update_date >= #{query.updateDateBegin}
        </if>
        <if test="query.updateDateEnd != null">
            and #{query.updateDateEnd} > p.update_date
        </if>

        <if test="query.createBy != null and query.createBy!=''">
            and p.create_by = #{query.createBy}
        </if>

        <if test="query.updateBy != null and query.updateBy!=''">
            and p.update_by = #{query.updateBy}
        </if>

        <if test="query.supplierShortName != null and query.supplierShortName!=''">
            and pmi.supplier_short_name = #{query.supplierShortName}
        </if>

        <if test="query.supplierMaterialName != null and query.supplierMaterialName!=''">
            and pmi.supplier_material_name = #{query.supplierMaterialName}
        </if>


        <if test="query.colorNumber != null and query.colorNumber!=''">
            and pmi.color_number = #{query.colorNumber}
        </if>

        <if test="query.prepareStatus != null and query.prepareStatus == 0">
            and pmi.inventory = 0
        </if>
        <if test="query.prepareStatus != null and query.prepareStatus == 1">
            and pmi.inventory > 0
        </if>
        <if test="query.prepareStatus != null and query.prepareStatus == 2">
            and pmi.is_bom = 0
        </if>

        <if test="query.status != null">
            and p.status = #{query.status}
        </if>
    </sql>


    <select id="pageMaterialList" resultType="com.nsy.scm.business.response.reparematerial.PrepareMaterialItemResponse">
        SELECT GROUP_CONCAT(pmi.prepare_material_item_id SEPARATOR ', ') as prepareMaterialItemIdStr
        FROM material_prepare_material_item pmi
        LEFT JOIN material_prepare_material p ON pmi.skc = p.skc

        <where>
            <include refid="whereByPrepareMaterialRequest"></include>
        </where>

        GROUP BY pmi.syc_supplier_id,pmi.supplier_material_name,pmi.color_number
    </select>

</mapper>
