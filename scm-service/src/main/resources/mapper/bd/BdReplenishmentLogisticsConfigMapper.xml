<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.bd.BdReplenishmentLogisticsConfigMapper">

    <resultMap id="BaseResultMap" type="com.nsy.scm.repository.entity.bd.BdReplenishmentLogisticsConfigEntity">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="configName" column="config_name" jdbcType="VARCHAR"/>
        <result property="fromWarehouse" column="from_warehouse" jdbcType="VARCHAR"/>
        <result property="marketplaceId" column="marketplace_id" jdbcType="VARCHAR"/>
        <result property="marketplaceChinese" column="marketplace_chinese" jdbcType="VARCHAR"/>
        <result property="localDays" column="local_days" jdbcType="SMALLINT"/>
        <result property="seaDays" column="sea_days" jdbcType="SMALLINT"/>
        <result property="airDays" column="air_days" jdbcType="SMALLINT"/>
        <result property="fbaPreparedStockDays" column="fba_prepared_stock_days" jdbcType="SMALLINT"/>
        <result property="isEnabled" column="is_enabled" jdbcType="TINYINT"/>
        <result property="location" column="location" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
    </resultMap>

</mapper>
