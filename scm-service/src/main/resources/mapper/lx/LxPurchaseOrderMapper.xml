<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.scm.repository.sql.mapper.lx.LxPurchaseOrderMapper">
	<select id="getSyncSupplierIds" resultType="java.lang.Long">
		SELECT distinct supplier_id
		FROM lx_purchase_order
		WHERE location = #{location}
		GROUP BY supplier_id
	</select>
</mapper>
