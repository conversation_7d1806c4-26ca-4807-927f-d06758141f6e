package com.nsy.api.scm.feign;


import com.nsy.api.scm.dto.domain.inventory.BusinessInventoryDto;
import com.nsy.api.scm.dto.request.inventory.BusinessInventoryPageRequest;
import com.nsy.api.scm.dto.request.inventory.QueryForAddDeliveryPlanRequest;
import com.nsy.api.scm.dto.response.common.PageResponse;
import com.nsy.api.scm.dto.response.inventory.QueryForAddDeliveryPlanResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "api-scm", contextId = "scm-factoryInventory")
@RequestMapping("factory-inventory")
@Api(tags = "工厂库存接口")
public interface FactoryInventoryFeignClient {

    @ApiOperation(value = "运营系统 - 业务库存 - 根据采购单+SKU批量查询可发库存、条码模板信息", notes = "运营系统 - 业务库存 - 根据采购单+SKU批量查询可发库存、条码模板信息")
    @RequestMapping(value = "/query-for-add-delivery-plan", method = RequestMethod.POST)
    List<QueryForAddDeliveryPlanResponse> queryForAddDeliveryPlan(@RequestBody @Valid QueryForAddDeliveryPlanRequest request);

    @ApiOperation(value = "运营系统 - 业务库存 - 分页查询", notes = "运营系统 - 业务库存 - 分页查询")
    @RequestMapping(value = "/business-inventory/page", method = RequestMethod.POST)
    PageResponse<BusinessInventoryDto> businessInventoryPage(@RequestBody @Valid BusinessInventoryPageRequest request);
}
