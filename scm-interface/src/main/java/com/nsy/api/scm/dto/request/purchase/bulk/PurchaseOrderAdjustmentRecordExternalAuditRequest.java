package com.nsy.api.scm.dto.request.purchase.bulk;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(value = "PurchaseOrderAdjustmentRecordExternalAudit", description = "采购申请调整记录表外部调用时的审核参数")
public class PurchaseOrderAdjustmentRecordExternalAuditRequest {

    @ApiModelProperty(value = "采购订单明细Id", name = "orderItemIds")
    @NotEmpty(message = "orderItemIds不能为空")
    private List<Integer> orderItemIds;

    @ApiModelProperty(value = "采购申请记录的调整类型", name = "adjustmentTypes")
    @NotEmpty(message = "adjustmentTypes不能为空")
    private List<Integer> adjustmentTypes;

    @ApiModelProperty(value = "原因", name = "reason")
    private String reason;

    @NotNull(message = "操作类型不能为空")
    @ApiModelProperty(value = "操作类型: PASS-过审，REJECT-驳回，DECREMENT_PASS-减量过审", name = "operationType")
    private String operationType;

    @ApiModelProperty(value = "操作人-真实姓名，前端调用不用传，用于服务间的调用", name = "operatorRealName")
    private String operatorRealName;

    @ApiModelProperty(value = "操作人-用户Id，前端调用不用传，用于服务间的调用", name = "operatorUserId")
    private Integer operatorUserId;

    @ApiModelProperty(value = "Ip地址，前端调用不用传，用于服务间的调用", name = "ipAddress")
    private String ipAddress;

    public List<Integer> getOrderItemIds() {
        return orderItemIds;
    }

    public void setOrderItemIds(List<Integer> orderItemIds) {
        this.orderItemIds = orderItemIds;
    }

    public List<Integer> getAdjustmentTypes() {
        return adjustmentTypes;
    }

    public void setAdjustmentTypes(List<Integer> adjustmentTypes) {
        this.adjustmentTypes = adjustmentTypes;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperatorRealName() {
        return operatorRealName;
    }

    public void setOperatorRealName(String operatorRealName) {
        this.operatorRealName = operatorRealName;
    }

    public Integer getOperatorUserId() {
        return operatorUserId;
    }

    public void setOperatorUserId(Integer operatorUserId) {
        this.operatorUserId = operatorUserId;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
}
