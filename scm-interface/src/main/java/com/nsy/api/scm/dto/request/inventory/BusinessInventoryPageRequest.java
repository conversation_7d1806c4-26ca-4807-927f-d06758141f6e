package com.nsy.api.scm.dto.request.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.scm.dto.constant.StringConstant;
import com.nsy.api.scm.dto.request.common.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-11-25
 */
@ApiModel(value = "BusinessInventoryPageRequest", description = "运营系统 - 业务库存 - 分页查询 - 请求体")
public class BusinessInventoryPageRequest extends PageRequest {

    @ApiModelProperty(value = "规格编码", name = "sku")
    private String sku;

    @ApiModelProperty(value = "店铺Id", name = "storeId")
    private Integer storeId;

    @ApiModelProperty(value = "供应商Id", name = "supplierId")
    private Integer supplierId;

    @ApiModelProperty(value = "预计到货时间-开始", name = "startExpectDeliveryDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startExpectDeliveryDate;

    @ApiModelProperty(value = "预计到货时间-结束", name = "endExpectDeliveryDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endExpectDeliveryDate;

    @ApiModelProperty(value = "选中的规格编码集合", name = "selectedSkuList")
    private List<String> selectedSkuList;

    @ApiModelProperty(value = "有无工厂可发库存:0-否，1-是", name = "hasCanShipQty")
    private Integer hasCanShipQty;

    @ApiModelProperty(value = "采购单号，多个用逗号隔开", name = "orderNos")
    private String orderNos;

    @ApiModelProperty(value = "采购单号集合", name = "orderNoList", hidden = true)
    private List<String> orderNoList;

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public Date getStartExpectDeliveryDate() {
        return startExpectDeliveryDate;
    }

    public void setStartExpectDeliveryDate(Date startExpectDeliveryDate) {
        this.startExpectDeliveryDate = startExpectDeliveryDate;
    }

    public Date getEndExpectDeliveryDate() {
        return endExpectDeliveryDate;
    }

    public void setEndExpectDeliveryDate(Date endExpectDeliveryDate) {
        this.endExpectDeliveryDate = endExpectDeliveryDate;
    }

    public List<String> getSelectedSkuList() {
        return selectedSkuList;
    }

    public void setSelectedSkuList(List<String> selectedSkuList) {
        this.selectedSkuList = selectedSkuList;
    }

    public Integer getHasCanShipQty() {
        return hasCanShipQty;
    }

    public void setHasCanShipQty(Integer hasCanShipQty) {
        this.hasCanShipQty = hasCanShipQty;
    }

    public String getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(String orderNos) {
        this.orderNos = orderNos;
    }

    public void buildOrderNoList() {
        if (StringUtils.isBlank(orderNos)) {
            return;
        }
        setOrderNoList(Arrays.asList(orderNos.split(StringConstant.ENGLISH_COMMA)));
    }

    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public void setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
    }
}
