package com.nsy.api.scm.dto.response.material;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 面料供应商信息项
 *
 * <AUTHOR>
 * @since 2022-04-12 17:49
 */
@ApiModel(value = "MaterialSupplierInfoRes", description = "面料供应商信息项")
public class MaterialSupplierInfoItem {

    @ApiModelProperty(value = "供应商面料id", name = "supplierInfoId")
    private Integer supplierInfoId;

    @ApiModelProperty(value = "物料id", name = "materialId")
    private Integer materialId;

    @ApiModelProperty(value = "物料名称", name = "materialId")
    private String supplierMaterialName;

    @ApiModelProperty(value = "单价", name = "unitPrice")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "面料商自备量", name = "supplierReserve")
    private Integer supplierReserve;

    @ApiModelProperty(value = "公司储备量", name = "reserveForNsy")
    private Integer reserveForNsy;

    @ApiModelProperty(value = "成衣供应商临取量", name = "receivedCount")
    private Integer receivedCount;

    @ApiModelProperty(value = "预测需求量", name = "forecastNeedCount")
    private Integer forecastNeedCount;

    @ApiModelProperty(value = "公司下单量", name = "nsyOrderCount")
    private Integer nsyOrderCount;

    public Integer getSupplierInfoId() {
        return supplierInfoId;
    }

    public void setSupplierInfoId(Integer supplierInfoId) {
        this.supplierInfoId = supplierInfoId;
    }

    public Integer getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Integer materialId) {
        this.materialId = materialId;
    }

    public String getSupplierMaterialName() {
        return supplierMaterialName;
    }

    public void setSupplierMaterialName(String supplierMaterialName) {
        this.supplierMaterialName = supplierMaterialName;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Integer getSupplierReserve() {
        return supplierReserve;
    }

    public void setSupplierReserve(Integer supplierReserve) {
        this.supplierReserve = supplierReserve;
    }

    public Integer getReserveForNsy() {
        return reserveForNsy;
    }

    public void setReserveForNsy(Integer reserveForNsy) {
        this.reserveForNsy = reserveForNsy;
    }

    public Integer getReceivedCount() {
        return receivedCount;
    }

    public void setReceivedCount(Integer receivedCount) {
        this.receivedCount = receivedCount;
    }

    public Integer getForecastNeedCount() {
        return forecastNeedCount;
    }

    public void setForecastNeedCount(Integer forecastNeedCount) {
        this.forecastNeedCount = forecastNeedCount;
    }

    public Integer getNsyOrderCount() {
        return nsyOrderCount;
    }

    public void setNsyOrderCount(Integer nsyOrderCount) {
        this.nsyOrderCount = nsyOrderCount;
    }
}
