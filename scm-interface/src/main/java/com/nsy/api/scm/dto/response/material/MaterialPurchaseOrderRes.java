package com.nsy.api.scm.dto.response.material;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.scm.dto.constant.BigDecimalUtils;
import com.nsy.api.scm.dto.domain.material.PurchasePlanMaterialUsageDTO;
import com.nsy.api.scm.dto.domain.supplier.SupplierSettlementDto;
import com.nsy.api.scm.dto.domain.supplier.SupplierTaxDto;
import com.nsy.api.scm.dto.enumstable.material.purchase.order.MaterialSupplyModeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 面料集采单详情响应类
 *
 * <AUTHOR>
 * @since 2022-05-13 14:54
 */
@ApiModel(value = "MaterialPurchaseOrderRes", description = "面料集采单详情响应类")
public class MaterialPurchaseOrderRes {

    @ApiModelProperty(value = "面料集采单id", name = "materialPurchaseOrderId")
    private Integer purchaseOrderId;

    @ApiModelProperty(value = "面料集采单号", name = "purchaseOrderNo")
    private String purchaseOrderNo;

    @ApiModelProperty(value = "面料集采单类型", name = "orderType")
    private String orderType;

    @ApiModelProperty(value = "面料集采单区域", name = "purchaseOrderLocation")
    private String purchaseOrderLocation;

    @ApiModelProperty(value = "商品编码", name = "productSku")
    private String productSku;

    @ApiModelProperty(value = "状态", name = "status")
    private Integer status;

    @ApiModelProperty(value = "状态名称", name = "statusName")
    private String statusName;

    @ApiModelProperty(value = "创建时间", name = "createDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "面料供应商id", name = "supplierId")
    private Integer supplierId;
    @ApiModelProperty(value = "面料供应商编码", name = "supplierCode")
    private String supplierCode;

    @ApiModelProperty(value = "面料供应商名称", name = "supplierName")
    private String supplierName;
    @ApiModelProperty(value = "面料供应商简称", name = "supplierShortName")
    private String supplierShortName;

    @ApiModelProperty(value = "面料供应商联系人", name = "contactsName")
    private String contactsName;

    @ApiModelProperty(value = "面料供应商联系电话", name = "contactsPhone")
    private String contactsPhone;

    @ApiModelProperty(value = "成衣供应商id", name = "sycSupplierId")
    private Integer sycSupplierId;

    @ApiModelProperty(value = "成衣供应商名称", name = "sycSupplierName")
    private String sycSupplierName;

    @ApiModelProperty(value = "成衣供应商实际名称", name = "sycSupplierRealName")
    private String sycSupplierRealName;

    @ApiModelProperty(value = "成衣供应商编号", name = "sycSupplierNo")
    private String sycSupplierNo;

    @ApiModelProperty(value = "成衣供应商编码", name = "sycSupplierCode")
    private String sycSupplierCode;

    @ApiModelProperty(value = "成衣供应商联系人", name = "sycContactsName")
    private String sycContactsName;

    @ApiModelProperty(value = "成衣供应商联系电话", name = "sycContactsPhone")
    private String sycContactsPhone;

    @ApiModelProperty(value = "供应商简称", name = "sycSupplierShortName")
    private String sycSupplierShortName;

    @ApiModelProperty(value = "是否资金风险：1-是，0-否", name = "isFinancialRisk")
    private Integer isFinancialRisk;

    @ApiModelProperty(value = "面料跟单员id", name = "fabricMerchandiserId")
    private Integer fabricMerchandiserId;

    @ApiModelProperty(value = "面料跟单员名称", name = "fabricMerchandiserName")
    private String fabricMerchandiserName;

    @ApiModelProperty(value = "交货地址", name = "deliveryAddress")
    private String deliveryAddress;

    @ApiModelProperty(value = "供应商 - 省份", name = "province")
    private String province;

    @ApiModelProperty(value = "运输费用", name = "transportationCost")
    private String transportationCost;

    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    @ApiModelProperty("下单方式，1-线上下单，2-线下下单")
    private Integer placeMode;
    @ApiModelProperty("下单方式描述")
    private String placeModeDesc;

    @ApiModelProperty("来源，1-大货订单，2-手动新增领料")
    private Integer source;
    @ApiModelProperty("来源描述")
    private String sourceDesc;
    @ApiModelProperty("花型总数")
    private Long colorCardTotalCount;
    @ApiModelProperty("总领料需求量")
    private BigDecimal totalDemand;

    @ApiModelProperty("紧急程度")
    private String urgency;

    @ApiModelProperty("紧急程度 - 中文")
    private String urgencyDesc;

    @ApiModelProperty(value = "是否包含运费：0-否，1-是", name = "isIncludeCourierFees")
    private Integer isIncludeCourierFees;

    @ApiModelProperty(value = "运费说明", name = "courierFeesExplication")
    private String courierFeesExplication;

    @ApiModelProperty(value = "是否可开发票. 0-不可开，1-可开发票", name = "canInvoice")
    private Integer canInvoice;

    @ApiModelProperty(value = "总费用", name = "allCost")
    private BigDecimal allCost;
    @ApiModelProperty(value = "面料集采单详情列表", name = "materialPurchaseOrderItemList")
    private List<MaterialPurchaseOrderItemRes> materialPurchaseOrderItemList;

    @ApiModelProperty(value = "税务信息", name = "supplierTax")
    private SupplierTaxDto supplierTax;

    public String getUrgency() {
        return urgency;
    }

    public void setUrgency(String urgency) {
        this.urgency = urgency;
    }

    public String getUrgencyDesc() {
        return urgencyDesc;
    }

    public void setUrgencyDesc(String urgencyDesc) {
        this.urgencyDesc = urgencyDesc;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getSupplierShortName() {
        return supplierShortName;
    }

    public void setSupplierShortName(String supplierShortName) {
        this.supplierShortName = supplierShortName;
    }

    public String getContactsName() {
        return contactsName;
    }

    public void setContactsName(String contactsName) {
        this.contactsName = contactsName;
    }

    public String getContactsPhone() {
        return contactsPhone;
    }

    public void setContactsPhone(String contactsPhone) {
        this.contactsPhone = contactsPhone;
    }

    public Long getColorCardTotalCount() {
        return colorCardTotalCount;
    }

    public void setColorCardTotalCount(Long colorCardTotalCount) {
        this.colorCardTotalCount = colorCardTotalCount;
    }

    public BigDecimal getTotalDemand() {
        return totalDemand;
    }

    public void setTotalDemand(BigDecimal totalDemand) {
        this.totalDemand = totalDemand;
    }

    public Integer getPurchaseOrderId() {
        return purchaseOrderId;
    }

    public void setPurchaseOrderId(Integer purchaseOrderId) {
        this.purchaseOrderId = purchaseOrderId;
    }

    public String getPurchaseOrderNo() {
        return purchaseOrderNo;
    }

    public void setPurchaseOrderNo(String purchaseOrderNo) {
        this.purchaseOrderNo = purchaseOrderNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getProductSku() {
        return productSku;
    }

    public void setProductSku(String productSku) {
        this.productSku = productSku;
    }

    public List<MaterialPurchaseOrderItemRes> getMaterialPurchaseOrderItemList() {
        return materialPurchaseOrderItemList;
    }

    public void setMaterialPurchaseOrderItemList(List<MaterialPurchaseOrderItemRes> materialPurchaseOrderItemList) {
        this.materialPurchaseOrderItemList = materialPurchaseOrderItemList;
    }

    public Integer getSycSupplierId() {
        return sycSupplierId;
    }

    public void setSycSupplierId(Integer sycSupplierId) {
        this.sycSupplierId = sycSupplierId;
    }

    public String getSycSupplierShortName() {
        return sycSupplierShortName;
    }

    public void setSycSupplierShortName(String sycSupplierShortName) {
        this.sycSupplierShortName = sycSupplierShortName;
    }

    public Integer getIsFinancialRisk() {
        return isFinancialRisk;
    }

    public void setIsFinancialRisk(Integer isFinancialRisk) {
        this.isFinancialRisk = isFinancialRisk;
    }

    public String getSycSupplierName() {
        return sycSupplierName;
    }

    public void setSycSupplierName(String sycSupplierName) {
        this.sycSupplierName = sycSupplierName;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    public String getTransportationCost() {
        return transportationCost;
    }

    public void setTransportationCost(String transportationCost) {
        this.transportationCost = transportationCost;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSycContactsName() {
        return sycContactsName;
    }

    public void setSycContactsName(String sycContactsName) {
        this.sycContactsName = sycContactsName;
    }

    public String getSycContactsPhone() {
        return sycContactsPhone;
    }

    public void setSycContactsPhone(String sycContactsPhone) {
        this.sycContactsPhone = sycContactsPhone;
    }

    public String getSycSupplierNo() {
        return sycSupplierNo;
    }

    public void setSycSupplierNo(String sycSupplierNo) {
        this.sycSupplierNo = sycSupplierNo;
    }

    public String getSycSupplierCode() {
        return sycSupplierCode;
    }

    public void setSycSupplierCode(String sycSupplierCode) {
        this.sycSupplierCode = sycSupplierCode;
    }

    public String getSycSupplierRealName() {
        return sycSupplierRealName;
    }

    public void setSycSupplierRealName(String sycSupplierRealName) {
        this.sycSupplierRealName = sycSupplierRealName;
    }

    public String getFabricMerchandiserId() {
        if (fabricMerchandiserId == null || fabricMerchandiserId <= 0) {
            return StringUtils.EMPTY;
        }
        return fabricMerchandiserId.toString();
    }

    public void setFabricMerchandiserId(Integer fabricMerchandiserId) {
        this.fabricMerchandiserId = fabricMerchandiserId;
    }

    public String getFabricMerchandiserName() {
        return fabricMerchandiserName;
    }

    public void setFabricMerchandiserName(String fabricMerchandiserName) {
        this.fabricMerchandiserName = fabricMerchandiserName;
    }

    public String getPlaceMode() {
        return Optional.ofNullable(placeMode).map(Object::toString).orElse(StringUtils.EMPTY);
    }

    public void setPlaceMode(Integer placeMode) {
        this.placeMode = placeMode;
    }

    public String getPlaceModeDesc() {
        return placeModeDesc;
    }

    public void setPlaceModeDesc(String placeModeDesc) {
        this.placeModeDesc = placeModeDesc;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getSourceDesc() {
        return sourceDesc;
    }

    public void setSourceDesc(String sourceDesc) {
        this.sourceDesc = sourceDesc;
    }

    public String getPurchaseOrderLocation() {
        return purchaseOrderLocation;
    }

    public void setPurchaseOrderLocation(String purchaseOrderLocation) {
        this.purchaseOrderLocation = purchaseOrderLocation;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public Integer getIsIncludeCourierFees() {
        return isIncludeCourierFees;
    }

    public void setIsIncludeCourierFees(Integer isIncludeCourierFees) {
        this.isIncludeCourierFees = isIncludeCourierFees;
    }

    public Integer getCanInvoice() {
        return canInvoice;
    }

    public void setCanInvoice(Integer canInvoice) {
        this.canInvoice = canInvoice;
    }

    public BigDecimal getAllCost() {
        return allCost;
    }

    public void setAllCost(BigDecimal allCost) {
        this.allCost = allCost;
    }

    public String getCourierFeesExplication() {
        return courierFeesExplication;
    }

    public void setCourierFeesExplication(String courierFeesExplication) {
        this.courierFeesExplication = courierFeesExplication;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public SupplierTaxDto getSupplierTax() {
        return supplierTax;
    }

    public void setSupplierTax(SupplierTaxDto supplierTax) {
        this.supplierTax = supplierTax;
    }

    public static MaterialPurchaseOrderRes buildByMaterialPurchaseOrderInfos(List<MaterialPurchaseOrderInfoVO> vos, Map<Integer, Integer> acceptanceCountMap, Map<Integer, PurchasePlanMaterialUsageDTO> usageMap) {
        if (CollectionUtils.isEmpty(vos)) {
            return null;
        }
        MaterialPurchaseOrderInfoVO firstVo = vos.get(0);
        MaterialPurchaseOrderRes res = newMaterialPurchaseOrder(firstVo);
        Map<Integer, List<MaterialPurchaseOrderInfoVO>> purchaseOrderInfoMap = vos.stream().collect(Collectors.groupingBy(MaterialPurchaseOrderInfoVO::getPurchaseOrderItemId));
        List<MaterialPurchaseOrderItemRes> materialPurchaseOrderItemList = Lists.newArrayListWithExpectedSize(purchaseOrderInfoMap.size());
        for (Map.Entry<Integer, List<MaterialPurchaseOrderInfoVO>> entry : purchaseOrderInfoMap.entrySet()) {
            List<MaterialPurchaseOrderInfoVO> voList = entry.getValue();
            MaterialPurchaseOrderItemRes purchaseOrderItem = newMaterialPurchaseOrderItem(voList.get(0));
            purchaseOrderItem.setAcceptedQuantity(acceptanceCountMap.getOrDefault(entry.getKey(), 0));
            purchaseOrderItem.setPurchaseRemark(usageMap.getOrDefault(purchaseOrderItem.getPurchaseOrderItemId(), new PurchasePlanMaterialUsageDTO()).getRemark());
            materialPurchaseOrderItemList.add(purchaseOrderItem);
        }
        res.setMaterialPurchaseOrderItemList(materialPurchaseOrderItemList);
        long totalColorCard = materialPurchaseOrderItemList.stream().map(MaterialPurchaseOrderItemRes::getColorCardId)
                .distinct().count();
        BigDecimal totalDemand = materialPurchaseOrderItemList.stream().map(MaterialPurchaseOrderItemRes::getDemand).reduce(BigDecimal.ZERO, BigDecimal::add);
        res.setTotalDemand(totalDemand);
        res.setColorCardTotalCount(totalColorCard);
        return res;
    }

    private static MaterialPurchaseOrderRes newMaterialPurchaseOrder(MaterialPurchaseOrderInfoVO vo) {
        MaterialPurchaseOrderRes res = new MaterialPurchaseOrderRes();
        BeanUtilsEx.copyProperties(vo, res);
        return res;
    }

    private static MaterialPurchaseOrderItemRes newMaterialPurchaseOrderItem(MaterialPurchaseOrderInfoVO vo) {
        MaterialPurchaseOrderItemRes res = new MaterialPurchaseOrderItemRes();
        BeanUtilsEx.copyProperties(vo, res);
        res.setRemark(vo.getItemRemark());
        res.setCreateDate(vo.getOrderItemCreateDate());
        return res;
    }

    public void filterSupplyModeWithoutSupplier() {
        List<MaterialPurchaseOrderItemRes> purchaseOrderItemRes = this.getMaterialPurchaseOrderItemList().stream()
                .filter(orderItemRes -> { // 过滤非工厂自备的
                    return !Objects.equals(MaterialSupplyModeEnum.SUPPLIER.getCode(), orderItemRes.getSupplyModeInt());
                }).collect(Collectors.toList());
        this.setMaterialPurchaseOrderItemList(purchaseOrderItemRes);
    }

    public void buildAllCost(SupplierSettlementDto supplierSettlementDto, SupplierTaxDto supplierTaxDto) {
        setCanInvoice(supplierSettlementDto.getCanInvoice());
        setIsIncludeCourierFees(supplierTaxDto.getIsIncludeCourierFees());
        setCourierFeesExplication(supplierTaxDto.getCourierFeesExplication());
        if (CollectionUtil.isEmpty(getMaterialPurchaseOrderItemList())) {
            return;
        }
        BigDecimal allCost = getMaterialPurchaseOrderItemList().stream().map(MaterialPurchaseOrderItemRes::getSubtotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (allCost.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        BigDecimal ticketPercent;
        if (ObjectUtil.isNull(supplierSettlementDto)
                || ObjectUtil.isNull(supplierSettlementDto.getTicketPercent())) {
            ticketPercent = BigDecimal.ZERO;
        } else {
            ticketPercent = supplierSettlementDto.getTicketPercent().divide(BigDecimalUtils.HUNDRED);
        }
        // 计算总费用,总费用：=总价格*（1+票点）
        setAllCost(allCost.multiply(ticketPercent.add(BigDecimal.ONE)).setScale(2, BigDecimal.ROUND_HALF_UP));
    }
}
