package com.nsy.api.scm.dto.response.product;

import com.nsy.api.scm.dto.domain.bd.BdMainMarkDto;
import com.nsy.api.scm.dto.domain.bd.BdTagDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-03-14 16:36
 */
@ApiModel(value = "商品系统 - 商品对应的主唛吊牌信息", description = "商品系统 - 商品对应的主唛吊牌信息")
public class ProductMainMarkTagResponse {

    @ApiModelProperty(value = "商品id", name = "productId")
    private Integer productId;

    @ApiModelProperty(value = "主唛信息", name = "bdMainMarkList")
    private List<BdMainMarkDto> bdMainMarkList;

    @ApiModelProperty(value = "吊牌信息", name = "tag")
    private BdTagDto bdTagDto;

    @ApiModelProperty(value = "品牌id", name = "brandId")
    private Integer brandId;

    @ApiModelProperty(value = "品牌名称", name = "brandName")
    private String brandName;

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public List<BdMainMarkDto> getBdMainMarkList() {
        return bdMainMarkList;
    }

    public void setBdMainMarkList(List<BdMainMarkDto> bdMainMarkList) {
        this.bdMainMarkList = bdMainMarkList;
    }

    public BdTagDto getBdTagDto() {
        return bdTagDto;
    }

    public void setBdTagDto(BdTagDto bdTagDto) {
        this.bdTagDto = bdTagDto;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }
}
