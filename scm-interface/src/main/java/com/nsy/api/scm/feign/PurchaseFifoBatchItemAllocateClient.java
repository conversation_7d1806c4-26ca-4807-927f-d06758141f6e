package com.nsy.api.scm.feign;

import com.nsy.api.scm.dto.domain.SelectModel;
import com.nsy.api.scm.dto.domain.purchase.PurchaseFifoBatchItemAllocateCanceledDto;
import com.nsy.api.scm.dto.domain.purchase.PurchaseFifoBatchItemAllocateCompletedDto;
import com.nsy.api.scm.dto.domain.purchase.PurchaseFifoBatchItemAllocateQueryForCompleteBatchDto;
import com.nsy.api.scm.dto.domain.purchase.PurchaseFifoBatchItemAllocateQueryForDeliveredInPast7DaysDto;
import com.nsy.api.scm.dto.domain.purchase.PurchaseFifoBatchItemAllocateUpdatePageDto;
import com.nsy.api.scm.dto.domain.purchase.PurchaseFifoBatchItemAllocateWaitConfirmDto;
import com.nsy.api.scm.dto.request.purchase.PurchaseFifoBatchItemAllocateCanceledPageRequest;
import com.nsy.api.scm.dto.request.purchase.PurchaseFifoBatchItemAllocatePageCompletedRequest;
import com.nsy.api.scm.dto.request.purchase.PurchaseFifoBatchItemAllocateQueryForCompleteBatchRequest;
import com.nsy.api.scm.dto.request.purchase.PurchaseFifoBatchItemAllocateQueryForUpdatePageRequest;
import com.nsy.api.scm.dto.request.purchase.PurchaseFifoBatchItemAllocateQueryUnAllocateSkcRequest;
import com.nsy.api.scm.dto.request.purchase.PurchaseFifoBatchItemAllocateUpdateRequest;
import com.nsy.api.scm.dto.request.purchase.PurchaseFifoBatchItemAllocateWaitConfirmPageRequest;
import com.nsy.api.scm.dto.request.purchase.PurchaseFifoBatchOperationRequest;
import com.nsy.api.scm.dto.request.purchase.PurchaseFifoCompleteBatchSecondConfirmRequest;
import com.nsy.api.scm.dto.request.purchase.fifo.VmsDeleteUpdateFifoBatchItemAllocateQuantitiesRequest;
import com.nsy.api.scm.dto.request.purchase.fifo.VmsDeliveryUpdateFifoBatchItemAllocateQuantitiesRequest;
import com.nsy.api.scm.dto.request.purchase.fifo.VmsImportDeliveryUpdateFifoBatchItemAllocateQuantitiesRequest;
import com.nsy.api.scm.dto.request.purchase.fifo.VmsProcessCompletedFifoRecordsRequest;
import com.nsy.api.scm.dto.request.purchase.fifo.VmsReceiveCheckUpdateFifoBatchItemAllocateQuantitiesRequest;
import com.nsy.api.scm.dto.response.common.PageResponse;
import com.nsy.api.scm.dto.response.purchase.fifo.BatchConfirmCompletionResponse;
import com.nsy.api.scm.dto.response.purchase.fifo.BatchSyncStoreSkuInfoResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "api-scm", contextId = "scm-13")
@RequestMapping("/purchase-fifo-batch-item-allocate")
@Api(tags = "快进快出批次明细分配表")
public interface PurchaseFifoBatchItemAllocateClient {
    @PostMapping("/page-canceled")
    @ApiOperation(value = "已取消列表", notes = "已取消列表", produces = "application/json")
    PageResponse<PurchaseFifoBatchItemAllocateCanceledDto> pageCanceled(@RequestBody @Valid PurchaseFifoBatchItemAllocateCanceledPageRequest request);

    @PostMapping("/page-wait-confirm")
    @ApiOperation(value = "待确认列表", notes = "待确认列表", produces = "application/json")
    PageResponse<PurchaseFifoBatchItemAllocateWaitConfirmDto> pageWaitConfirm(@RequestBody @Valid PurchaseFifoBatchItemAllocateWaitConfirmPageRequest request);

    @PostMapping("/page-completed")
    @ApiOperation(value = "已完成列表", notes = "已完成列表", produces = "application/json")
    PageResponse<PurchaseFifoBatchItemAllocateCompletedDto> pageCompleted(@RequestBody @Valid PurchaseFifoBatchItemAllocatePageCompletedRequest request);

    @GetMapping({"/end-batch/{batchNo}/{operatorName}", "/end-batch/{batchNo}"})
    @ApiOperation(value = "取消批次", notes = "取消批次")
    void endBatch(@PathVariable String batchNo, @PathVariable(required = false) String operatorName);

    @PostMapping("/batch-complete-batch")
    @ApiOperation(value = "批量完成批次", notes = "批量完成批次")
    BatchConfirmCompletionResponse batchConfirmCompletion(@RequestBody @Valid PurchaseFifoBatchOperationRequest request);

    @PostMapping("/batch-sync-store-sku-info")
    @ApiOperation(value = "批量同步店铺条形码信息", notes = "批量同步店铺条形码信息")
    BatchSyncStoreSkuInfoResponse batchSyncStoreSkuInfo(@RequestBody @Valid PurchaseFifoBatchOperationRequest request);

    @PostMapping("/query-for-update-page")
    @ApiOperation(value = "快进快出调整页面-查询", notes = "快进快出调整页面-查询", produces = "application/json")
    List<PurchaseFifoBatchItemAllocateUpdatePageDto> queryForUpdatePage(@RequestBody @Valid PurchaseFifoBatchItemAllocateQueryForUpdatePageRequest request);

    @PostMapping("/query-for-un-allocate-skc-select")
    @ApiOperation(value = "快进快出调整页面-查询批次未分配的SKC下拉数据", notes = "快进快出调整页面-查询批次未分配的SKC下拉数据", produces = "application/json")
    PageResponse<SelectModel> queryForUnAllocateSkcSelect(@RequestBody @Valid PurchaseFifoBatchItemAllocateQueryUnAllocateSkcRequest request);

    @PutMapping("/judge-batch-allocate-completed/{batchNo}")
    @ApiOperation(value = "快进快出调整页面-校验批次是否分配完成", notes = "快进快出调整页面-校验批次是否分配完成", produces = "application/json")
    boolean judgeBatchAllocateCompleted(@PathVariable String batchNo);

    @PutMapping("/save-allocate-info")
    @ApiOperation(value = "快进快出调整页面-分配其他颜色功能保存功能和确认功能", notes = "快进快出调整页面-分配其他颜色功能保存功能和确认功能", produces = "application/json")
    void saveAllocateInfo(@RequestBody @Valid PurchaseFifoBatchItemAllocateUpdateRequest request);

    @PostMapping("/get-store-select")
    @ApiOperation(value = "快进快出调整页面-获取店铺下拉数据", notes = "快进快出调整页面-获取店铺下拉数据", produces = "application/json")
    PageResponse<SelectModel> getStoreSelect();

    @PostMapping("/query-for-complete-batch")
    @ApiOperation(value = "快进快出页面-待确认-确认完成-查询", notes = "快进快出页面-待确认-确认完成-查询", produces = "application/json")
    List<PurchaseFifoBatchItemAllocateQueryForCompleteBatchDto> queryForCompleteBatch(@RequestBody @Valid PurchaseFifoBatchItemAllocateQueryForCompleteBatchRequest request);

    @PostMapping("/query-for-delivered-in-past-7days")
    @ApiOperation(value = "快进快出页面-待确认-二次确认(上次发货是否在7天内)-查询", notes = "快进快出页面-待确认-二次确认(上次发货是否在7天内)-查询", produces = "application/json")
    List<PurchaseFifoBatchItemAllocateQueryForDeliveredInPast7DaysDto> queryForDeliveredInPast7Days(@RequestBody @Valid PurchaseFifoCompleteBatchSecondConfirmRequest request);

    @PostMapping("/vms/delivery-update-fifo-batch-item-allocate-quantities")
    @ApiOperation(value = "VMS-工厂发货，scm更新批次明细分配表的相关数量", notes = "VMS-工厂发货，scm更新批次明细分配表的相关数量", produces = "application/json")
    Boolean vmsDeliveryUpdateFifoBatchItemAllocateQuantities(@RequestBody @Valid VmsDeliveryUpdateFifoBatchItemAllocateQuantitiesRequest request);

    @PostMapping("/vms/delete-update-fifo-batch-item-allocate-quantities")
    @ApiOperation(value = "VMS-工厂删除发货单，scm更新批次明细分配表的相关数量", notes = "VMS-工厂删除发货单，scm更新批次明细分配表的相关数量", produces = "application/json")
    Boolean vmsDeleteUpdateFifoBatchItemAllocateQuantities(@RequestBody @Valid VmsDeleteUpdateFifoBatchItemAllocateQuantitiesRequest request);

    @PostMapping("/vms/import-delivery-update-fifo-batch-item-allocate-quantities")
    @ApiOperation(value = "VMS-工厂导发货单，scm更新批次明细分配表的相关数量", notes = "VMS-工厂导发货单，scm更新批次明细分配表的相关数量", produces = "application/json")
    Boolean vmsImportDeliveryUpdateFifoBatchItemAllocateQuantities(@RequestBody @Valid VmsImportDeliveryUpdateFifoBatchItemAllocateQuantitiesRequest request);

    @ApiOperation(value = "VMS-更新快进快出已完成批次的发货数", produces = "application/json")
    @RequestMapping(value = "/vms/update-completed-fifo-shipped-qty", method = RequestMethod.POST)
    void vmsUpdateCompletedFifoShippedQty(@RequestBody @Valid VmsProcessCompletedFifoRecordsRequest request);

    @ApiOperation(value = "VMS-工厂差异确认-追加生产，scm更新批次明细分配表的相关数量", produces = "application/json")
    @RequestMapping(value = "/vms/receive-check/update-fifo-allocated-qty", method = RequestMethod.POST)
    void vmsReceiveCheckUpdateFifoBatchItemAllocateQuantities(@RequestBody @Valid VmsReceiveCheckUpdateFifoBatchItemAllocateQuantitiesRequest request);
}
