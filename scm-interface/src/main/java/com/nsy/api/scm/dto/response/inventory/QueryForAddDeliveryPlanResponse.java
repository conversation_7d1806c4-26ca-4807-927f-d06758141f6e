package com.nsy.api.scm.dto.response.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 2024-11-25
 */
@ApiModel(value = "QueryForAddDeliveryPlanResponse", description = "运营系统 - 业务库存 - 根据采购单+SKU批量查询可发库存、条码模板信息 - 响应体")
public class QueryForAddDeliveryPlanResponse {
    @ApiModelProperty(value = "采购单号", name = "purchaseOrderNo")
    private String purchaseOrderNo;

    @ApiModelProperty(value = "规格编码", name = "sku")
    private String sku;

    @ApiModelProperty(value = "可发库存", name = "canShipInventoryQty")
    private Integer canShipInventoryQty;

    @ApiModelProperty(value = "条码模板", name = "barcodeTemplate")
    private String barcodeTemplate;

    @ApiModelProperty(value = "品牌Id", name = "brandId")
    private Integer brandId;

    @ApiModelProperty(value = "品牌名称", name = "brandName")
    private String brandName;

    public String getPurchaseOrderNo() {
        return purchaseOrderNo;
    }

    public void setPurchaseOrderNo(String purchaseOrderNo) {
        this.purchaseOrderNo = purchaseOrderNo;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getCanShipInventoryQty() {
        return canShipInventoryQty;
    }

    public void setCanShipInventoryQty(Integer canShipInventoryQty) {
        this.canShipInventoryQty = canShipInventoryQty;
    }

    public String getBarcodeTemplate() {
        return barcodeTemplate;
    }

    public void setBarcodeTemplate(String barcodeTemplate) {
        this.barcodeTemplate = barcodeTemplate;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }
}
