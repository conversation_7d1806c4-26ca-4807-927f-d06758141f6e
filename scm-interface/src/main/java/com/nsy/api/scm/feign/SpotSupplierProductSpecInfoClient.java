package com.nsy.api.scm.feign;

import com.nsy.api.scm.dto.domain.supplier.SpotSupplierSpecInfoDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@FeignClient(value = "api-scm", contextId = "spot-supplier-product-spec-info")
@Api(tags = "供应商商品现货链接")
public interface SpotSupplierProductSpecInfoClient {

    @ApiOperation(value = "供应商商品详情", notes = "供应商商品详情")
    @RequestMapping(value = "/supplier-product/spot-supplier-product-spec-info/list", method = RequestMethod.POST)
    List<SpotSupplierSpecInfoDto> listBySpecIds(@RequestBody List<Integer> specIds);

}
