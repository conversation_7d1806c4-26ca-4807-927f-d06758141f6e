package com.nsy.api.scm.dto.request.purchase;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR> csw
 * @description :
 * @since : 2024/11/27 下午3:19
 */
@ApiModel(value = "PromoteProcurementRequest", description = "采购推单请求体")
public class PromoteProcurementRequest {
    @ApiModelProperty(value = "商品Id集合", name = "productIdList")
    @NotEmpty(message = "请至少选择一个")
    private List<Integer> productIdList;

    @ApiModelProperty(value = "颜色编码集合", name = "skcList")
    @NotEmpty(message = "颜色编码不得为空")
    private List<String> skcList;

    public List<Integer> getProductIdList() {
        return productIdList;
    }

    public void setProductIdList(List<Integer> productIdList) {
        this.productIdList = productIdList;
    }

    public List<String> getSkcList() {
        return skcList;
    }

    public void setSkcList(List<String> skcList) {
        this.skcList = skcList;
    }
}
