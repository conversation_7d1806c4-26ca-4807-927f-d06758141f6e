package com.nsy.api.scm.dto.request.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-11-25
 */
@ApiModel(value = "QueryForAddDeliveryPlanRequest", description = "运营系统 - 业务库存 - 根据采购单+SKU批量查询可发库存、条码模板信息 - 请求体")
public class QueryForAddDeliveryPlanRequest {
    @ApiModelProperty(value = "请求参数不能为空", name = "purchaseOrderAndSkuList")
    @NotEmpty(message = "purchaseOrderAndSkuList不能为空")
    @Valid
    private List<PurchaseOrderAndSku> purchaseOrderAndSkuList;

    public List<PurchaseOrderAndSku> getPurchaseOrderAndSkuList() {
        return purchaseOrderAndSkuList;
    }

    public void setPurchaseOrderAndSkuList(List<PurchaseOrderAndSku> purchaseOrderAndSkuList) {
        this.purchaseOrderAndSkuList = purchaseOrderAndSkuList;
    }

    public static class PurchaseOrderAndSku {
        @ApiModelProperty(value = "采购单号", name = "purchaseOrderNo")
        @NotBlank(message = "采购单号不能为空")
        private String purchaseOrderNo;

        @ApiModelProperty(value = "规格编码", name = "skuList")
        @NotEmpty(message = "规格编码不能为空")
        private List<String> skuList;

        public String getPurchaseOrderNo() {
            return purchaseOrderNo;
        }

        public void setPurchaseOrderNo(String purchaseOrderNo) {
            this.purchaseOrderNo = purchaseOrderNo;
        }

        public List<String> getSkuList() {
            return skuList;
        }

        public void setSkuList(List<String> skuList) {
            this.skuList = skuList;
        }
    }
}
