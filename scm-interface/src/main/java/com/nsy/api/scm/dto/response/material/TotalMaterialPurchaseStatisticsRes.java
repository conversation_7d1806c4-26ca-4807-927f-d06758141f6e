package com.nsy.api.scm.dto.response.material;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 所有面料集采统计信息
 *
 * <AUTHOR>
 * @since 2022-04-06 9:59
 */
@ApiModel(value = "TotalMaterialPurchaseStatisticsRes", description = "所有面料集采统计信息")
public class TotalMaterialPurchaseStatisticsRes {

    @ApiModelProperty(value = "总面料数", name = "totalMaterialCount")
    private Integer totalMaterialCount;

    @ApiModelProperty(value = "总供应商自备量", name = "totalSupplierReserveCount")
    private Integer totalSupplierReserveCount;

    @ApiModelProperty(value = "总公司储备量", name = "totalReserveForNsy")
    private Integer totalReserveForNsy;

    @ApiModelProperty(value = "近三个月消耗量", name = "nearlyThreeMonthsConsumptionCount")
    private Double nearlyThreeMonthsConsumptionCount;

    @ApiModelProperty(value = "近半年消耗量", name = "nearlySixMonthsConsumptionCount")
    private Double nearlySixMonthsConsumptionCount;

    @ApiModelProperty(value = "面料id（当查询单个面料统计信息时有值）", name = "materialId")
    private Integer materialId;

    @ApiModelProperty(value = "面料名称（当查询单个面料统计信息时有值）", name = "materialName")
    private String materialName;

    @ApiModelProperty(value = "供应商面料名称（当查询单个面料统计信息时有值）", name = "supplierMaterialName")
    private String supplierMaterialName;

    public Integer getTotalMaterialCount() {
        return totalMaterialCount;
    }

    public void setTotalMaterialCount(Integer totalMaterialCount) {
        this.totalMaterialCount = totalMaterialCount;
    }

    public Integer getTotalSupplierReserveCount() {
        return totalSupplierReserveCount;
    }

    public void setTotalSupplierReserveCount(Integer totalSupplierReserveCount) {
        this.totalSupplierReserveCount = totalSupplierReserveCount;
    }

    public Integer getTotalReserveForNsy() {
        return totalReserveForNsy;
    }

    public void setTotalReserveForNsy(Integer totalReserveForNsy) {
        this.totalReserveForNsy = totalReserveForNsy;
    }

    public Double getNearlyThreeMonthsConsumptionCount() {
        return nearlyThreeMonthsConsumptionCount;
    }

    public void setNearlyThreeMonthsConsumptionCount(Double nearlyThreeMonthsConsumptionCount) {
        this.nearlyThreeMonthsConsumptionCount = nearlyThreeMonthsConsumptionCount;
    }

    public Double getNearlySixMonthsConsumptionCount() {
        return nearlySixMonthsConsumptionCount;
    }

    public void setNearlySixMonthsConsumptionCount(Double nearlySixMonthsConsumptionCount) {
        this.nearlySixMonthsConsumptionCount = nearlySixMonthsConsumptionCount;
    }

    public Integer getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Integer materialId) {
        this.materialId = materialId;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getSupplierMaterialName() {
        return supplierMaterialName;
    }

    public void setSupplierMaterialName(String supplierMaterialName) {
        this.supplierMaterialName = supplierMaterialName;
    }
}
