package com.nsy.api.scm.dto.response.material;

import com.nsy.api.scm.dto.domain.material.MaterialSupplierColorCardDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 花型色卡停用替换列表响应
 *
 * <AUTHOR>
 * @date 2023/10/18 11:42
 */
@ApiModel(value = "MaterialSupplierColorCardDisableReplaceRes", description = "花型色卡停用替换列表响应")
public class MaterialSupplierColorCardDisableReplaceRes implements Serializable {

    private static final long serialVersionUID = 3228655796616011457L;

    @ApiModelProperty(value = "停用色卡Id", name = "disableColorCardId")
    private Integer disableColorCardId;

    @ApiModelProperty(value = "停用色卡", name = "disableColorCard")
    private MaterialSupplierColorCardDto disableColorCard;

    @ApiModelProperty(value = "更换色卡列表", name = "replaceColorCardList")
    private List<MaterialSupplierColorCardDto> replaceColorCardList;

    public MaterialSupplierColorCardDisableReplaceRes() {
    }

    public MaterialSupplierColorCardDisableReplaceRes(Integer disableColorCardId) {
        this.disableColorCardId = disableColorCardId;
        this.replaceColorCardList = new ArrayList<>();
    }

    public Integer getDisableColorCardId() {
        return disableColorCardId;
    }

    public void setDisableColorCardId(Integer disableColorCardId) {
        this.disableColorCardId = disableColorCardId;
    }

    public MaterialSupplierColorCardDto getDisableColorCard() {
        return disableColorCard;
    }

    public void setDisableColorCard(MaterialSupplierColorCardDto disableColorCard) {
        this.disableColorCard = disableColorCard;
    }

    public List<MaterialSupplierColorCardDto> getReplaceColorCardList() {
        return replaceColorCardList;
    }

    public void setReplaceColorCardList(List<MaterialSupplierColorCardDto> replaceColorCardList) {
        this.replaceColorCardList = replaceColorCardList;
    }

    /**
     * 构建花型色卡停用更换响应<br>
     * 根据花型色卡的ID赋值给停用色卡或更换色卡列表
     *
     * @param materialSupplierColorCardDto 花型色卡信息
     */
    public void build(MaterialSupplierColorCardDto materialSupplierColorCardDto) {
        if (Objects.equals(materialSupplierColorCardDto.getColorCardId(), this.disableColorCardId)) {
            this.disableColorCard = materialSupplierColorCardDto;
            return;
        }
        replaceColorCardList.add(materialSupplierColorCardDto);
    }
}
