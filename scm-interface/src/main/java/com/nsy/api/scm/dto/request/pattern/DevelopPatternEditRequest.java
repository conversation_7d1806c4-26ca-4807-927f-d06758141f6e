package com.nsy.api.scm.dto.request.pattern;

import com.nsy.api.scm.dto.domain.pattern.DevelopPatternAttachmentDTO;
import com.nsy.api.scm.dto.domain.pattern.DevelopPatternClothesPartMappingDTO;
import com.nsy.api.scm.dto.domain.pattern.DevelopPatternDTO;
import com.nsy.api.scm.dto.domain.pattern.DevelopPatternSizeItemDTO;
import com.nsy.api.scm.dto.domain.pattern.DevelopPatternSupplierMaterialInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 版型库新增编辑请求
 *
 * <AUTHOR>
 */
@ApiModel(value = "DevelopPatternEditRequest", description = "版型库新增编辑请求")
public class DevelopPatternEditRequest {

    @ApiModelProperty(value = "版型", name = "developPatternDTO")
    @NotNull(message = "版型不能为空")
    private DevelopPatternDTO developPatternDTO;

    @ApiModelProperty(value = "面料信息", name = "developPatternSupplierFabricInfoDTOList", required = true)
    private List<DevelopPatternSupplierMaterialInfoDTO> developPatternSupplierFabricInfoDTOList;

    @ApiModelProperty(value = "辅料信息", name = "developPatternSupplierAccessoriesInfoDTOList", required = true)
    private List<DevelopPatternSupplierMaterialInfoDTO> developPatternSupplierAccessoriesInfoDTOList;

    @ApiModelProperty(value = "版型图片信息", name = "developPatternAttachmentDTOList")
    private List<DevelopPatternAttachmentDTO> developPatternAttachmentDTOList;

    @ApiModelProperty(value = "纸样图", name = "patternDrawingAttachmentDTOList")
    private List<DevelopPatternAttachmentDTO> patternDrawingAttachmentDTOList;

    @ApiModelProperty(value = "工艺单", name = "processSheetAttachmentDTOList")
    private List<DevelopPatternAttachmentDTO> processSheetAttachmentDTOList;

    @ApiModelProperty(value = "尺寸表", name = "dimensionTableAttachmentDTOList")
    private List<DevelopPatternAttachmentDTO> dimensionTableAttachmentDTOList;

    @ApiModelProperty(value = "部门量法尺寸信息", name = "developPatternClothesPartMappingDTOList")
    private List<DevelopPatternClothesPartMappingDTO> developPatternClothesPartMappingDTOList;

    /**
     * 尺码信息
     */
    @ApiModelProperty(value = "尺码信息", name = "developPatternSizeItemDTO")
    private List<DevelopPatternSizeItemDTO> developPatternSizeItemDTO;

    public DevelopPatternDTO getDevelopPatternDTO() {
        return developPatternDTO;
    }

    public void setDevelopPatternDTO(DevelopPatternDTO developPatternDTO) {
        this.developPatternDTO = developPatternDTO;
    }

    public List<DevelopPatternSupplierMaterialInfoDTO> getDevelopPatternSupplierFabricInfoDTOList() {
        return developPatternSupplierFabricInfoDTOList;
    }

    public void setDevelopPatternSupplierFabricInfoDTOList(List<DevelopPatternSupplierMaterialInfoDTO> developPatternSupplierFabricInfoDTOList) {
        this.developPatternSupplierFabricInfoDTOList = developPatternSupplierFabricInfoDTOList;
    }

    public List<DevelopPatternSupplierMaterialInfoDTO> getDevelopPatternSupplierAccessoriesInfoDTOList() {
        return developPatternSupplierAccessoriesInfoDTOList;
    }

    public void setDevelopPatternSupplierAccessoriesInfoDTOList(List<DevelopPatternSupplierMaterialInfoDTO> developPatternSupplierAccessoriesInfoDTOList) {
        this.developPatternSupplierAccessoriesInfoDTOList = developPatternSupplierAccessoriesInfoDTOList;
    }
    public List<DevelopPatternAttachmentDTO> getDevelopPatternAttachmentDTOList() {
        return developPatternAttachmentDTOList;
    }

    public void setDevelopPatternAttachmentDTOList(List<DevelopPatternAttachmentDTO> developPatternAttachmentDTOList) {
        this.developPatternAttachmentDTOList = developPatternAttachmentDTOList;
    }

    public List<DevelopPatternAttachmentDTO> getPatternDrawingAttachmentDTOList() {
        return patternDrawingAttachmentDTOList;
    }

    public void setPatternDrawingAttachmentDTOList(List<DevelopPatternAttachmentDTO> patternDrawingAttachmentDTOList) {
        this.patternDrawingAttachmentDTOList = patternDrawingAttachmentDTOList;
    }

    public List<DevelopPatternAttachmentDTO> getProcessSheetAttachmentDTOList() {
        return processSheetAttachmentDTOList;
    }

    public void setProcessSheetAttachmentDTOList(List<DevelopPatternAttachmentDTO> processSheetAttachmentDTOList) {
        this.processSheetAttachmentDTOList = processSheetAttachmentDTOList;
    }

    public List<DevelopPatternAttachmentDTO> getDimensionTableAttachmentDTOList() {
        return dimensionTableAttachmentDTOList;
    }

    public void setDimensionTableAttachmentDTOList(List<DevelopPatternAttachmentDTO> dimensionTableAttachmentDTOList) {
        this.dimensionTableAttachmentDTOList = dimensionTableAttachmentDTOList;
    }

    public List<DevelopPatternClothesPartMappingDTO> getDevelopPatternClothesPartMappingDTOList() {
        return developPatternClothesPartMappingDTOList;
    }

    public void setDevelopPatternClothesPartMappingDTOList(List<DevelopPatternClothesPartMappingDTO> developPatternClothesPartMappingDTOList) {
        this.developPatternClothesPartMappingDTOList = developPatternClothesPartMappingDTOList;
    }

    /**
     * 尺码信息
     */
    public List<DevelopPatternSizeItemDTO> getDevelopPatternSizeItemDTO() {
        return this.developPatternSizeItemDTO;
    }

    /**
     * 尺码信息
     */
    public void setDevelopPatternSizeItemDTO(List<DevelopPatternSizeItemDTO> developPatternSizeItemDTO) {
        this.developPatternSizeItemDTO = developPatternSizeItemDTO;
    }
}
