package com.nsy.api.scm.feign;

import com.nsy.api.scm.dto.domain.config.PurchaseContractConfigDto;
import com.nsy.api.scm.dto.domain.config.PurchaseContractConfigGroupListDto;
import com.nsy.api.scm.dto.request.config.PurchaseContractConfigPageRequest;
import com.nsy.api.scm.dto.request.config.PurchaseContractConfigSaveRequest;
import com.nsy.api.scm.dto.response.common.PageResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.Valid;

/**
 * <p>
 * 采购合同配置 控制类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@FeignClient(value = "api-scm", contextId = "scm-purchaseContractConfig")
@RequestMapping("/purchase-contract-config")
@Api(tags = "采购合同配置")
public interface PurchaseContractConfigFeignClient {

    @PostMapping("/page")
    @ApiOperation(value = "分页", notes = "分页")
    PageResponse<PurchaseContractConfigDto> page(@RequestBody @Valid PurchaseContractConfigPageRequest request);

    @ApiOperation(value = "保存", notes = "保存")
    @PostMapping("/save")
    void save(@RequestBody @Valid PurchaseContractConfigSaveRequest request);

    @GetMapping("/{contractConfigId}")
    @ApiOperation(value = "详情", notes = "详情")
    PurchaseContractConfigDto edit(@PathVariable("contractConfigId") Integer contractConfigId);

    @GetMapping("/group-list")
    @ApiOperation(value = "分组列表", notes = "分组列表")
    PurchaseContractConfigGroupListDto groupList();
}
