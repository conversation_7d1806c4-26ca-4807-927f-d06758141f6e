package com.nsy.api.scm.dto.request.recommendmaterial;

import com.nsy.api.core.apicore.base.UserInfo;
import com.nsy.api.scm.dto.constant.ProductRelationshipTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.logging.log4j.util.Strings;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 新增开款推荐面料任务请求体
 *
 * <AUTHOR>
 * @since 2023-08-14 11:35
 */
@ApiModel(value = "RecommendMaterialFlowTaskInitializeRequest", description = "新增开款推荐面料任务请求体")
public class RecommendMaterialFlowTaskInitializeRequest {

    @ApiModelProperty(value = "商品ID", name = "productId")
    @NotNull(message = "商品ID不能为空")
    private Integer productId;

    @ApiModelProperty(value = "商品编码", name = "spu")
    @NotBlank(message = "商品编码不能为空")
    private String spu;

    @ApiModelProperty(value = "推荐部门", name = "recommendPlatforms")
    private String recommendPlatforms;

    @ApiModelProperty(value = "开款部门", name = "developDepartment")
    private String developDepartment;

    @ApiModelProperty(value = "任务类型：1-新款 2-确版驳回 3-报核价驳回 4-新款加色 5-旧款加色 6-面料信息补录", name = "taskType")
    private Integer taskType;

    @ApiModelProperty(value = "商品是否包含售前-自主设计标签", name = "includeAutonomousDesignProductLabel")
    private Boolean includeAutonomousDesignProductLabel;

    @ApiModelProperty(value = "颜色编码集合", name = "skcList")
    private List<String> skcList;

    @ApiModelProperty(value = "印花颜色编码集合", name = "skcPrintFlowerList")
    private List<String> skcPrintFlowerList;

    @ApiModelProperty(value = "skc分组信息集合", name = "skcList")
    private List<RecommendMaterialSkcGroupInfoRequest> skcGroupInfoList;

    @ApiModelProperty(value = "驳回原因", name = "rejectReason")
    private String rejectReason;
    @ApiModelProperty(value = "回退原因", name = "rejectReason")
    private String rollbackReason;

    @ApiModelProperty(value = "创建人", name = "createBy")
    private String createBy;

    @ApiModelProperty(value = "商品分类id", name = "productCategoryId")
    @NotNull(message = "商品分类id不能为空")
    private Integer productCategoryId;

    @ApiModelProperty(value = "商品分类名称", name = "productCategoryName")
    private String productCategoryName;

    @ApiModelProperty(value = "商品图片地址", name = "productImageUrl")
    private String productImageUrl;

    @ApiModelProperty(value = "工艺分类", name = "processClassification")
    private String processClassification;

    @ApiModelProperty(value = "商品开款操作人信息", name = "userInfo")
    @NotNull(message = "操作人信息不能为空")
    private UserInfo userInfo;

    @ApiModelProperty(value = "旧商品id", name = "oldProductId")
    private Integer oldProductId;

    @ApiModelProperty(value = "旧商品spu", name = "oldSpu")
    private String oldSpu;

    @ApiModelProperty(value = "和旧商品的关联关系", name = "relationshipType")
    private String relationshipType;

    @ApiModelProperty(value = "SKC推荐面料信息", name = "skcRecommendMaterialMap")
    private Map<String, List<ScmProductDevelopRecommendMaterial>> skcRecommendMaterialMap;

    @ApiModelProperty(value = "加色需求id", name = "productAddColorRequireId")
    private Integer productAddColorRequireId;

    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    @ApiModelProperty(value = " 判断是否已有任务 1:存在直接结束", name = "checkTaskExist")
    private Integer checkTaskExist;

    @ApiModelProperty(value = "签收面料信息列表", name = "receiveMaterialInfoList")
    private List<ReceiveMaterialInfoDto> receiveMaterialInfoList;

    private Integer taskId;

    public boolean majorRevisionType() {
        return Strings.isNotBlank(relationshipType) && ProductRelationshipTypeEnum.MAJOR_REVISION.name().equals(relationshipType);
    }

    public Integer getProductCategoryId() {
        return productCategoryId;
    }

    public void setProductCategoryId(Integer productCategoryId) {
        this.productCategoryId = productCategoryId;
    }

    public String getProductCategoryName() {
        if (productCategoryName == null) {
            return "";
        }
        return productCategoryName;
    }

    public void setProductCategoryName(String productCategoryName) {
        this.productCategoryName = productCategoryName;
    }

    public String getProductImageUrl() {
        if (productImageUrl == null) {
            return "";
        }
        return productImageUrl;
    }

    public void setProductImageUrl(String productImageUrl) {
        this.productImageUrl = productImageUrl;
    }

    public UserInfo getUserInfo() {
        return userInfo;
    }

    public Integer getUserId() {
        if (userInfo == null || userInfo.getUserId() == null) {
            return 0;
        }
        return userInfo.getUserId();
    }

    public void setUserInfo(UserInfo userInfo) {
        this.userInfo = userInfo;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getRecommendPlatforms() {
        return recommendPlatforms;
    }

    public void setRecommendPlatforms(String recommendPlatforms) {
        this.recommendPlatforms = recommendPlatforms;
    }

    public String getDevelopDepartment() {
        return developDepartment;
    }

    public void setDevelopDepartment(String developDepartment) {
        this.developDepartment = developDepartment;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public Boolean getIncludeAutonomousDesignProductLabel() {
        return includeAutonomousDesignProductLabel;
    }

    public void setIncludeAutonomousDesignProductLabel(Boolean includeAutonomousDesignProductLabel) {
        this.includeAutonomousDesignProductLabel = includeAutonomousDesignProductLabel;
    }

    public List<String> getSkcList() {
        return skcList;
    }

    public void setSkcList(List<String> skcList) {
        this.skcList = skcList;
    }

    public List<String> getSkcPrintFlowerList() {
        return skcPrintFlowerList;
    }

    public void setSkcPrintFlowerList(List<String> skcPrintFlowerList) {
        this.skcPrintFlowerList = skcPrintFlowerList;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getProcessClassification() {
        return processClassification;
    }

    public void setProcessClassification(String processClassification) {
        this.processClassification = processClassification;
    }

    public Integer getOldProductId() {
        return oldProductId;
    }

    public void setOldProductId(Integer oldProductId) {
        this.oldProductId = oldProductId;
    }

    public String getOldSpu() {
        return oldSpu;
    }

    public void setOldSpu(String oldSpu) {
        this.oldSpu = oldSpu;
    }

    public Map<String, List<ScmProductDevelopRecommendMaterial>> getSkcRecommendMaterialMap() {
        if (skcRecommendMaterialMap == null) {
            return Collections.emptyMap();
        }
        return skcRecommendMaterialMap;
    }

    public void setSkcRecommendMaterialMap(Map<String, List<ScmProductDevelopRecommendMaterial>> skcRecommendMaterialMap) {
        this.skcRecommendMaterialMap = skcRecommendMaterialMap;
    }

    public String getRelationshipType() {
        return relationshipType;
    }

    public void setRelationshipType(String relationshipType) {
        this.relationshipType = relationshipType;
    }


    public Integer getProductAddColorRequireId() {
        return productAddColorRequireId;
    }

    public void setProductAddColorRequireId(Integer productAddColorRequireId) {
        this.productAddColorRequireId = productAddColorRequireId;
    }

    public String getRollbackReason() {
        return rollbackReason;
    }

    public void setRollbackReason(String rollbackReason) {
        this.rollbackReason = rollbackReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public List<RecommendMaterialSkcGroupInfoRequest> getSkcGroupInfoList() {
        return skcGroupInfoList;
    }

    public void setSkcGroupInfoList(List<RecommendMaterialSkcGroupInfoRequest> skcGroupInfoList) {
        this.skcGroupInfoList = skcGroupInfoList;
    }

    public Integer getCheckTaskExist() {
        return checkTaskExist;
    }

    public void setCheckTaskExist(Integer checkTaskExist) {
        this.checkTaskExist = checkTaskExist;
    }

    public boolean isCheckTaskExist() {
        return Objects.equals(1, checkTaskExist);
    }

    public List<ReceiveMaterialInfoDto> getReceiveMaterialInfoList() {
        return receiveMaterialInfoList;
    }

    public void setReceiveMaterialInfoList(List<ReceiveMaterialInfoDto> receiveMaterialInfoList) {
        this.receiveMaterialInfoList = receiveMaterialInfoList;
    }
}
